#!/usr/bin/env python3
"""
Test script to verify audio-processor to backend translation connection
"""

import asyncio
import sys
import os

# Add audio-processor src to path
sys.path.insert(0, '/home/<USER>/website_project/packages/services/audio-processor/src')

from core.config import Settings
from infrastructure.qwen_service_client_impl import QwenServiceClientImpl
from models.services.semantic_validation_request import SemanticValidationRequest


async def test_translation_connection():
    """Test if audio-processor can connect to backend for translation"""
    
    print("🔧 Initializing audio-processor settings...")
    settings = Settings()
    print(f"✅ Backend URL: {settings.QWEN_SERVICE_API_URL}")
    print(f"✅ Timeout: {settings.QWEN_SERVICE_TIMEOUT}s")
    
    print("\n🔧 Creating qwen service client...")
    qwen_client = QwenServiceClientImpl(settings)
    
    print("\n🔧 Creating test translation request...")
    test_request = SemanticValidationRequest(
        prompt="Translate this Arabic text to Chinese: مرحبا",
        system_prompt="You are a professional Arabic to Chinese translator. Provide accurate and natural translations.",
        enable_thinking=True
    )
    
    print("\n🚀 Testing connection to backend service...")
    try:
        response = await qwen_client.validate_semantically(test_request)
        print("✅ SUCCESS: Connection to backend service working!")
        print(f"✅ Response success: {response.success}")
        print(f"✅ Response message: {response.message}")
        print(f"✅ Generated text: {response.generated_text[:100]}...")
        print(f"✅ Processing time: {response.processing_time}s")
        return True
        
    except Exception as e:
        print(f"❌ FAILED: Connection to backend service failed!")
        print(f"❌ Error: {e}")
        return False


if __name__ == "__main__":
    print("🧪 Testing Audio-Processor to Backend Translation Connection")
    print("=" * 60)
    
    success = asyncio.run(test_translation_connection())
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 CONCLUSION: Translation functionality should work!")
        print("   The audio-processor can successfully connect to backend service.")
    else:
        print("💥 CONCLUSION: Translation functionality is broken!")
        print("   The audio-processor cannot connect to backend service.")
    
    sys.exit(0 if success else 1)
