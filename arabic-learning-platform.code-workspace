{
  "folders": [
    {
      "name": "🏠 Project Root",
      "path": "."
    },
    {
      "name": "🎵 Audio Processor",
      "path": "./packages/services/audio-processor",
      "settings": {
        "python.analysis.extraPaths": ["./src"],
        "python.defaultInterpreterPath": "./.venv/bin/python"
      }
    },

    {
      "name": "🖥️ Frontend",
      "path": "./packages/frontend"
    },
    {
      "name": "🔧 Backend",
      "path": "./packages/backend",
      "settings": {
        "python.analysis.extraPaths": ["./app"],
        "python.defaultInterpreterPath": "./.venv/bin/python"
      }
    },

    {
      "name": "🐪 Camel Tools API",
      "path": "./packages/services/camel-tools-api",
      "settings": {
        "python.analysis.extraPaths": ["./src"],
        "python.defaultInterpreterPath": "./.venv/bin/python"
      }
    },
    {
      "name": "🤖 Qwen Service",
      "path": "./packages/services/qwen-service",
      "settings": {
        "python.analysis.extraPaths": ["./src"],
        "python.defaultInterpreterPath": "./.venv/bin/python"
      }
    }
  ],
  "settings": {
    // 全局设置
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "editor.formatOnSave": true,
    // Note: editor.codeActionsOnSave moved to service-specific settings
    // because different services need different code actions:
    // - Python services: source.fixAll.ruff
    // - Frontend: source.fixAll.eslint
    // This prevents conflicts between different toolchains

    // 文件关联
    "files.associations": {
      "*.toml": "toml"
    },

    // 搜索和文件排除
    "search.exclude": {
      "**/node_modules": true,
      "**/venv": true,
      "**/.venv": true,
      "**/dist": true,
      "**/.next": true,
      "**/__pycache__": true,
      "**/*.pyc": true,
      "**/models/whisper": true
    },
    "files.exclude": {
      "**/__pycache__": true,
      "**/*.pyc": true,
      "**/node_modules": true,
      "**/.next": true
    },



    // ===== IMPORTANT: NO PYTHON-SPECIFIC SETTINGS HERE =====
    // All Python settings moved to individual service .vscode/settings.json
    // to prevent workspace-level settings from overriding service-specific configurations
    // This ensures each microservice can have independent Python/Ruff configurations

    // TypeScript 设置
    "typescript.preferences.importModuleSpecifier": "relative",
    "typescript.suggest.autoImports": true,

    // Web 开发设置
    "emmet.includeLanguages": {
      "typescript": "html",
      "typescriptreact": "html"
    },
    "tailwindCSS.includeLanguages": {
      "typescript": "html",
      "typescriptreact": "html"
    }
  },
  "extensions": {
    "recommendations": [
      // ===== PYTHON DEVELOPMENT (Ruff-based Modern Stack) =====
      "ms-python.python",                    // Core Python support
      "charliermarsh.ruff",                  // ⭐ Modern linter/formatter
      "ms-python.mypy-type-checker",         // Type checking

      // ===== WEB DEVELOPMENT =====
      "ms-vscode.vscode-typescript-next",    // TypeScript support
      "bradlc.vscode-tailwindcss",           // Tailwind CSS IntelliSense
      "esbenp.prettier-vscode",              // Web formatting
      "dbaeumer.vscode-eslint",              // JavaScript/TypeScript linting

      // ===== PRODUCTIVITY =====
      "formulahendry.auto-rename-tag",       // Auto rename paired tags
      "christian-kohler.path-intellisense",  // Path autocomplete

      // ===== CONFIGURATION FILES =====
      "ms-vscode.vscode-json",               // JSON support
      "redhat.vscode-yaml",                  // YAML support
      "tamasfe.even-better-toml",            // TOML support

      // ===== REMOTE DEVELOPMENT =====
      "ms-vscode-remote.remote-containers",  // Docker development
      "ms-vscode-remote.remote-ssh"          // SSH remote development

      // ===== REMOVED (Replaced by Ruff) =====
      // "ms-python.black-formatter",        // ❌ Replaced by Ruff
      // "ms-python.flake8",                 // ❌ Replaced by Ruff
      // "ms-python.isort",                  // ❌ Replaced by Ruff
      // "ms-python.pylint",                 // ❌ Replaced by Ruff (Phase 2)
    ]
  }
}
