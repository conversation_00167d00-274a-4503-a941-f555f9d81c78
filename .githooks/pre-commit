#!/bin/bash

# BAYAN阿拉伯语学习平台 - Git Pre-commit Hook
# 在提交前执行代码质量检查

set -e

echo "🔍 执行提交前检查..."

# 检查是否有暂存的文件
if git diff --cached --quiet; then
    echo "❌ 没有暂存的文件可以提交"
    exit 1
fi

# 检查提交信息格式（如果有的话）
if [ -f .git/COMMIT_EDITMSG ]; then
    commit_msg=$(cat .git/COMMIT_EDITMSG)
    if ! echo "$commit_msg" | grep -qE "^(feat|fix|docs|style|refactor|test|chore|perf|ci|build)(\(.+\))?: .{1,50}"; then
        echo "❌ 提交信息格式不正确"
        echo "格式应为: type(scope): description"
        echo "类型: feat, fix, docs, style, refactor, test, chore, perf, ci, build"
        exit 1
    fi
fi

# 检查Python代码格式（如果有Python文件被修改）
python_files=$(git diff --cached --name-only --diff-filter=ACM | grep '\.py$' || true)
if [ -n "$python_files" ]; then
    echo "🐍 检查Python代码..."
    
    # 检查Python语法
    for file in $python_files; do
        if [ -f "$file" ]; then
            PYTHONDONTWRITEBYTECODE=1 python -m py_compile "$file" || {
                echo "❌ Python语法错误: $file"
                exit 1
            }
        fi
    done
    
    echo "✅ Python代码检查通过"
fi

# 检查TypeScript/JavaScript代码（如果有相关文件被修改）
js_ts_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(js|ts|tsx|jsx)$' || true)
if [ -n "$js_ts_files" ]; then
    echo "📝 检查TypeScript/JavaScript代码..."
    
    # 如果项目中有package.json，尝试运行lint
    if [ -f "package.json" ]; then
        if npm list eslint > /dev/null 2>&1; then
            echo "运行ESLint检查..."
            npx eslint $js_ts_files || {
                echo "❌ ESLint检查失败"
                exit 1
            }
        fi
    fi
    
    echo "✅ TypeScript/JavaScript代码检查通过"
fi

# Docker检查已移除 - 项目已迁移到本地开发模式

# 检查敏感信息
echo "🔒 检查敏感信息..."
sensitive_patterns=(
    "password\s*=\s*['\"][^'\"]*['\"]"
    "secret\s*=\s*['\"][^'\"]*['\"]"
    "api_key\s*=\s*['\"][^'\"]*['\"]"
    "token\s*=\s*['\"][^'\"]*['\"]"
    "-----BEGIN PRIVATE KEY-----"
    "-----BEGIN RSA PRIVATE KEY-----"
)

for pattern in "${sensitive_patterns[@]}"; do
    # Only check added lines (starting with +), not deleted lines (starting with -)
    if git diff --cached | grep "^+" | grep -iE "$pattern" >/dev/null 2>&1; then
        echo "❌ 检测到可能的敏感信息，请检查并移除"
        exit 1
    fi
done

echo "✅ 敏感信息检查通过"

# 检查文件大小
echo "📏 检查文件大小..."
large_files=$(git diff --cached --name-only --diff-filter=ACM | xargs -I {} find {} -size +10M 2>/dev/null || true)
if [ -n "$large_files" ]; then
    echo "❌ 发现大文件（>10MB），请考虑使用Git LFS:"
    echo "$large_files"
    exit 1
fi

echo "✅ 所有检查通过！"
echo "🚀 准备提交..."
