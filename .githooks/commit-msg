#!/bin/bash

# BAYAN阿拉伯语学习平台 - Git Commit Message Hook
# 检查提交信息格式

commit_msg_file=$1
commit_msg=$(cat "$commit_msg_file")

# 提交信息格式规范
# type(scope): description
# 
# type: feat, fix, docs, style, refactor, test, chore, perf, ci, build
# scope: 可选，表示影响的范围
# description: 简短描述，不超过50个字符

echo "🔍 检查提交信息格式..."

# 检查提交信息是否为空
if [ -z "$commit_msg" ] || [ "$commit_msg" = "$(echo -e '\n')" ]; then
    echo "❌ 提交信息不能为空"
    exit 1
fi

# 检查提交信息格式
if ! echo "$commit_msg" | head -n1 | grep -qE "^(feat|fix|docs|style|refactor|test|chore|perf|ci|build)(\(.+\))?: .{1,50}"; then
    echo "❌ 提交信息格式不正确"
    echo ""
    echo "正确格式: type(scope): description"
    echo ""
    echo "可用类型:"
    echo "  feat:     新功能"
    echo "  fix:      修复bug"
    echo "  docs:     文档更新"
    echo "  style:    代码格式调整（不影响功能）"
    echo "  refactor: 代码重构"
    echo "  test:     测试相关"
    echo "  chore:    构建过程或辅助工具的变动"
    echo "  perf:     性能优化"
    echo "  ci:       CI/CD相关"
    echo "  build:    构建系统相关"
    echo ""
    echo "示例:"
    echo "  feat(audio): 添加语音识别功能"
    echo "  fix(backend): 修复用户认证问题"
    echo "  docs: 更新API文档"
    echo ""
    exit 1
fi

# 检查描述长度
description=$(echo "$commit_msg" | head -n1 | sed 's/^[^:]*: //')
if [ ${#description} -gt 50 ]; then
    echo "❌ 提交描述过长（${#description}字符），请控制在50字符以内"
    exit 1
fi

# 检查是否有正文，如果有，检查格式
body_lines=$(echo "$commit_msg" | tail -n +3)
if [ -n "$body_lines" ]; then
    # 检查正文每行长度
    while IFS= read -r line; do
        if [ ${#line} -gt 72 ]; then
            echo "❌ 提交正文行过长（${#line}字符），请控制在72字符以内"
            echo "问题行: $line"
            exit 1
        fi
    done <<< "$body_lines"
fi

echo "✅ 提交信息格式正确"
