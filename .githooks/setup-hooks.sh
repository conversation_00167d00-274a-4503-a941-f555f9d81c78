#!/bin/bash

# BAYAN阿拉伯语学习平台 - Git钩子安装脚本

echo "🔧 设置Git钩子..."

# 获取项目根目录
PROJECT_ROOT=$(git rev-parse --show-toplevel)
HOOKS_DIR="$PROJECT_ROOT/.githooks"
GIT_HOOKS_DIR="$PROJECT_ROOT/.git/hooks"

# 检查.githooks目录是否存在
if [ ! -d "$HOOKS_DIR" ]; then
    echo "❌ .githooks目录不存在"
    exit 1
fi

# 创建.git/hooks目录（如果不存在）
mkdir -p "$GIT_HOOKS_DIR"

# 安装钩子
hooks=("pre-commit" "commit-msg")

for hook in "${hooks[@]}"; do
    if [ -f "$HOOKS_DIR/$hook" ]; then
        echo "📋 安装 $hook 钩子..."
        cp "$HOOKS_DIR/$hook" "$GIT_HOOKS_DIR/$hook"
        chmod +x "$GIT_HOOKS_DIR/$hook"
        echo "✅ $hook 钩子安装完成"
    else
        echo "⚠️  $hook 钩子文件不存在，跳过"
    fi
done

echo ""
echo "🎉 Git钩子设置完成！"
echo ""
echo "已安装的钩子:"
for hook in "${hooks[@]}"; do
    if [ -f "$GIT_HOOKS_DIR/$hook" ]; then
        echo "  ✅ $hook"
    fi
done

echo ""
echo "📝 提交信息格式规范:"
echo "  type(scope): description"
echo ""
echo "  可用类型: feat, fix, docs, style, refactor, test, chore, perf, ci, build"
echo "  示例: feat(audio): 添加语音识别功能"
