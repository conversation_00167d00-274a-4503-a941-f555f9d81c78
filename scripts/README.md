# Universal File Organization Validation Script

## Overview

The `validate_file_organization.py` script is a universal validation tool designed to ensure Python files across all modules in the project comply with the **Clean Architecture + Service Factory pattern** file organization rules.

## Features

### ✅ **Universal Compatibility**
- **Auto-detection**: Automatically detects service name and project structure
- **Multi-module support**: Works with backend, microservices, and any Python module
- **Flexible directory structure**: Supports both `src/` and `app/` directory patterns
- **Path intelligence**: Handles different project layouts automatically

### 🔍 **Comprehensive Validation**
- **One class per file rule** (with exceptions for related config/data classes)
- **Snake_case file naming** matching class names
- **Proper class grouping** (configuration, data classes, protocols)
- **Import error detection** and syntax validation

### 📊 **Rich Reporting**
- **Dynamic service names** in report headers
- **Detailed statistics** with compliance rates
- **Violation categorization** by type
- **Actionable fix suggestions** for each violation
- **Compliant files listing** for verification

### 🔧 **Automated Fix Generation**
- **Shell commands** for filename violations
- **Safe file renaming** suggestions
- **Batch operation support** for multiple fixes

## Usage

### Basic Usage

```bash
# Validate audio-processor service
python scripts/validate_file_organization.py packages/services/audio-processor/

# Validate backend module
python scripts/validate_file_organization.py packages/backend/

# Validate qwen-service
python scripts/validate_file_organization.py packages/services/qwen-service/
```

### Advanced Options

```bash
# Show detailed fix suggestions
python scripts/validate_file_organization.py packages/backend/ --fix-suggestions

# Generate shell commands for automatic fixes
python scripts/validate_file_organization.py packages/services/qwen-service/ --generate-fix-commands

# Save report to file
python scripts/validate_file_organization.py packages/backend/ --output-file backend_report.txt

# Override auto-detected service name
python scripts/validate_file_organization.py packages/services/custom-service/ --service-name "Custom Service"

# Combine multiple options
python scripts/validate_file_organization.py packages/backend/ --fix-suggestions --generate-fix-commands --output-file full_report.txt
```

## Supported Project Structures

### Microservices Pattern
```
packages/services/service-name/
├── src/                    # Primary source directory
│   ├── interfaces/         # Abstract interfaces
│   ├── services/          # Service implementations
│   ├── models/            # Data models
│   ├── adapters/          # Adapters layer
│   ├── infrastructure/    # Infrastructure layer
│   └── api/               # API layer
```

### Backend Pattern
```
packages/backend/
├── app/                   # Primary source directory
│   ├── api/               # API routes
│   ├── core/              # Core configuration
│   ├── services/          # Service modules
│   ├── shared/            # Shared components
│   └── infrastructure/    # Infrastructure layer
```

## Validation Rules

### 1. One Class Per File
- **Rule**: Each Python file should contain exactly one primary class
- **Exceptions**: 
  - Multiple configuration classes in one file
  - Multiple data classes in one file
  - Multiple protocol/interface classes in one file
- **Violation**: Files with mixed class types or multiple business logic classes

### 2. Snake_case File Naming
- **Rule**: File names must match the primary class name in snake_case
- **Example**: `AudioProcessingService` → `audio_processing_service.py`
- **Violation**: Mismatched file and class names

### 3. Proper Class Grouping
- **Configuration classes**: Settings, Config, Configuration, Options
- **Data classes**: BaseModel, dataclass, DTO, Request, Response, Result
- **Protocol classes**: Protocol, Interface, runtime_checkable

## Auto-Detection Logic

### Service Name Detection
1. **Backend modules**: Detects "Backend" from path containing "backend"
2. **Microservices**: Extracts service name from `packages/services/service-name/`
3. **Custom override**: Use `--service-name` parameter for manual specification

### Source Directory Discovery
1. **Primary attempt**: Look for `src/` directory
2. **Secondary attempt**: Look for `app/` directory  
3. **Fallback**: Use target directory itself

## Report Format

### Summary Statistics
- Target and source directories
- Total files checked
- Compliant vs. violation counts
- Overall compliance percentage

### Violation Details
- File path (relative to target)
- Violation type and description
- Classes found with line numbers
- Specific fix suggestions

### Fix Commands
- Ready-to-execute shell commands
- Safe file renaming operations
- Batch processing support

## Integration Examples

### CI/CD Pipeline
```bash
# Validate all services in CI
python scripts/validate_file_organization.py packages/services/audio-processor/
python scripts/validate_file_organization.py packages/services/qwen-service/
python scripts/validate_file_organization.py packages/services/camel-tools-api/
python scripts/validate_file_organization.py packages/backend/
```

### Pre-commit Hook
```bash
#!/bin/bash
# Validate changed Python modules
python scripts/validate_file_organization.py packages/backend/ --fix-suggestions
```

### Development Workflow
```bash
# Quick validation during development
python scripts/validate_file_organization.py packages/services/audio-processor/ --generate-fix-commands
```

## Exit Codes

- **0**: All files compliant, no violations found
- **1**: Violations found or validation errors occurred

## Configuration

The script uses intelligent defaults but supports customization:

- **Service patterns**: Auto-detects Clean Architecture + Service Factory patterns
- **Exclude patterns**: Automatically excludes `__pycache__`, `.pytest_cache`, `.venv`, `node_modules`
- **Class type patterns**: Configurable patterns for config, data, and protocol classes

## Migration from Service-Specific Scripts

The universal script is fully backward compatible with existing service-specific validation scripts. Simply replace:

```bash
# Old: Service-specific script
cd packages/services/audio-processor/
python validate_file_organization.py

# New: Universal script
python scripts/validate_file_organization.py packages/services/audio-processor/
```

## Troubleshooting

### Common Issues

1. **"Directory does not exist"**: Verify the target path is correct
2. **"No Python files found"**: Check if the source directory detection is correct
3. **"Import errors"**: Files with syntax errors will be reported as violations

### Debug Tips

- Use `--service-name` to override auto-detection
- Check the "Source directory" in the report output
- Verify file permissions for the target directory

## Contributing

When adding new validation rules or enhancing the script:

1. Maintain backward compatibility
2. Update the configuration patterns as needed
3. Add appropriate test cases
4. Update this documentation

## Related Files

- **Original script**: `packages/services/audio-processor/validate_file_organization.py`
- **Project architecture**: `docs/architecture.md`
- **Setup scripts**: `scripts/setup-python-service.sh`
