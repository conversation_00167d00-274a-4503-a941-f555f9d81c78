#!/usr/bin/env python3
"""
Universal File Organization Validation Script

This script validates that all Python files in any module comply with the
established Clean Architecture + Service Factory pattern file organization rules:

1. One class per file rule (with exceptions for related config/data classes)
2. Snake_case file naming matching class names
3. Proper grouping of configuration and data classes
4. Correct import statements

Usage:
    python validate_file_organization.py <target_directory> [options]
    
Examples:
    python validate_file_organization.py packages/services/audio-processor/
    python validate_file_organization.py packages/backend/
    python validate_file_organization.py packages/services/qwen-service/ --fix-suggestions
"""

import argparse
import ast
import re
import sys
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Optional


class ViolationType(Enum):
    """Types of file organization violations"""

    MULTIPLE_CLASSES = "multiple_classes"
    FILENAME_MISMATCH = "filename_mismatch"
    IMPROPER_GROUPING = "improper_grouping"
    IMPORT_ERROR = "import_error"


@dataclass
class ProjectConfig:
    """Configuration for different project types and structures"""

    service_name: str
    source_directories: list[str] = field(default_factory=lambda: ["src", "app"])
    config_patterns: set[str] = field(
        default_factory=lambda: {"Settings", "Config", "Configuration", "Options"}
    )
    data_class_patterns: set[str] = field(
        default_factory=lambda: {"BaseModel", "dataclass", "NamedTuple", "TypedDict"}
    )
    protocol_patterns: set[str] = field(
        default_factory=lambda: {"Protocol", "runtime_checkable"}
    )
    exclude_patterns: set[str] = field(
        default_factory=lambda: {
            "__pycache__",
            ".pytest_cache",
            ".venv",
            "node_modules",
        }
    )


@dataclass
class ClassInfo:
    """Information about a class found in a file"""

    name: str
    line_number: int
    is_config: bool = False
    is_data_class: bool = False
    is_protocol: bool = False
    is_enum: bool = False
    base_classes: Optional[list[str]] = None

    def __post_init__(self) -> None:
        if self.base_classes is None:
            self.base_classes = []


@dataclass
class FileViolation:
    """Represents a file organization violation"""

    file_path: Path
    violation_type: ViolationType
    description: str
    suggested_fix: str
    classes_found: Optional[list[ClassInfo]] = None

    def __post_init__(self) -> None:
        if self.classes_found is None:
            self.classes_found = []


class ServiceDetector:
    """Detects service information from directory paths"""

    @staticmethod
    def detect_service_info(target_path: Path) -> ProjectConfig:
        """Detect service name and configuration from target directory path"""

        # Convert to absolute path and resolve
        abs_path = target_path.resolve()
        path_parts = abs_path.parts

        # Try to detect service name from path
        service_name = "Unknown Service"
        source_dirs = ["src", "app"]

        # Check if it's a backend module
        if "backend" in path_parts:
            service_name = "Backend"
            source_dirs = ["app", "src"]

        # Check if it's a microservice
        elif "services" in path_parts:
            try:
                services_idx = path_parts.index("services")
                if services_idx + 1 < len(path_parts):
                    service_name = (
                        path_parts[services_idx + 1].replace("-", " ").title()
                    )
            except (ValueError, IndexError):
                pass

        # Check if target path itself contains source directory
        if abs_path.name in ["src", "app"]:
            # If targeting src/app directly, get parent service name
            parent_parts = abs_path.parent.parts
            if "services" in parent_parts:
                try:
                    services_idx = parent_parts.index("services")
                    if services_idx + 1 < len(parent_parts):
                        service_name = (
                            parent_parts[services_idx + 1].replace("-", " ").title()
                        )
                except (ValueError, IndexError):
                    pass
            elif "backend" in parent_parts:
                service_name = "Backend"

        return ProjectConfig(service_name=service_name, source_directories=source_dirs)

    @staticmethod
    def find_source_directory(target_path: Path, config: ProjectConfig) -> Path:
        """Find the actual source directory to validate"""

        abs_path = target_path.resolve()

        # If target is already a source directory, use it
        if abs_path.name in config.source_directories:
            return abs_path

        # Try to find source directory within target
        for src_dir in config.source_directories:
            candidate = abs_path / src_dir
            if candidate.exists() and candidate.is_dir():
                return candidate

        # If no specific source directory found, use target itself
        return abs_path


class FileOrganizationValidator:
    """Validates Python file organization rules"""

    def __init__(self, target_directory: Path, config: Optional[ProjectConfig] = None):
        if config is None:
            config = ServiceDetector.detect_service_info(target_directory)

        self.config = config
        self.source_directory = ServiceDetector.find_source_directory(
            target_directory, config
        )
        self.base_directory = target_directory.resolve()
        self.violations: list[FileViolation] = []
        self.compliant_files: list[Path] = []
        self.total_files = 0

    def snake_case_to_pascal_case(self, snake_str: str) -> str:
        """Convert snake_case to PascalCase"""
        components = snake_str.split("_")
        return "".join(word.capitalize() for word in components)

    def pascal_case_to_snake_case(self, pascal_str: str) -> str:
        """Convert PascalCase to snake_case"""
        # Insert underscore before uppercase letters (except first)
        snake_str = re.sub("([a-z0-9])([A-Z])", r"\1_\2", pascal_str)
        return snake_str.lower()

    def is_config_class(self, class_info: ClassInfo) -> bool:
        """Check if a class is a configuration class"""
        base_classes = class_info.base_classes or []
        return (
            any(pattern in class_info.name for pattern in self.config.config_patterns)
            or "BaseSettings" in base_classes
            or class_info.name.endswith("Config")
            or class_info.name.endswith("Settings")
        )

    def is_data_class(self, class_info: ClassInfo) -> bool:
        """Check if a class is a data class"""
        base_classes = class_info.base_classes or []
        return (
            any(pattern in base_classes for pattern in self.config.data_class_patterns)
            or class_info.is_data_class
            or class_info.name.endswith("DTO")
            or class_info.name.endswith("Request")
            or class_info.name.endswith("Response")
            or class_info.name.endswith("Result")
        )

    def is_protocol_class(self, class_info: ClassInfo) -> bool:
        """Check if a class is a protocol/interface"""
        base_classes = class_info.base_classes or []
        return (
            class_info.is_protocol
            or "Protocol" in base_classes
            or class_info.name.endswith("Interface")
            or class_info.name.endswith("Protocol")
        )

    def extract_classes_from_file(self, file_path: Path) -> list[ClassInfo]:
        """Extract class information from a Python file"""
        try:
            with file_path.open(encoding="utf-8") as f:
                content = f.read()

            tree = ast.parse(content)
            classes = []

            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    base_classes = []
                    is_protocol = False
                    is_data_class = False

                    # Check base classes
                    for base in node.bases:
                        if isinstance(base, ast.Name):
                            base_classes.append(base.id)
                        elif isinstance(base, ast.Attribute):
                            base_classes.append(base.attr)

                    # Check decorators
                    for decorator in node.decorator_list:
                        if isinstance(decorator, ast.Name):
                            if decorator.id == "dataclass":
                                is_data_class = True
                            elif decorator.id == "runtime_checkable":
                                is_protocol = True

                    class_info = ClassInfo(
                        name=node.name,
                        line_number=node.lineno,
                        base_classes=base_classes,
                        is_protocol=is_protocol,
                        is_data_class=is_data_class,
                    )

                    # Set additional flags
                    class_info.is_config = self.is_config_class(class_info)
                    class_info.is_data_class = (
                        class_info.is_data_class or self.is_data_class(class_info)
                    )
                    class_info.is_protocol = (
                        class_info.is_protocol or self.is_protocol_class(class_info)
                    )

                    classes.append(class_info)

            return classes

        except Exception as e:
            # Create a violation for files that can't be parsed
            violation = FileViolation(
                file_path=file_path,
                violation_type=ViolationType.IMPORT_ERROR,
                description=f"Failed to parse file: {e}",
                suggested_fix="Check file syntax and encoding",
            )
            self.violations.append(violation)
            return []

    def validate_file(self, file_path: Path) -> bool:
        """Validate a single Python file against organization rules"""
        if file_path.name == "__init__.py":
            # Skip __init__.py files as they have different rules
            return True

        classes = self.extract_classes_from_file(file_path)

        if not classes:
            # Files without classes are acceptable (utility modules, etc.)
            self.compliant_files.append(file_path)
            return True

        violations_found = False

        # Rule 1: Check one class per file (with exceptions)
        if len(classes) > 1:
            # Check if multiple classes are allowed (config/data classes)
            config_data_classes = [c for c in classes if c.is_config or c.is_data_class]
            protocol_classes = [c for c in classes if c.is_protocol]
            regular_classes = [
                c
                for c in classes
                if not (c.is_config or c.is_data_class or c.is_protocol)
            ]

            # Allow multiple config/data classes or multiple protocols, but not mixed
            if len(regular_classes) > 1 or (
                regular_classes and (config_data_classes or protocol_classes)
            ):
                violation = FileViolation(
                    file_path=file_path,
                    violation_type=ViolationType.MULTIPLE_CLASSES,
                    description=(
                        f"File contains {len(classes)} classes: "
                        f"{[c.name for c in classes]}"
                    ),
                    suggested_fix="Split into separate files, one class per file",
                    classes_found=classes,
                )
                self.violations.append(violation)
                violations_found = True

        # Rule 2: Check filename matches primary class name
        if classes:
            primary_class = classes[0]  # Assume first class is primary
            expected_filename = (
                self.pascal_case_to_snake_case(primary_class.name) + ".py"
            )

            if file_path.name != expected_filename:
                violation = FileViolation(
                    file_path=file_path,
                    violation_type=ViolationType.FILENAME_MISMATCH,
                    description=(
                        f"Filename '{file_path.name}' doesn't match "
                        f"class '{primary_class.name}'"
                    ),
                    suggested_fix=f"Rename file to '{expected_filename}'",
                    classes_found=classes,
                )
                self.violations.append(violation)
                violations_found = True

        if not violations_found:
            self.compliant_files.append(file_path)
            return True

        return False

    def validate_directory(self, directory: Path) -> None:
        """Validate all Python files in a directory recursively"""
        python_files = []

        # Collect Python files while respecting exclude patterns
        for file_path in directory.rglob("*.py"):
            # Check if file should be excluded
            should_exclude = False
            for exclude_pattern in self.config.exclude_patterns:
                if exclude_pattern in str(file_path):
                    should_exclude = True
                    break

            if not should_exclude:
                python_files.append(file_path)

        self.total_files = len(python_files)

        for file_path in python_files:
            self.validate_file(file_path)

    def generate_report(self, show_fix_suggestions: bool = True) -> str:
        """Generate a comprehensive validation report"""
        report_lines = []

        # Header with dynamic service name
        header_text = (
            f"🔍 {self.config.service_name.upper()} FILE ORGANIZATION VALIDATION REPORT"
        )
        report_lines.append("=" * len(header_text))
        report_lines.append(header_text)
        report_lines.append("=" * len(header_text))
        report_lines.append("")

        # Summary statistics
        compliance_rate = (
            (len(self.compliant_files) / self.total_files * 100)
            if self.total_files > 0
            else 0
        )
        report_lines.append("📊 SUMMARY STATISTICS")
        report_lines.append("-" * 40)
        report_lines.append(f"Target directory: {self.base_directory}")
        report_lines.append(f"Source directory: {self.source_directory}")
        report_lines.append(f"Total files checked: {self.total_files}")
        report_lines.append(f"Compliant files: {len(self.compliant_files)}")
        report_lines.append(f"Files with violations: {len(self.violations)}")
        report_lines.append(f"Compliance rate: {compliance_rate:.1f}%")
        report_lines.append("")

        # Violations by type
        if self.violations:
            violation_counts: dict[ViolationType, int] = {}
            for violation in self.violations:
                violation_counts[violation.violation_type] = (
                    violation_counts.get(violation.violation_type, 0) + 1
                )

            report_lines.append("📈 VIOLATIONS BY TYPE")
            report_lines.append("-" * 40)
            for violation_type, count in violation_counts.items():
                report_lines.append(f"{violation_type.value}: {count}")
            report_lines.append("")

        # Detailed violations
        if self.violations:
            report_lines.append("❌ FILES WITH VIOLATIONS")
            report_lines.append("-" * 40)

            for violation in self.violations:
                try:
                    relative_path = violation.file_path.relative_to(self.base_directory)
                except ValueError:
                    # If relative path fails, use the full path
                    relative_path = violation.file_path

                report_lines.append(f"📁 {relative_path}")
                report_lines.append(f"   Type: {violation.violation_type.value}")
                report_lines.append(f"   Issue: {violation.description}")

                if violation.classes_found:
                    class_names = [
                        f"{c.name} (line {c.line_number})"
                        for c in violation.classes_found
                    ]
                    report_lines.append(f"   Classes: {', '.join(class_names)}")

                if show_fix_suggestions:
                    report_lines.append(f"   🔧 Fix: {violation.suggested_fix}")

                report_lines.append("")

        # Compliant files
        if self.compliant_files:
            report_lines.append("✅ COMPLIANT FILES")
            report_lines.append("-" * 40)

            for file_path in sorted(self.compliant_files):
                try:
                    relative_path = file_path.relative_to(self.base_directory)
                except ValueError:
                    relative_path = file_path
                report_lines.append(f"📁 {relative_path}")

            report_lines.append("")

        # Recommendations
        report_lines.append("💡 RECOMMENDATIONS")
        report_lines.append("-" * 40)

        if not self.violations:
            report_lines.append(
                "🎉 Excellent! All files comply with the Clean Architecture + Service Factory organization rules."
            )
        else:
            report_lines.append("To achieve full compliance:")
            report_lines.append("1. Address filename mismatches by renaming files")
            report_lines.append(
                "2. Split files with multiple classes into separate files"
            )
            report_lines.append(
                "3. Group related configuration and data classes appropriately"
            )
            report_lines.append("4. Update import statements after making changes")
            report_lines.append(
                "5. Run this validation script regularly to catch violations early"
            )

        report_lines.append("")
        report_lines.append("=" * len(header_text))

        return "\n".join(report_lines)

    def get_fix_commands(self) -> list[str]:
        """Generate shell commands to fix common violations"""
        commands = []

        for violation in self.violations:
            if (
                violation.violation_type == ViolationType.FILENAME_MISMATCH
                and violation.classes_found
            ):
                primary_class = violation.classes_found[0]
                expected_filename = (
                    self.pascal_case_to_snake_case(primary_class.name) + ".py"
                )
                old_path = violation.file_path
                new_path = old_path.parent / expected_filename

                commands.append(f"mv '{old_path}' '{new_path}'")

        return commands


def main() -> int:
    """Main function to run the validation

    Returns:
        int: Exit code (0 for success, 1 for validation failures or errors)
    """
    parser = argparse.ArgumentParser(
        description="Universal Python file organization validator for Clean Architecture + Service Factory pattern",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s packages/services/audio-processor/
  %(prog)s packages/backend/ --fix-suggestions
  %(prog)s packages/services/qwen-service/ --generate-fix-commands
  %(prog)s packages/services/camel-tools-api/src/ --output-file report.txt
        """,
    )

    parser.add_argument(
        "target_directory",
        type=Path,
        help="Target directory to validate (required)",
    )
    parser.add_argument(
        "--fix-suggestions",
        action="store_true",
        help="Show detailed fix suggestions in report",
    )
    parser.add_argument(
        "--generate-fix-commands",
        action="store_true",
        help="Generate shell commands to fix filename violations",
    )
    parser.add_argument(
        "--output-file",
        type=Path,
        help="Save report to file instead of printing to console",
    )
    parser.add_argument(
        "--service-name",
        type=str,
        help="Override auto-detected service name",
    )

    args = parser.parse_args()

    # Validate target directory exists
    if not args.target_directory.exists():
        error_msg = f"❌ Error: Directory '{args.target_directory}' does not exist"
        if args.output_file:
            args.output_file.write_text(error_msg, encoding="utf-8")
        else:
            sys.stdout.write(f"{error_msg}\n")
        return 1

    # Detect or override service configuration
    config = ServiceDetector.detect_service_info(args.target_directory)
    if args.service_name:
        config.service_name = args.service_name

    # Run validation
    validator = FileOrganizationValidator(args.target_directory, config)
    validator.validate_directory(validator.source_directory)

    # Generate report
    report = validator.generate_report(show_fix_suggestions=args.fix_suggestions)

    # Output report
    if args.output_file:
        args.output_file.write_text(report, encoding="utf-8")
        success_msg = f"📄 Report saved to: {args.output_file}"
        sys.stdout.write(f"{success_msg}\n")
    else:
        sys.stdout.write(f"{report}\n")

    # Generate fix commands if requested
    if args.generate_fix_commands:
        fix_commands = validator.get_fix_commands()
        if fix_commands:
            fix_header = "\n🔧 SUGGESTED FIX COMMANDS\n" + "-" * 40 + "\n"
            fix_content = "\n".join(fix_commands)
            fix_output = fix_header + fix_content
        else:
            fix_output = "\n✅ No automatic fixes available"

        if args.output_file:
            # Append fix commands to the existing report file
            with args.output_file.open("a", encoding="utf-8") as f:
                f.write(fix_output)
        else:
            sys.stdout.write(f"{fix_output}\n")

    # Return exit code based on compliance
    return 0 if not validator.violations else 1


if __name__ == "__main__":
    sys.exit(main())
