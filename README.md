# 阿拉伯语学习平台

一个功能全面、交互友好的综合性阿拉伯语学习平台，采用现代化的 Monorepo 架构。

## 🎯 项目愿景

创建一个服务于各个水平阿拉伯语学习者的在线平台，提供：

- 📚 多维度学习内容（阅读、语法、影音、教材）
- 🛠️ AI 驱动的学习工具
- 👥 活跃的学习社区
- 🌍 多语言支持

## 📊 项目状态

### 🎵 音频处理服务

- ✅ **本地开发环境完成** - 支持 GPU 加速
- ✅ **Poetry 环境配置完成** - 依赖管理优化
- ✅ **生产环境就绪** - FastAPI + WhisperX 3.2.0
- ✅ **健康检查正常** - http://localhost:8001/health
- 🔄 **功能开发中** - 音频转录 API 优化

### 🏗️ 其他服务

- 🚧 **前端应用** - Next.js 开发中
- 🚧 **后端 API** - FastAPI 架构设计中
- 🚧 **单词分析器** - Camel-Tools 集成中
- 🚧 **翻译服务** - 微服务架构规划中

## 🏗️ 项目架构

### 技术栈

- **前端**: Next.js 14 (App Router) - 本地开发
- **后端**: FastAPI (Python) - 本地开发
- **数据库**: PostgreSQL (本地安装)
- **缓存**: Redis (本地安装)
- **包管理**: pnpm (前端) + Poetry (Python 依赖管理)
- **开发环境**: 本地 Poetry 虚拟环境
- **AI/ML**: WhisperX 3.2.0 + PyTorch 2.7.1 + CUDA 12.8

### 目录结构

```
arabic-learning-platform/
├── docs/                           # 项目文档
├── packages/
│   ├── frontend/                   # Next.js 前端应用
│   ├── backend/                    # FastAPI 后端应用
│   ├── shared/                     # 共享逻辑包
│   │   └── i18n/                   # 国际化资源共享包
│   ├── services/                   # 独立微服务
│   │   ├── audio-processor/        # 音频处理服务 (WhisperX)
│   │   ├── camel-tools-api/        # Camel-Tools 阿拉伯语处理服务
│   │   └── qwen-service/           # Qwen 大语言模型服务
│   └── types/                      # 共享 TypeScript 类型
├── package.json                    # Monorepo 根配置
└── pnpm-workspace.yaml            # pnpm 工作区配置
```

## 📋 Git 版本控制

本项目使用 Git 进行版本控制，并托管在 GitHub 私有仓库中。

### 仓库信息

- **仓库名称**: BAYAN-ARABIC-LEARNING-PLATFORM
- **仓库类型**: 私有仓库
- **主分支**: main
- **开发分支**: develop

### Git 工作流程

请参阅 [Git 工作流程指南](docs/git-workflow-guide.md) 了解详细的分支策略和提交规范。

#### 快速开始

```bash
# 克隆仓库
<NAME_EMAIL>:zmz231126/BAYAN-ARABIC-LEARNING-PLATFORM.git
cd BAYAN-ARABIC-LEARNING-PLATFORM

# 安装Git钩子
./.githooks/setup-hooks.sh

# 创建功能分支
git checkout develop
git checkout -b feature/your-feature-name
```

#### 提交规范

使用约定式提交格式：

```
type(scope): description

示例:
feat(audio): 添加语音识别功能
fix(backend): 修复用户认证问题
docs: 更新API文档
```

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0
- Python >= 3.9
- Poetry (Python 依赖管理)
- PostgreSQL (本地安装)
- Redis (本地安装)
- CUDA 12.8 (可选，用于 GPU 加速)
- FFmpeg (音频处理必需)

### 自动环境设置（推荐）

```bash
chmod +x scripts/setup-dev-env.sh
./scripts/setup-dev-env.sh
```

### 手动安装依赖

```bash
# 1. 安装 Node.js 依赖
pnpm install

# 2. 设置 Python 服务环境
./scripts/setup-poetry-envs.sh
```

### 本地开发模式

```bash
# 启动前端 + 后端（本地）
pnpm dev

# 启动所有微服务（本地）
pnpm services:dev

# 单独启动音频处理服务
pnpm audio-processor:dev

# 进入服务环境进行开发
pnpm audio-processor:shell

# 查看服务状态
ps aux | grep python
```

### 依赖管理

```bash
# 添加新依赖
./scripts/add-dependency.sh audio-processor add numpy

# 移除依赖
./scripts/add-dependency.sh audio-processor remove old-package

# 更新依赖
./scripts/add-dependency.sh audio-processor update
```

### 测试安装

```bash
# 测试音频处理服务（本地）
cd packages/services/audio-processor
poetry run python src/main.py &
curl --noproxy localhost http://localhost:8001/health

# 测试音频处理模型
poetry run python test_model.py
```

## 🚀 本地部署

### 本地服务部署

#### 音频处理服务

```bash
# 进入音频服务目录
cd packages/services/audio-processor

# 启动服务
poetry run python src/main.py

# 健康检查
curl --noproxy localhost http://localhost:8001/health
```

#### 服务管理

```bash
# 启动所有服务
pnpm services:dev

# 停止服务 (Ctrl+C)

# 重启服务
pnpm services:dev

# 查看服务状态
ps aux | grep python
```

### 生产环境部署

对于生产环境，建议：

- 使用 systemd 服务管理
- 配置 Nginx 反向代理
- 设置日志轮转
- 配置监控和告警

## 📦 核心服务

### 🎵 音频处理服务 (audio-processor)

基于 WhisperX 的音频转录服务，支持：

- 多语言音频转录
- VAD (语音活动检测)
- 时间戳对齐
- 说话人分离
- GPU 加速 (CUDA 12.8)

**位置**: `packages/services/audio-processor/`
**模型**: faster-whisper-large-v3-turbo-ct2
**技术栈**: FastAPI + WhisperX 3.2.0 + PyTorch 2.7.1+cu128
**部署**: 本地 Poetry 环境
**端口**: http://localhost:8001

#### 快速启动

```bash
cd packages/services/audio-processor
poetry run python src/main.py
curl --noproxy localhost http://localhost:8001/health
```

#### 功能状态

- ✅ 本地环境配置完成
- ✅ GPU 支持正常 (RTX 5090)
- ✅ Poetry 依赖管理完成
- ✅ 健康检查正常
- ✅ API 端点可访问

## 📚 文档

- [项目蓝图](docs/blueprint.md) - 完整的项目设计文档
- [环境设置指南](docs/environment-setup.md) - 详细的开发环境配置
- [NLP 指南](docs/nlp_guide.md) - 自然语言处理相关文档
- [音频转录流程](packages/services/audio-processor/whisperX音频转录流程.md) - WhisperX 使用指南
- [Python 开发标准](docs/python-development-standards.md) - Python 代码规范和工具配置

## 🛠️ 开发指南

### 添加新功能

1. 确定功能属于哪个包/服务
2. 在对应目录下开发
3. 更新相关文档
4. 添加测试用例

### 代码规范

- TypeScript 用于前端和共享包
- Python 用于后端和 AI 服务
- 遵循各语言的最佳实践

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

MIT License

## 🔗 相关链接

- [WhisperX](https://github.com/m-bain/whisperX)
- [Camel Tools](https://github.com/CAMeL-Lab/camel_tools)
- [Next.js](https://nextjs.org/)
- [FastAPI](https://fastapi.tiangolo.com/)

# 测试中文提交信息
