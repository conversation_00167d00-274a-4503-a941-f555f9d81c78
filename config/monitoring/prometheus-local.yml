# Prometheus 本地配置
# 适用于所有服务都在本地运行的情况

global:
  scrape_interval: 15s
  evaluation_interval: 15s

# 规则文件配置
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

# 抓取配置
scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 系统监控 (Node Exporter)
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']

  # Redis 监控
  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']

  # PostgreSQL 监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['localhost:9187']

  # 你的微服务监控
  - job_name: 'backend-api'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'audio-processor'
    static_configs:
      - targets: ['localhost:8001']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'word-analyzer'
    static_configs:
      - targets: ['localhost:8002']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'translation-service'
    static_configs:
      - targets: ['localhost:8003']
    metrics_path: '/metrics'
    scrape_interval: 5s

# 告警配置 (可选)
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# 存储配置
storage:
  tsdb:
    path: /var/lib/prometheus/
    retention.time: 15d
