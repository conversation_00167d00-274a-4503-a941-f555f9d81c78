# Grafana 数据源配置
# 放在 /etc/grafana/provisioning/datasources/ 目录下

apiVersion: 1

datasources:
  # Prometheus 数据源
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://localhost:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "5s"
      queryTimeout: "60s"
      httpMethod: "POST"

  # Loki 数据源 (如果后续需要日志监控)
  - name: Loki
    type: loki
    access: proxy
    url: http://localhost:3100
    editable: true
    jsonData:
      maxLines: 1000
