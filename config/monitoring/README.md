# 监控配置说明

这个目录包含两套监控配置方案，你可以根据需要选择使用：

## 🏠 本地部署方案（推荐用于开发）

**适用场景**: 个人开发、避免 Docker 镜像拉取问题、资源占用少

**配置文件**:
- `prometheus-local.yml` - Prometheus 本地配置
- `grafana-datasources.yml` - Grafana 数据源配置

**使用方法**:
```bash
# 一键设置环境
./scripts/setup-local-monitoring.sh

# 启动所有服务
./scripts/start-local-services.sh

# 停止所有服务
./scripts/stop-local-services.sh
```

**详细文档**: [本地监控设置指南](../../docs/local-monitoring-setup.md)

## 🐳 Docker 部署方案

**适用场景**: 生产环境、团队协作、环境一致性要求高

**配置文件**:
- `docker-compose.monitoring.yml` - Docker Compose 监控栈
- `prometheus.yml` - Prometheus Docker 配置（需要创建）
- `loki-config.yml` - Loki 配置（需要创建）
- `promtail-config.yml` - Promtail 配置（需要创建）

**使用方法**:
```bash
# 启动 Docker 监控栈
docker-compose -f config/monitoring/docker-compose.monitoring.yml up -d

# 停止 Docker 监控栈
docker-compose -f config/monitoring/docker-compose.monitoring.yml down
```

**注意**: Docker 方案的具体配置文件还需要创建，目前只有 Docker Compose 编排文件。

## 🔄 方案对比

| 特性 | 本地部署 | Docker 部署 |
|------|----------|-------------|
| 启动速度 | ⚡ 快 | 🐌 较慢 |
| 资源占用 | 💚 低 | 🟡 中等 |
| 网络要求 | 💚 无特殊要求 | 🔴 需要拉取镜像 |
| 环境一致性 | 🟡 依赖本地环境 | 💚 高度一致 |
| 调试便利性 | 💚 方便 | 🟡 需要进入容器 |
| 生产就绪 | 🟡 需要额外配置 | 💚 开箱即用 |

## 📋 当前状态

- ✅ **本地部署方案**: 完整可用
- ⚠️ **Docker 部署方案**: 框架已有，需要补充配置文件

## 🚀 推荐使用

**开发阶段**: 使用本地部署方案  
**生产部署**: 使用 Docker 部署方案（需要先完善配置）

## 🔧 切换方案

如果你想从本地方案切换到 Docker 方案：

1. 停止本地服务: `./scripts/stop-local-services.sh`
2. 创建缺失的 Docker 配置文件
3. 启动 Docker 监控: `docker-compose -f config/monitoring/docker-compose.monitoring.yml up`

如果你想从 Docker 方案切换到本地方案：

1. 停止 Docker 服务: `docker-compose -f config/monitoring/docker-compose.monitoring.yml down`
2. 启动本地服务: `./scripts/start-local-services.sh`
