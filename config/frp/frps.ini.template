# FRP 服务端配置模板
# 适用于阿里云服务器

[common]
# FRP 服务端监听端口
bind_port = 7000

# 认证令牌（与客户端保持一致）
token = arabic_learning_platform_token_change_this

# 管理面板配置
dashboard_port = 7500
dashboard_user = admin
dashboard_pwd = admin_password_change_this

# HTTP 服务端口
vhost_http_port = 80
vhost_https_port = 443

# 日志配置
log_file = /var/log/frp/frps.log
log_level = info
log_max_days = 7

# 子域名配置
subdomain_host = YOUR_DOMAIN_NAME

# 最大连接池大小
max_pool_count = 50

# 心跳配置
heartbeat_timeout = 90

# 用户连接限制
max_ports_per_client = 0

# 允许的端口范围
allow_ports = 6000-6500,8000-8010

# TLS 配置（如果使用 HTTPS）
# tls_cert_file = /etc/letsencrypt/live/YOUR_DOMAIN_NAME/fullchain.pem
# tls_key_file = /etc/letsencrypt/live/YOUR_DOMAIN_NAME/privkey.pem

# 自定义 404 页面
# custom_404_page = /var/www/frp/404.html
