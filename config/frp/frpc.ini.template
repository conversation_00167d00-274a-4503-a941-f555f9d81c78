# FRP 客户端配置模板
# 适用于本地 Ubuntu 服务器

[common]
server_addr = YOUR_ALIYUN_SERVER_IP
server_port = 7000
token = arabic_learning_platform_token_change_this
admin_addr = 127.0.0.1
admin_port = 7400
admin_user = admin
admin_pwd = admin_password_change_this

# 日志配置
log_file = /var/log/frp/frpc.log
log_level = info
log_max_days = 3

# 前端应用 - 主站
[web]
type = http
local_ip = 127.0.0.1
local_port = 3000
custom_domains = YOUR_DOMAIN_NAME
host_header_rewrite = YOUR_DOMAIN_NAME

# 后端 API
[api]
type = http
local_ip = 127.0.0.1
local_port = 8000
custom_domains = api.YOUR_DOMAIN_NAME
host_header_rewrite = api.YOUR_DOMAIN_NAME

# 音频处理服务
[audio-processor]
type = http
local_ip = 127.0.0.1
local_port = 8001
custom_domains = audio.YOUR_DOMAIN_NAME
host_header_rewrite = audio.YOUR_DOMAIN_NAME

# 单词分析服务
[word-analyzer]
type = http
local_ip = 127.0.0.1
local_port = 8002
custom_domains = words.YOUR_DOMAIN_NAME
host_header_rewrite = words.YOUR_DOMAIN_NAME

# 翻译服务
[translation-service]
type = http
local_ip = 127.0.0.1
local_port = 8003
custom_domains = translate.YOUR_DOMAIN_NAME
host_header_rewrite = translate.YOUR_DOMAIN_NAME

# 管理端口 - SSH
[ssh]
type = tcp
local_ip = 127.0.0.1
local_port = 22
remote_port = 6000

# 数据库端口（仅管理使用）
[postgres-admin]
type = tcp
local_ip = 127.0.0.1
local_port = 5432
remote_port = 6432

# Redis 端口（仅管理使用）
[redis-admin]
type = tcp
local_ip = 127.0.0.1
local_port = 6379
remote_port = 6380

# 开发环境热重载端口
[dev-frontend]
type = tcp
local_ip = 127.0.0.1
local_port = 3001
remote_port = 6001

# 监控端口
[monitoring]
type = http
local_ip = 127.0.0.1
local_port = 9090
custom_domains = monitor.YOUR_DOMAIN_NAME
host_header_rewrite = monitor.YOUR_DOMAIN_NAME
