#!/bin/bash

# Enhanced test script for chapter detection with detailed output
cd /home/<USER>/website_project

# Create output directory with timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="test_results_${TIMESTAMP}"
mkdir -p "$OUTPUT_DIR"

echo "=== 章节检测测试 - $(date) ==="
echo "输出目录: $OUTPUT_DIR"

# Function to check service health
check_service() {
    local service_name="$1"
    local url="$2"
    echo "检查 $service_name..."
    if curl -s "$url" > /dev/null; then
        echo "✅ $service_name 运行正常"
        return 0
    else
        echo "❌ $service_name 未响应"
        return 1
    fi
}

echo -e "\n=== 检查所有服务状态 ==="
check_service "Audio Processor (8001)" "http://localhost:8001/health"
check_service "Camel Tools API (8002)" "http://localhost:8002/health"
check_service "Qwen Service (8004)" "http://localhost:8004/api/v1/health"

echo -e "\n=== 开始音频转录和章节检测测试 ==="
echo "音频文件: arabic_audio_short.mp3"
echo "章节标记:"
echo "  - مقدمة"
echo "  - الفصل الأول الصهيونية تقاتل على جبهة اللغة"
echo "  - الفصل الثاني ولادة الصهيونية الأدبية"
echo "  - الفصل الثالث العرق والدين في الأدب الصهيوني يستولدان الصهيونية السياسية"
echo "  - الفصل الرابع شخصية اليهودي التائه: نشأتها وتطورها"
echo "  - الفصل الخامس الأدب الصهيوني يضبط خطواته مع السياسة"

echo -e "\n开始处理..."
START_TIME=$(date +%s)

# Execute the API call and save results (with translation enabled)
curl -X POST "http://localhost:8001/transcribe" \
  -F "file=@arabic_audio_short.mp3" \
  -F "language=ar" \
  -F 'chapter_markers=["مقدمة","الفصل الأول الصهيونية تقاتل على جبهة اللغة","الفصل الثاني ولادة الصهيونية الأدبية","الفصل الثالث العرق والدين في الأدب الصهيوني يستولدان الصهيونية السياسية","الفصل الرابع شخصية اليهودي التائه: نشأتها وتطورها","الفصل الخامس الأدب الصهيوني يضبط خطواته مع السياسة"]' \
  -F "split_chapters=false" \
  -F "enable_translation=true" \
  -F "target_language=中文" \
  -F "translation_context_segments=4" \
  -H "Accept: application/json" \
  --max-time 0 \
  > "$OUTPUT_DIR/raw_response.json" 2> "$OUTPUT_DIR/curl_error.log"

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo "处理完成，耗时: ${DURATION}秒"

# Check if the request was successful
if [ $? -eq 0 ] && [ -s "$OUTPUT_DIR/raw_response.json" ]; then
    echo "✅ API调用成功"
    
    # Pretty print the JSON response
    echo "格式化JSON响应..."
    jq '.' "$OUTPUT_DIR/raw_response.json" > "$OUTPUT_DIR/formatted_response.json" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "✅ JSON格式化成功"
        
        # Extract specific sections for analysis
        echo "提取分析数据..."
        
        # Extract transcription text
        jq -r '.transcription.text // "未找到转录文本"' "$OUTPUT_DIR/formatted_response.json" > "$OUTPUT_DIR/transcription_text.txt"
        
        # Extract segments
        jq '.segments // []' "$OUTPUT_DIR/formatted_response.json" > "$OUTPUT_DIR/segments.json"
        
        # Extract detected chapters
        jq '.chapters // []' "$OUTPUT_DIR/formatted_response.json" > "$OUTPUT_DIR/detected_chapters.json"
        
        # Extract processing report
        jq '.report // []' "$OUTPUT_DIR/formatted_response.json" > "$OUTPUT_DIR/processing_report.json"
        
        # Extract translation information
        jq -r '.translation_enabled // false' "$OUTPUT_DIR/formatted_response.json" > "$OUTPUT_DIR/translation_enabled.txt"
        jq -r '.target_language // "无"' "$OUTPUT_DIR/formatted_response.json" > "$OUTPUT_DIR/target_language.txt"
        jq -r '.translation_processing_time // "无"' "$OUTPUT_DIR/formatted_response.json" > "$OUTPUT_DIR/translation_time.txt"
        
        # Extract segments with translations
        jq '[.segments[] | select(.translated_text != null)]' "$OUTPUT_DIR/formatted_response.json" > "$OUTPUT_DIR/translated_segments.json"
        
        # Create summary report
        cat > "$OUTPUT_DIR/test_summary.txt" << EOF
=== 章节检测测试总结 ===
测试时间: $(date)
音频文件: arabic_audio_short.mp3
处理耗时: ${DURATION}秒

=== 转录统计 ===
段落数量: $(jq 'length' "$OUTPUT_DIR/segments.json")

=== 章节检测结果 ===
检测到的章节数量: $(jq 'length' "$OUTPUT_DIR/detected_chapters.json")
预期章节数量: 6

=== 翻译结果 ===
翻译启用: $(cat "$OUTPUT_DIR/translation_enabled.txt")
目标语言: $(cat "$OUTPUT_DIR/target_language.txt")
翻译处理时间: $(cat "$OUTPUT_DIR/translation_time.txt") 秒
已翻译段落数量: $(jq 'length' "$OUTPUT_DIR/translated_segments.json")

=== 检测到的章节 ===
EOF
        
        # Add detected chapters to summary
        jq -r '.[] | "- \(.title) (时间: \(.start_time)s)"' "$OUTPUT_DIR/detected_chapters.json" >> "$OUTPUT_DIR/test_summary.txt"
        
        echo -e "\n=== 文件输出完成 ==="
        echo "输出文件:"
        ls -la "$OUTPUT_DIR/"
        
        echo -e "\n=== 快速查看结果 ==="
        echo "检测到的章节数量: $(jq 'length' "$OUTPUT_DIR/detected_chapters.json")"
        echo "检测到的章节:"
        jq -r '.[] | "  - \(.title) (时间: \(.start_time)s)"' "$OUTPUT_DIR/detected_chapters.json"
        
        echo -e "\n=== 翻译结果验证 ==="
        echo "翻译启用: $(cat "$OUTPUT_DIR/translation_enabled.txt")"
        echo "目标语言: $(cat "$OUTPUT_DIR/target_language.txt")"
        echo "已翻译段落数: $(jq 'length' "$OUTPUT_DIR/translated_segments.json") / $(jq 'length' "$OUTPUT_DIR/segments.json")"
        
        # Show first few translation examples
        echo -e "\n=== 翻译示例 (前3个段落) ==="
        jq -r '.[:3] | .[] | "原文: \(.text)\n译文: \(.translated_text // "无翻译")\n---"' "$OUTPUT_DIR/segments.json"
        
        echo -e "\n详细分析请查看: $OUTPUT_DIR/test_summary.txt"
        
    else
        echo "❌ JSON格式化失败，可能响应格式有误"
        echo "原始响应内容:"
        head -20 "$OUTPUT_DIR/raw_response.json"
    fi
else
    echo "❌ API调用失败"
    echo "错误信息:"
    cat "$OUTPUT_DIR/curl_error.log"
fi

echo -e "\n=== 测试完成 ==="
echo "所有结果已保存到: $OUTPUT_DIR/"
