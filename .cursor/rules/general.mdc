---
description: 
globs: 
alwaysApply: true
---
# 核心交互流程：
回复用户前，必须遵循以下流程：
1. 阅读 [architecture.md](mdc:🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/docs/architecture.md) 文件，以便于对项目整体架构有一个完整的了解, 阅读完成后, 请输出你对项目的整体认识了了解。
2. 阅读结束后, 请输出"我已经完全了解了项目结构, 清楚了项目配置环境和各个模块的微服务环境配置和依赖管理机制"

## 代码分析流程
- 在进行任何代码修改前，先全面审查相关代码结构
- 识别所有与任务相关的代码位置，确保后续修改可以同步调整所有相关部分
- 确保充分理解代码结构和依赖关系后再进行更改

## 新功能设计与重构流程
1. 不要直接实施用户提出的方案
1. 评估用户提出解决方案的合理性和可行性
2. 确定是否获取了足够的上下文信息, 如果你发现你需要更多的上下文, 请向用户提出申请
2. 复述你理解的用户方案, 明确确认是否完全理解用户需求
4. 清晰展示综合评估结果
5. 若有问题，明确提出疑问或建议
6. 若无问题，明确询问用户是否开始执行任务

## 代码构建标准
- 所有代码构建必须基于对现有代码或第三方库的精确理解
- 在缺乏足够信息时不进行实现方法猜测
- 代码应遵循项目现有的架构设计和命名约定

## 测试流程
- 第一轮：通过单元测试和功能测试脚本进行验证
- 第二轮：找到程序入口点，在真实环境中进行集成测试
- 面对测试过程发现的所有问题，禁止采用迂回、回退、简化的方法让测试通过，必须找出根本原因并彻底解决

## 错误处理和知识更新
- 当代码逻辑正确但程序报错时，主动查阅相关第三方源码
- 获取最新技术信息，确保解决方案基于最新知识

## 特殊指令处理
当收到"请解释"指令时，按照以下步骤回答：
1. 用简单易懂的语言解释代码中的专业术语
2. 解释代码涉及的基本语法知识
3. 清晰解释代码在当前上下文中的执行逻辑和功能
4. 提供简化示例帮助理解

每轮对话结束后, 观察[architecture.md](mdc:🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/🏠 Project Root/docs/architecture.md)是否需要更新, 如需要更新, 请及时更新