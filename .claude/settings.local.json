{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(python:*)", "Bash(ls:*)", "<PERSON><PERSON>(curl:*)", "Bash(./test_chapter_detection.sh:*)", "<PERSON><PERSON>(jq:*)", "Bash(grep:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(mypy:*)", "<PERSON><PERSON>(poetry run ruff:*)", "<PERSON><PERSON>(poetry run:*)", "<PERSON><PERSON>(timeout:*)", "Bash(bash:*)", "<PERSON><PERSON>(cat:*)", "Bash(ss:*)", "Bash(kill:*)", "Bash(ruff check:*)", "Bash(for:*)", "Bash(do echo \"=== $file ===\")", "Bash(awk:*)", "Bash(done)", "<PERSON><PERSON>(sed:*)"], "deny": []}}