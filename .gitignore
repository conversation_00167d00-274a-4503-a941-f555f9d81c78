# Dependencies
node_modules/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis
.ruff_cache/
packages/services/qwen-service/llama.cpp/
# Python 虚拟环境 (服务特定)
packages/services/*/venv/
packages/backend/venv/
**/venv/
**/env/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build outputs
dist/
build/
*.egg-info/
.next/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Database
*.db
*.sqlite
*.sqlite3

# Redis dump file
dump.rdb

# Audio/Video files (large files should use Git LFS)
*.mp3
*.mp4
*.wav
*.avi
*.mov
*.wmv
*.flv
*.webm

# Model files (large files should use Git LFS)
*.bin
*.safetensors
*.onnx
*.pt
*.pth
*.msgpack

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry (保留poetry.lock用于依赖版本锁定)
# poetry.lock

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Docker相关
.dockerignore
docker-compose.override.yml
.docker/

# 项目特定
# 临时文件和缓存
.cache/
.tmp/
*.tmp

# 测试覆盖率报告
htmlcov/
.coverage.*
coverage.json

# 开发工具配置
.vscode/settings.json
.vscode/launch.json

# 系统和编辑器临时文件
*~
.#*
\#*#
.*.swp
.*.swo

# 项目文档生成
docs/_build/
docs/build/

# 监控和日志
monitoring/data/
logs/
*.log.*

# 备份文件
*.bak
*.backup
*.orig

# 模型文件目录
**/models/**/*.pt
**/models/**/*.bin
**/models/**/*.gguf
**/models/**/*.ckpt
**/models/**/*.ot
**/models/**/*.model
**/models/**/*.tokenizer

# 微服务模型资源文件夹
packages/services/*/assets/
**/assets/

