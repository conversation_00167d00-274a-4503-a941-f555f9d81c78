name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test-frontend:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install pnpm
      run: npm install -g pnpm
    
    - name: Install dependencies
      run: pnpm install
    
    - name: Type check
      run: pnpm --filter frontend type-check
    
    - name: Lint
      run: pnpm --filter frontend lint
    
    - name: Test
      run: pnpm --filter frontend test

  test-backend:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y ffmpeg
    
    - name: Install Poetry
      run: |
        curl -sSL https://install.python-poetry.org | python3 -
        echo "$HOME/.local/bin" >> $GITHUB_PATH
    
    - name: Install backend dependencies
      working-directory: packages/backend
      run: |
        poetry install
    
    - name: Run backend tests
      working-directory: packages/backend
      run: |
        poetry run pytest
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0

  test-audio-processor:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12"]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y ffmpeg

    - name: Install Poetry
      run: |
        curl -sSL https://install.python-poetry.org | python3 -
        echo "$HOME/.local/bin" >> $GITHUB_PATH

    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: packages/services/audio-processor/.venv
        key: venv-${{ runner.os }}-${{ matrix.python-version }}-${{ hashFiles('packages/services/audio-processor/poetry.lock') }}

    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      working-directory: packages/services/audio-processor
      run: poetry install --no-interaction --no-root

    - name: Install project
      working-directory: packages/services/audio-processor
      run: poetry install --no-interaction

    - name: Run code quality checks
      working-directory: packages/services/audio-processor
      run: |
        poetry run ruff check src/ tests/
        poetry run ruff format --check src/ tests/
        poetry run mypy src/

    - name: Run unit tests
      working-directory: packages/services/audio-processor
      run: |
        poetry run pytest tests/unit/ -v --cov=src --cov-report=xml --cov-report=term-missing -m "unit"

    - name: Run integration tests
      working-directory: packages/services/audio-processor
      run: |
        poetry run pytest tests/integration/ -v --cov=src --cov-append --cov-report=xml -m "integration"

    - name: Run performance tests (quick)
      working-directory: packages/services/audio-processor
      run: |
        poetry run pytest tests/performance/ -v -m "not slow" || true

    - name: Run model functionality tests
      working-directory: packages/services/audio-processor
      run: |
        poetry run python test_model.py

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: packages/services/audio-processor/coverage.xml
        flags: audio-processor
        name: audio-processor-coverage

  test-camel-tools-api:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12"]

    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Poetry
      run: |
        curl -sSL https://install.python-poetry.org | python3 -
        echo "$HOME/.local/bin" >> $GITHUB_PATH

    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: packages/services/camel-tools-api/.venv
        key: venv-${{ runner.os }}-${{ matrix.python-version }}-${{ hashFiles('packages/services/camel-tools-api/poetry.lock') }}

    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      working-directory: packages/services/camel-tools-api
      run: poetry install --no-interaction --no-root

    - name: Install project
      working-directory: packages/services/camel-tools-api
      run: poetry install --no-interaction

    - name: Run code quality checks
      working-directory: packages/services/camel-tools-api
      run: |
        poetry run ruff check src/ tests/
        poetry run ruff format --check src/ tests/
        poetry run mypy src/

    - name: Run unit tests
      working-directory: packages/services/camel-tools-api
      run: |
        poetry run pytest tests/unit/ -v --cov=src --cov-report=xml --cov-report=term-missing

    - name: Run integration tests
      working-directory: packages/services/camel-tools-api
      run: |
        poetry run pytest tests/integration/ -v --cov=src --cov-append --cov-report=xml

    - name: Run performance tests (quick)
      working-directory: packages/services/camel-tools-api
      run: |
        poetry run pytest tests/performance/ -v -m "not slow"

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: packages/services/camel-tools-api/coverage.xml
        flags: camel-tools-api
        name: camel-tools-api-coverage
