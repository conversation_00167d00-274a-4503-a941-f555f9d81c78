name: Deploy to Production

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.DEPLOY_SSH_KEY }}
    
    - name: Deploy to server
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} << 'EOF'
          cd /opt/arabic-learning-platform
          git pull origin main
          
          # Update Node.js dependencies
          pnpm install --frozen-lockfile
          
          # Update Python services
          cd packages/services/audio-processor
          poetry install --only=main
          cd ../../..
          
          # Restart services
          sudo systemctl restart audio-processor
          sudo systemctl restart nginx
          
          # Health check
          sleep 10
          curl -f http://localhost:8001/health || exit 1
        EOF
    
    - name: Notify deployment
      if: success()
      run: |
        echo "Deployment successful to production server"
    
    - name: Notify failure
      if: failure()
      run: |
        echo "Deployment failed"
