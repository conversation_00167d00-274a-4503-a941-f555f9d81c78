#!/bin/bash

# Test script for audio-processor API
cd /home/<USER>/website_project

echo "=== 检查所有服务状态 ==="
echo "1. Audio Processor (8001):"
curl -s http://localhost:8001/health || echo "服务未响应"

echo -e "\n2. Camel Tools API (8002):"
curl -s http://localhost:8002/health || echo "服务未响应"

echo -e "\n3. Qwen Service (8004):"
curl -s http://localhost:8004/api/v1/health || echo "服务未响应"

echo -e "\n=== 测试音频转录和章节检测 ==="
echo "使用完整的章节标记列表进行测试..."

curl -X POST "http://localhost:8001/transcribe" \
  -F "file=@arabic_audio_short.mp3" \
  -F "language=ar" \
  -F 'chapter_markers=["مقدمة","الفصل الأول الصهيونية تقاتل على جبهة اللغة","الفصل الثاني ولادة الصهيونية الأدبية","الفصل الثالث العرق والدين في الأدب الصهيوني يستولدان الصهيونية السياسية","الفصل الرابع شخصية اليهودي التائه: نشأتها وتطورها","الفصل الخامس الأدب الصهيوني يضبط خطواته مع السياسة"]' \
  -F "split_chapters=false" \
  -H "Accept: application/json" \
  --max-time 1800 \
  | jq '.'
