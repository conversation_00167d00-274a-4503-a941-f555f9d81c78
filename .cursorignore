# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)

# 模型文件和二进制文件
packages/services/audio-processor/models/whisper/faster-whisper-large-v3-turbo-ct2/

# 日志文件
logs/

# 临时文件
.tmp/
.temp/

# 环境变量
.env
.env.*

# 构建产物
dist/
.next/
build/
**/__pycache__/
**/venv/
**/.venv/
**/dist/
**/build/
**/node_modules/

# 多媒体
*.mp3
*.mp4
*.wav
*.avi
*.mov
*.wmv
*.flv
*.webm

# 数据文件
*.csv
*.parquet
*.sqlite

# 忽略大型输出数据
*.csv
*.parquet
*.sqlite

packages/services/qwen-service/llama.cpp/
