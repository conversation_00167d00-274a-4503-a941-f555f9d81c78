# 阿拉伯语学习平台 - 环境变量配置示例

# ===========================================
# 数据库配置
# ===========================================
DATABASE_URL=postgresql://username:password@localhost:5432/arabic_learning
POSTGRES_USER=arabic_user
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=arabic_learning

# ===========================================
# Redis 配置
# ===========================================
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# ===========================================
# 应用配置
# ===========================================
# 前端
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_AUDIO_PROCESSOR_URL=http://localhost:8001

# 生产环境 URL（通过 FRP 映射）
NEXT_PUBLIC_PROD_API_URL=https://api.your-domain.com
NEXT_PUBLIC_PROD_AUDIO_URL=https://audio.your-domain.com
NEXT_PUBLIC_PROD_WORDS_URL=https://words.your-domain.com
NEXT_PUBLIC_PROD_TRANSLATE_URL=https://translate.your-domain.com

# 后端
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000
DEBUG=true
SECRET_KEY=your-super-secret-key-change-this-in-production

# ===========================================
# FRP 部署配置
# ===========================================
# 阿里云服务器配置
ALIYUN_SERVER_IP=your-aliyun-server-ip
DOMAIN_NAME=your-domain.com

# 本地服务器配置
LOCAL_SERVER_IP=*************
FRP_TOKEN=arabic_learning_platform_token_change_this

# FRP 管理配置
FRP_DASHBOARD_USER=admin
FRP_DASHBOARD_PASSWORD=admin_password_change_this

# ===========================================
# 音频处理服务配置
# ===========================================
AUDIO_PROCESSOR_HOST=0.0.0.0
AUDIO_PROCESSOR_PORT=8001
MODEL_PATH=./assets/transcription/whisper/faster-whisper-large-v3-turbo-ct2
DEVICE=cuda
COMPUTE_TYPE=float16

# HuggingFace Token (用于说话人分离功能)
HF_TOKEN=your_huggingface_token_here

# ===========================================
# AI 服务配置
# ===========================================
# OpenAI API (如果使用)
OPENAI_API_KEY=your_openai_api_key

# Qwen API (用于翻译和方言转换)
QWEN_API_KEY=your_qwen_api_key
QWEN_API_URL=https://dashscope.aliyuncs.com/api/v1/

# Qwen Service 配置
QWEN_SERVICE_API_URL=http://localhost:8004
QWEN_SERVICE_TIMEOUT=300.0
QWEN_SERVICE_MAX_CONNECTIONS=20
QWEN_SERVICE_MAX_KEEPALIVE=10
QWEN_SERVICE_KEEPALIVE_EXPIRY=30.0

# Qwen Call Manager 配置
QWEN_MAX_CONCURRENT_REQUESTS=8
QWEN_MAX_QUEUE_SIZE=1000
QWEN_HIGH_PRIORITY_TIMEOUT=5.0
QWEN_MEDIUM_PRIORITY_TIMEOUT=30.0
QWEN_LOW_PRIORITY_TIMEOUT=300.0

# Circuit Breaker 配置
QWEN_CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
QWEN_CIRCUIT_BREAKER_RECOVERY_TIMEOUT=30.0
QWEN_CIRCUIT_BREAKER_SUCCESS_THRESHOLD=3

# ===========================================
# 文件存储配置
# ===========================================
# 本地存储
UPLOAD_DIR=./uploads
STATIC_DIR=./static

# 云存储 (可选)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_BUCKET_NAME=your_bucket_name
AWS_REGION=us-east-1

# ===========================================
# 邮件服务配置
# ===========================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# ===========================================
# 安全配置
# ===========================================
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# CORS 配置
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=INFO
LOG_FORMAT=json

# ===========================================
# 开发工具配置
# ===========================================
# 是否启用 API 文档
ENABLE_DOCS=true

# 是否启用调试模式
DEBUG_MODE=true

# 是否启用性能监控
ENABLE_MONITORING=false
