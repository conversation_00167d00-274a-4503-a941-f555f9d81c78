{"name": "arabic-learning-platform", "version": "1.0.0", "description": "综合性阿拉伯语学习平台", "private": true, "workspaces": ["packages/*", "packages/services/*"], "scripts": {"dev": "concurrently \"pnpm --filter frontend dev\" \"pnpm backend:dev\"", "build": "pnpm --filter frontend build && pnpm backend:build", "test": "pnpm --filter frontend test && pnpm backend:test", "lint": "pnpm --filter frontend lint", "type-check": "pnpm --filter frontend type-check && pnpm --filter @arabic-learning/types type-check", "clean": "pnpm --filter frontend clean && rm -rf packages/*/dist", "backend:dev": "cd packages/backend && poetry run python app/main.py", "backend:build": "cd packages/backend && poetry build", "backend:test": "cd packages/backend && poetry run pytest", "backend:shell": "cd packages/backend && poetry shell", "audio-processor:dev": "cd packages/services/audio-processor && poetry run python src/main.py", "audio-processor:shell": "cd packages/services/audio-processor && poetry shell", "services:dev": "concurrently \"pnpm audio-processor:dev\"", "services:setup": "./scripts/setup-poetry-envs.sh", "setup": "pnpm install && ./scripts/setup-local-services.sh", "deps:add": "./scripts/add-dependency.sh"}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0", "dependencies": {"axios": "^1.9.0", "playwright": "^1.52.0", "socks-proxy-agent": "^8.0.5"}}