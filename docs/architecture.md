# 项目整体开发环境说明

本文档详细描述了项目开发环境、目录结构、VSCode 多根工作区配置和技术栈选择

## 📁 完整目录树

```
website_project/
├── config/                     # 配置文件目录
│   ├── frp/                    # FRP 内网穿透配置
│   └── monitoring/             # 监控配置
│
├── docs/                       # 项目文档
│   ├── architecture.md         # 架构设计决策文档（本文档）
│   ├── blueprint.md            # 项目蓝图文件
│   ├── git-workflow-guide.md   # Git 工作流指南
│   ├── local-monitoring-setup.md # 本地监控设置
│   └── nlp_guide.md           # NLP 指南
│
├── models/                     # 共享模型存储目录（保留用于文档、翻译等共享模型）
│   ├── README.md               # 模型管理说明
│   ├── document/               # 文档处理模型
│   ├── shared/                 # 共享模型资源
│   │   └── model-registry.json # 模型注册表
│   └── translation/            # 翻译模型
│
├── packages/                   # 核心服务代码（Monorepo 工作区）
│   │
│   ├── frontend/               # Next.js 前端应用
│   │   ├── app/                # Next.js 13+ App Router
│   │   ├── components/         # 可复用 React 组件
│   │   ├── constants/          # 常量定义
│   │   ├── hooks/              # 自定义 React Hooks
│   │   ├── lib/                # 核心辅助函数/库封装
│   │   ├── public/             # 静态资源
│   │   ├── styles/             # 样式文件
│   │   ├── types/              # TypeScript 类型定义
│   │   ├── next.config.js      # Next.js 配置文件
│   │   ├── package.json        # 前端项目依赖
│   │   └── tsconfig.json       # TypeScript 配置文件
│   │
│   ├── backend/                # FastAPI 后端应用 (Clean Architecture + Service Factory)
│   │   ├── app/                # Python 应用主模块
│   │   │   ├── api/            # API 路由层
│   │   │   ├── core/           # 核心配置与通用模块
│   │   │   ├── shared/         # 🆕 通用可复用组件
│   │   │   │   ├── interfaces/ # 通用接口定义
│   │   │   │   ├── services/   # 通用服务实现
│   │   │   │   ├── models/     # 通用数据模型
│   │   │   │   └── infrastructure/ # 通用基础设施
│   │   │   ├── services/       # 🔄 服务模块 (按服务分组)
│   │   │   │   ├── qwen/       # Qwen 服务模块
│   │   │   │   │   ├── interfaces/    # Qwen 特定接口
│   │   │   │   │   ├── services/      # Qwen 服务实现
│   │   │   │   │   ├── models/        # Qwen 数据模型
│   │   │   │   │   ├── adapters/      # Qwen 适配器
│   │   │   │   │   └── infrastructure/ # Qwen 基础设施
│   │   │   │   └── [future]/   # 未来服务模块 (audio, document, etc.)
│   │   │   ├── infrastructure/ # 应用级基础设施
│   │   │   │   ├── factory.py  # 通用服务工厂
│   │   │   │   └── container.py # 依赖注入容器
│   │   │   └── main.py         # FastAPI 应用入口
│   │   ├── alembic/            # Alembic 数据库迁移脚本
│   │   ├── tests/              # 后端测试用例
│   │   └── pyproject.toml      # Python 项目依赖与配置
│   │
│   ├── shared/                 # 共享逻辑包
│   │   └── i18n/               # 国际化资源共享包
│   │       ├── src/
│   │       │   ├── locales/    # 翻译文件
│   │       │   └── index.ts
│   │       └── package.json
│   │
│   ├── services/               # 独立微服务
│   │   ├── audio-processor/    # 音频处理服务 (重构为清洁架构 + 功能模块化)
│   │   │   ├── src/            # 源代码目录
│   │   │   │   ├── interfaces/ # 抽象接口定义
│   │   │   │   │   ├── audio_processing.py  # 核心音频处理接口
│   │   │   │   │   ├── external_services.py # 外部服务客户端接口
│   │   │   │   │   └── services.py          # 服务工厂接口
│   │   │   │   ├── services/   # 具体服务实现 (按功能模块组织)
│   │   │   │   │   ├── transcription/       # 音频转录模块
│   │   │   │   │   │   └── audio_transcription_service.py
│   │   │   │   │   ├── chapter_detection/   # 章节检测模块
│   │   │   │   │   │   ├── orchestrator.py             # 章节检测编排器
│   │   │   │   │   │   ├── text_preprocessing.py       # 阿拉伯语文本预处理
│   │   │   │   │   │   ├── cross_segment_matching.py   # 跨段匹配算法
│   │   │   │   │   │   ├── word_level_timestamp.py     # 词级时间戳计算
│   │   │   │   │   │   └── segment_reorganization.py   # 段落重组服务
│   │   │   │   │   ├── chapter_processing/  # 章节处理模块
│   │   │   │   │   │   └── chapter_splitting_service.py # 章节分割服务
│   │   │   │   │   └── orchestration/       # 主编排模块
│   │   │   │   │       └── audio_processing_service.py  # 主音频处理编排服务
│   │   │   │   ├── infrastructure/ # 基础设施层
│   │   │   │   │   ├── factory.py      # 服务工厂实现
│   │   │   │   │   ├── container.py    # 依赖注入容器
│   │   │   │   │   └── external_clients.py # 外部服务HTTP客户端
│   │   │   │   ├── adapters/   # 适配器层
│   │   │   │   │   └── api_adapters.py # API模型转换和文件处理适配器
│   │   │   │   ├── api/        # API 定义 (重构为使用依赖注入)
│   │   │   │   ├── core/       # 核心配置和工具
│   │   │   │   ├── models/     # 数据模型 (注意：这是 src/models，不是根目录的 models/)
│   │   │   │   └── main.py     # 入口文件 (重构为使用新架构)
│   │   │   ├── assets/         # 本地模型资源
│   │   │   │   ├── transcription/  # 转录模型
│   │   │   │   └── alignment/      # 音频对齐模型
│   │   │   ├── .vscode/        # VSCode 配置
│   │   │   │   └── settings.json
│   │   │   ├── .gitignore      # Git 忽略文件 (包含 Poetry 自动生成的 models/ 目录)
│   │   │   ├── pyproject.toml  # 依赖管理 (已修复 Poetry 包配置问题)
│   │   │   ├── test_model.py   # 模型测试脚本
│   │   │   └── README.md       # 服务文档
│   │   │
│   │   ├── qwen-service/       # Qwen 大语言模型服务
│   │   │   ├── src/            # 源代码目录
│   │   │   │   ├── api/        # API 定义
│   │   │   │   ├── core/       # 核心逻辑
│   │   │   │   ├── models/     # 数据模型
│   │   │   │   └── main.py     # 入口文件
│   │   │   ├── assets/         # 本地模型资源
│   │   │   │   └── Qwen3-32B-Q5_0.gguf  # Qwen 大语言模型
│   │   │   ├── .vscode/        # VSCode 配置
│   │   │   │   └── settings.json
│   │   │   └── pyproject.toml  # 依赖管理
│   │   │
│   │   ├── camel-tools-api/    # Camel-Tools 阿拉伯语处理服务 (重构为清洁架构)
│   │   │   ├── src/            # 源代码目录
│   │   │   │   ├── interfaces/ # 抽象接口定义
│   │   │   │   │   ├── processing.py    # 核心处理接口
│   │   │   │   │   ├── caching.py       # 缓存接口 (分离为通用和业务特定接口)
│   │   │   │   │   └── services.py      # 服务工厂接口
│   │   │   │   ├── services/   # 具体服务实现
│   │   │   │   │   ├── normalization.py    # 文本标准化服务
│   │   │   │   │   ├── tokenization.py     # 分词服务
│   │   │   │   │   ├── morphology.py       # 形态学分析服务
│   │   │   │   │   ├── batch_processing.py # 批处理服务
│   │   │   │   │   └── text_processing.py  # 主文本处理服务
│   │   │   │   ├── infrastructure/ # 基础设施层
│   │   │   │   │   ├── factory.py      # 服务工厂实现 (使用依赖注入)
│   │   │   │   │   ├── container.py    # 依赖注入容器
│   │   │   │   │   └── redis_cache_adapter.py # Redis缓存适配器 (遵循清洁架构)
│   │   │   │   ├── adapters/   # 适配器层
│   │   │   │   │   └── api_adapters.py # API模型转换适配器
│   │   │   │   ├── api/        # API 定义 (重构为使用依赖注入)
│   │   │   │   ├── core/       # 核心配置和工具
│   │   │   │   ├── models/     # 数据模型
│   │   │   │   └── main.py     # 入口文件 (重构为使用新架构)
│   │   │   ├── .vscode/        # VSCode 配置
│   │   │   │   └── settings.json
│   │   │   ├── pyproject.toml  # 依赖管理
│   │   │   ├── package.json    # 服务脚本
│   │   │   └── README.md       # 服务文档
│   │   │

│   │
│   └── types/                  # 共享 TypeScript 类型
│       ├── src/
│       │   ├── index.ts        # 类型出口
│       │   ├── document.ts     # 文档解析类型
│       │   ├── api.ts          # API 类型
│       │   ├── audio.ts        # 音频处理类型
│       │   ├── user.ts         # 用户类型
│       │   └── content.ts      # 内容类型
│       ├── package.json
│       └── tsconfig.json
│
├── scripts/                    # 脚本文件
│   ├── add-dependency.sh       # 依赖添加脚本
│   ├── deploy-production.sh    # 生产环境部署脚本
│   ├── frp-deploy.sh          # FRP 部署脚本
│   ├── health-check.sh        # 健康检查脚本
│   ├── setup-dev-env.sh       # 开发环境设置脚本
│   ├── setup-local-monitoring.sh # 本地监控设置脚本
│   ├── setup-poetry-envs.sh   # Poetry 环境设置脚本
│   ├── setup-python-service.sh # Python 服务设置脚本
│   ├── start-local-services.sh # 启动本地服务脚本
│   └── stop-local-services.sh  # 停止本地服务脚本
│
├── arabic-learning-platform.code-workspace # VSCode 多根工作区配置
├── package.json                # Monorepo 根 package.json
├── pnpm-workspace.yaml         # pnpm 工作区配置文件
├── PROJECT_STATUS.md           # 项目状态文档
├── README.md                   # 项目总说明文档
├── VSCode_多微服务开发指南.md    # VSCode 开发指南
└── 远程SSH开发指南.md           # 远程开发指南
```

## 🔧 技术栈对应

### Python 项目 (使用 pyproject.toml + Poetry)

- `packages/backend/`
- `packages/services/audio-processor/`
- `packages/services/qwen-service/`
- `packages/services/camel-tools-api/`

### Node.js 项目 (使用 package.json + pnpm)

- `packages/frontend/`
- `packages/shared/i18n/`
- `packages/types/`

## 🏗️ VSCode 多根工作区配置

### 工作区结构

项目采用 VSCode 多根工作区（Multi-Root Workspace）架构，通过 `arabic-learning-platform.code-workspace` 文件统一管理所有微服务和共享包。

### 工作区配置层次

#### 1. 工作区级别设置（Workspace Level）

- **配置文件**: `arabic-learning-platform.code-workspace`
- **作用范围**: 影响整个工作区的所有文件夹
- **主要配置**:
  - 全局编辑器设置（自动保存、格式化等）
  - 文件关联和搜索排除规则
  - TypeScript 和 Web 开发相关设置
  - 推荐扩展列表

#### 2. 服务级别设置（Service Level）

- **配置文件**: 各服务的 `.vscode/settings.json`
- **作用范围**: 仅影响特定服务目录
- **主要配置**:
  - Python 解释器路径（指向各自的 `.venv`）
  - Python 分析路径配置
  - Ruff 格式化和代码检查设置

### 配置层次优先级

```
服务级别 (.vscode/settings.json) > 工作区级别 (*.code-workspace) > 全局设置
```

### 工作区文件夹配置

每个微服务在工作区中都有独立的配置：

```json
{
  "name": "🎵 Audio Processor",
  "path": "./packages/services/audio-processor",
  "settings": {
    "python.analysis.extraPaths": ["./src"],
    "python.defaultInterpreterPath": "./.venv/bin/python"
  }
}
```

### 配置冲突解决策略

1. **Python 设置隔离**: 所有 Python 相关设置移至服务级别，避免工作区级别冲突
2. **代码操作分离**: 不同技术栈的代码操作（Ruff vs ESLint）在各自服务中配置
3. **路径解析独立**: 每个服务维护独立的模块解析路径

## 🐍 Python 依赖和虚拟环境管理

### 虚拟环境策略

#### 环境选择原则

- **开发环境**: 使用 conda base 环境而非 conda 创建的虚拟环境
- **服务隔离**: 每个 Python 微服务使用 Poetry 管理的独立 `.venv` 虚拟环境
- **环境关系**: Poetry 管理的 `.venv` 与 conda base 环境协同工作

#### 环境配置结构

```
conda base environment (系统级 Python 环境)
├── 全局系统依赖 (CUDA, cuDNN 等)
└── 各微服务 Poetry .venv 环境
    ├── packages/backend/.venv/
    ├── packages/services/audio-processor/.venv/
    ├── packages/services/qwen-service/.venv/
    └── packages/services/camel-tools-api/.venv/
```

### Poetry 依赖管理

#### pyproject.toml 配置原则

- **统一配置文件**: 使用 `pyproject.toml` 作为所有工具配置的中心文件
- **依赖源配置**: 配置清华镜像源和 PyTorch 专用源
- **开发依赖分组**: 将开发工具依赖与运行时依赖分离

#### 典型 pyproject.toml 结构

```toml
[tool.poetry]
name = "service-name"
version = "1.0.0"
description = "服务描述"

[tool.poetry.dependencies]
python = ">=3.9,<3.13"
# 运行时依赖

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
ruff = "^0.1.0"
mypy = "^1.5.0"
# 开发工具依赖

[[tool.poetry.source]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
priority = "primary"

[tool.ruff]
# Ruff 配置
```

### Ruff 中心化 Python 工具链

#### 工具链演进

- **旧工具链**: Black + Flake8 + isort + Pylint（多工具组合）
- **新工具链**: Ruff（统一工具）+ MyPy（类型检查）

#### Ruff 配置特点

- **88 字符行长度**: 保持与 Black 兼容的格式化标准
- **综合规则集**: 集成 pycodestyle、Pyflakes、isort、Pylint 等规则
- **格式化兼容**: 提供与 Black 兼容的代码格式化
- **性能优势**: 比传统工具链快 10-100 倍

#### 配置管理策略

- **集中配置**: 所有工具配置统一在 `pyproject.toml` 中
- **服务独立**: 每个服务可根据需要调整 Ruff 规则
- **VSCode 集成**: 通过 charliermarsh.ruff 扩展提供编辑器支持

### 依赖安装和管理流程

#### 新服务创建流程

1. 使用 `scripts/setup-python-service.sh` 创建服务结构
2. Poetry 自动创建 `.venv` 虚拟环境
3. 安装依赖并配置 VSCode 设置

#### 依赖添加流程

1. 使用 `poetry add <package>` 添加运行时依赖
2. 使用 `poetry add --group dev <package>` 添加开发依赖
3. Poetry 自动更新 `pyproject.toml` 和 `poetry.lock`

#### 环境同步流程

1. `poetry install` - 安装所有依赖（包括开发依赖）
2. `poetry install --only main` - 仅安装运行时依赖
3. `poetry update` - 更新所有依赖到最新兼容版本

## 架构原则

### 1. 模块化设计

- **Monorepo 架构**：统一代码管理，便于协作和依赖管理
- **微服务分离**：独立的音频处理、阿拉伯语文本处理、大语言模型等服务
- **共享包复用**：文档解析、类型定义等跨服务共享
- **分布式模型管理**：每个微服务拥有独立的 `assets/` 目录，实现服务自包含和容器化就绪

## 🏗️ Backend 架构重构 (2024-12-24)

### 架构决策：Clean Architecture + Service Factory Pattern

项目采用了 **Clean Architecture + Service Factory Pattern** 替代原有的 Gateway + Task 模式，实现更好的可扩展性和可维护性。

#### 🎯 重构目标

1. **服务模块化**：每个服务独立模块，便于管理和扩展
2. **通用组件提取**：队列管理、资源管理、断路器等组件可跨服务复用
3. **清晰的关注点分离**：接口、服务、模型、适配器、基础设施分层明确
4. **易于扩展**：添加新服务无需修改现有架构

#### 📁 新架构结构

```
app/
├── shared/                     # 🆕 通用可复用组件
│   ├── interfaces/            # 通用接口 (CallManager, QueueManager, ResourceManager)
│   ├── services/              # 通用服务实现 (Generic implementations)
│   ├── models/                # 通用数据模型 (BaseRequest, ServiceConfig, etc.)
│   └── infrastructure/        # 通用基础设施 (CircuitBreaker, etc.)
├── services/                   # 🔄 服务模块 (按服务分组)
│   └── qwen/                  # Qwen 服务模块
│       ├── interfaces/        # Qwen 特定接口
│       ├── services/          # Qwen 服务实现
│       ├── models/            # Qwen 数据模型
│       ├── adapters/          # Qwen 适配器
│       └── infrastructure/    # Qwen 基础设施 (Factory, Client)
├── infrastructure/            # 应用级基础设施
│   ├── factory.py            # 🔄 更新为通用服务工厂
│   └── container.py          # 依赖注入容器
└── api/                      # API 层 (保持不变)
```

#### 🔄 架构优势

| 方面         | 旧架构 (Gateway + Task) | 新架构 (Clean + Factory) |
| ------------ | ----------------------- | ------------------------ |
| **服务通信** | 集中式网关路由          | 接口驱动的直接通信       |
| **资源管理** | Gateway 统一处理        | 服务特定 + 通用组件      |
| **扩展性**   | 需要修改网关配置        | 添加新服务模块即可       |
| **测试性**   | 依赖网关模拟            | 每个组件独立测试         |
| **维护性**   | 集中式复杂度高          | 分布式，关注点分离       |

#### 🚀 未来服务扩展模式

添加新服务只需遵循相同模式：

```python
# 1. 创建服务模块
app/services/audio/
├── interfaces/audio_service.py
├── services/audio_service.py
├── models/audio_models.py
├── adapters/audio_adapters.py
└── infrastructure/factory.py

# 2. 使用通用组件
from app.shared.services import GenericQueueManager, GenericResourceManager
from app.shared.models import ServiceConfig

# 3. 注册到主工厂
factory.get_service_factory("audio").create_audio_service()
```

#### 📋 已移除组件

- ❌ `app/core/gateway_manager.py` - 未使用的 API 网关
- ❌ `app/tasks/` - 空的异步任务目录
- ❌ 旧的平铺式服务文件 - 已重组到服务模块中

### 2. 技术栈选择

#### 前端

- **Next.js 14 (App Router)**：现代化的 React 框架
- **TypeScript**：类型安全和开发体验
- **Tailwind CSS**：实用优先的 CSS 框架
- **pnpm workspaces**：高效的包管理

#### 后端

- **FastAPI**：高性能的 Python Web 框架
- **ORM**: SQLModel
- **数据库驱动**: asyncpg(PostgreSQL)
- **数据库迁移**: Alembic
- **PostgreSQL**：可靠的关系型数据库
- **Redis**：缓存和任务队列
- **Poetry**：现代化的 Python 依赖管理

#### AI/ML 服务

- **WhisperX**：音频转录和对齐
- **Camel Tools**：阿拉伯语 NLP 处理
- **Qwen**：大语言模型（使用 GGUF 格式）

#### 开发工具

- **Ruff**：现代化 Python 代码检查和格式化
- **MyPy**：Python 静态类型检查
- **Pytest**：Python 测试框架
- **VSCode Multi-Root Workspace**：多服务开发环境

### 3. 开发环境特点

#### 本地开发优先

- **环境策略**: 本地开发为主，Docker 仅用于生产部署
- **网络配置**: 支持中国网络环境，配置 SOCKS5 代理和镜像源
- **依赖管理**: conda base + Poetry .venv 的混合环境策略

#### 代码质量保证

- **统一工具链**: Ruff 替代传统多工具组合，提升性能和一致性
- **配置集中**: pyproject.toml 作为配置中心，避免配置文件分散
- **自动化检查**: VSCode 集成自动格式化和代码检查

### 4. 功能模块

**图书馆**：阿拉伯语相关图书的检索系统，用户搜索图书，提供各种格式的电子书下载。图书源文件存放在 COS。同时可以查询到有声书
**查词查句**：在转录分段表和单词表中查询，用户输入要查询的单词或句子，返回句子，如果是单词，返回句子并高亮句子中的单词。在句子表中创建对转录文本的向量表示，使用向量索引查询句子。单词表中存放单词的词根和词干表示，如用户查询单词，使用词干匹配的。利用转录后分段的时间戳提供发音功能，利用 qwen 附带上下文分段提供翻译功能。
**转录**：用户上传音频文件，系统自动转录，并提供转录文本的编辑功能。
**字幕检索**：用户输入要查询的句子，返回句子所处的音频或者视频片段
**有声书**：阅读、字幕滚动功能播放控制等等高阶功能。支持评论
**多媒体**：提供多种类型的视频，结合字幕控制，提供高效的语言学习环境。支持评论

### 5. 学习路线

- **基础知识**: 了解 Python 异步编程、继承、多态、架构模式等基础知识
- **后端**: 了解 FastAPI、SQLModel、PostgreSQL、Redis、Poetry 等基础知识
- **前端**: 了解 Next.js、Tailwind CSS、pnpm workspaces 等基础知识
- **阅读源码**: 阅读项目源码，了解项目架构、代码组织方式、依赖管理等
- **项目结构**: 理解项目结构和文件夹组织方式
- **完成微服务设计**
- **完成后端**
