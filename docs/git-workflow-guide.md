# BAYAN阿拉伯语学习平台 - Git工作流程指南

## 概述

本文档描述了BAYAN阿拉伯语学习平台项目的Git工作流程、分支策略和代码提交规范。

## 分支策略

### 主要分支

- **main**: 生产分支，包含稳定的、可部署的代码
- **develop**: 开发分支，包含最新的开发功能，用于集成测试

### 功能分支

- **feature/**: 新功能开发分支
- **fix/**: Bug修复分支
- **hotfix/**: 紧急修复分支
- **release/**: 发布准备分支

### 分支命名规范

```
feature/audio-recognition-enhancement
fix/user-authentication-bug
hotfix/critical-security-patch
release/v1.2.0
```

## 工作流程

### 1. 功能开发流程

```bash
# 1. 从develop分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/your-feature-name

# 2. 开发功能
# ... 编写代码 ...

# 3. 提交代码
git add .
git commit -m "feat(scope): 功能描述"

# 4. 推送到远程仓库
git push origin feature/your-feature-name

# 5. 创建Pull Request到develop分支
```

### 2. Bug修复流程

```bash
# 1. 从develop分支创建修复分支
git checkout develop
git pull origin develop
git checkout -b fix/bug-description

# 2. 修复Bug
# ... 修复代码 ...

# 3. 提交修复
git add .
git commit -m "fix(scope): 修复描述"

# 4. 推送并创建PR
git push origin fix/bug-description
```

### 3. 紧急修复流程

```bash
# 1. 从main分支创建热修复分支
git checkout main
git pull origin main
git checkout -b hotfix/critical-fix

# 2. 修复问题
# ... 修复代码 ...

# 3. 提交修复
git add .
git commit -m "fix: 紧急修复描述"

# 4. 合并到main和develop
git checkout main
git merge hotfix/critical-fix
git checkout develop
git merge hotfix/critical-fix

# 5. 推送更新
git push origin main
git push origin develop
```

## 提交信息规范

### 格式

```
type(scope): description

[optional body]

[optional footer]
```

### 类型说明

- **feat**: 新功能
- **fix**: Bug修复
- **docs**: 文档更新
- **style**: 代码格式调整（不影响功能）
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动
- **perf**: 性能优化
- **ci**: CI/CD相关
- **build**: 构建系统相关

### 范围说明

- **audio**: 音频处理相关
- **backend**: 后端API相关
- **frontend**: 前端界面相关
- **translation**: 翻译服务相关
- **word-analyzer**: 词汇分析相关
- **docker**: Docker配置相关
- **docs**: 文档相关

### 示例

```
feat(audio): 添加阿拉伯语语音识别功能

- 集成Whisper模型进行语音转录
- 支持实时音频流处理
- 添加音频质量检测

Closes #123
```

## 代码审查

### Pull Request要求

1. **标题**: 使用提交信息格式
2. **描述**: 详细说明变更内容和原因
3. **测试**: 包含相关测试用例
4. **文档**: 更新相关文档

### 审查检查清单

- [ ] 代码符合项目编码规范
- [ ] 包含适当的测试用例
- [ ] 文档已更新
- [ ] 没有引入安全漏洞
- [ ] 性能影响可接受
- [ ] 与现有功能兼容

## Git钩子

项目配置了以下Git钩子来确保代码质量：

### pre-commit钩子

- Python语法检查
- TypeScript/JavaScript代码检查
- Docker配置验证
- 敏感信息检测
- 文件大小检查

### commit-msg钩子

- 提交信息格式验证
- 描述长度检查

### 安装钩子

```bash
# 运行钩子安装脚本
./.githooks/setup-hooks.sh
```

## 最佳实践

### 1. 提交频率

- 小而频繁的提交
- 每个提交只包含一个逻辑变更
- 避免大型提交

### 2. 分支管理

- 及时删除已合并的分支
- 保持分支名称简洁明了
- 定期同步develop分支

### 3. 代码同步

```bash
# 定期同步develop分支
git checkout develop
git pull origin develop

# 将develop分支合并到功能分支
git checkout feature/your-feature
git merge develop
```

### 4. 冲突解决

```bash
# 解决合并冲突
git merge develop
# 手动解决冲突
git add .
git commit -m "resolve: 解决合并冲突"
```

## 发布流程

### 1. 准备发布

```bash
# 创建发布分支
git checkout develop
git pull origin develop
git checkout -b release/v1.2.0

# 更新版本号和文档
# ... 更新相关文件 ...

git commit -m "chore: 准备v1.2.0发布"
```

### 2. 发布到生产

```bash
# 合并到main分支
git checkout main
git merge release/v1.2.0

# 创建标签
git tag -a v1.2.0 -m "Release v1.2.0"

# 推送到远程
git push origin main
git push origin v1.2.0

# 合并回develop分支
git checkout develop
git merge release/v1.2.0
git push origin develop
```

## 故障排除

### 常见问题

1. **提交被钩子拒绝**: 检查代码格式和提交信息格式
2. **合并冲突**: 手动解决冲突后重新提交
3. **推送失败**: 检查网络连接和权限设置

### 紧急情况

如果需要绕过钩子（仅在紧急情况下使用）：

```bash
git commit --no-verify -m "emergency: 紧急修复"
```

## 联系方式

如有Git工作流程相关问题，请联系项目维护者。
