# 阿拉伯语学习网站：项目蓝图

本文档旨在阐述一个综合性阿拉伯语学习平台的愿景、功能与技术架构。

## 1. 项目愿景

### 1.1. 目标

创建一个功能全面、交互友好、用户体验卓越的在线平台，服务于各个水平的阿拉伯语学习者。

### 1.2. 目标用户

- **学生**: 在校大学生或自学者。
- **教师**: 为课堂寻找教学资源的教育工作者。
- **职场人士**: 在阿拉伯语国家工作或业务相关的专业人士。
- **爱好者**: 对阿拉伯语及阿拉伯文化充满热情的任何人。

## 2. 核心功能 (面向用户)

本章节详细介绍终端用户可使用的主要模块与工具。

### 2.1. 学习内容模块

一个多维度的内容库，旨在满足不同学习需求和风格。

#### 2.1.1. 阿拉伯语阅读

- **内容分类**: 历史、文学、科普、伊斯兰研究、社会话题、新闻等。
- **内容格式**: 提供纯文本和带音频两种格式，后者将配备高级播放控制功能。
- **内容类型**: 包含文章、新闻报道和完整书籍的丰富组合。

##### 阅读模块 — 技术深潜

- **核心思路："三级表结构 + 直接句子存储"**

  - 采用标准化的关系数据库设计，确保高性能查询和精细的交互能力。
  - **Books → Chapters → Sentences 三级结构**:
    - `books` 表：存储书籍元数据（标题、作者、语言等）
    - `chapters` 表：存储章节信息和结构化数据
    - `sentences` 表：直接存储每个句子的完整内容和元数据
  - **优势**:
    - 查询性能优异，避免 JSON 解析开销
    - 支持复杂的句子级别操作和统计
    - 便于扩展和维护，符合关系数据库最佳实践

- **多级交互功能**:

  - **句子级交互**:
    - 所有交互功能 (如笔记、翻译) 都通过句子表的主键 ID 和 `sentence_id` 字段关联。
    - **数据库设计**:
      - `user_interactions`: 存储用户个人数据，关联到 `sentences` 表 (`user_id`, `sentence_table_id`, `sentence_id`, `type`, `content`, `is_public`)。
      - `sentence_translations`: 存储翻译缓存，关联到 `sentences` 表 (`sentence_table_id`, `sentence_id`, `target_language`, `translation_text`)。
    - **功能实现**:
      - **收藏与笔记**: 用户可以收藏句子或添加私人笔记，笔记可选择性地公开分享。
      - **社区笔记**: 查看其他用户为特定句子分享的所有公开笔记。
      - **按需翻译**: 翻译内容在用户请求时才通过 API 惰性加载，结果缓存到 `sentence_translations` 表。为确保翻译质量，后端会提取相邻句子作为上下文。
  - **单词级交互**:
    - **音频同步单词高亮**: 除句子高亮外，支持单词级播放同步，实时高亮当前朗读的单词。
    - **数据库设计**:
      - 在 `sentences` 表中添加 `words_metadata` JSONB 字段，存储单词级时间戳信息:
        ```json
        [
          {
            "word_id": "c1s1w1",
            "word": "الكتاب",
            "start_pos": 0,
            "end_pos": 6,
            "audio_start": 0.2,
            "audio_end": 0.5
          }
        ]
        ```
      - `word_interactions`: 存储用户对单词级别的交互 (`user_id`, `sentence_id`, `word_id`, `type`, `content`)。
    - **功能实现**:
      - **单词分析整合**: 阅读界面中，用户可直接点击任意单词，调用单词分析器功能，在侧边栏展示词根、词干和词性等分析结果。
      - **单词收藏与笔记**: 用户可为特定单词添加个人笔记或加入生词本，便于后续复习。
      - **自定义交互偏好**: 用户可在设置中选择默认交互级别（句子级或单词级），以及音频同步高亮模式。

- **文末辅助学习**:
  - 每篇文章末尾将提供重点词汇、语法结构和关键句型的总结分析。

#### 2.1.2. 阿拉伯语语法

- 一个专门的语法学习版块，提供从基础到高级的结构化课程 (例如：名词、动词、格位、正偏组合、时态等)。

#### 2.1.3. 阿拉伯语影音

- 一个精心策划的视频资料库，旨在提升听力理解和文化认知。
- **内容**: 电影、纪录片、电视剧和短片。

#### 2.1.4. 教材

- 一个汇集了来自不同国家（如中国、沙特、埃及）的知名阿拉伯语教材的数字资源库。
- 每本教材都将附有下载链接及其内容和教学法的精炼摘要。

### 2.2. 学习工具

一套由人工智能驱动的强大辅助工具。

- **音频转录**: 转写用户上传的音频文件。此功能是 `3.2` 节中描述的 `audio-processor` 核心服务的直接、轻量级应用。
- **单词分析器**: 利用 `camel-tools` 对任意单词进行深度形态分析，揭示其词根、词条、词干和词性。
- **元音符助手**:
  - **添加元音符**: 采用专门的机器学习模型，为无元音符的文本精确添加符号。
  - **去除元音符**: 提供一个简单脚本，去除元音符以供阅读练习。
- **方言-标准语转换器**: 一个由 `qwen-32b` 大语言模型 API 驱动的工具，可将方言文本转写为现代标准阿拉伯语 (MSA)。它能智能地将长文本分块处理，同时保留上下文。
- **罗马化转写引擎**: 使用 `camel-tools` 提供多种阿拉伯字母的罗马化转写标准 (如 Buckwalter, ALA-LC, SATTS)。
- **日历转换器**: 在伊斯兰历、公历和农历之间轻松切换。
- **文档翻译**:
  - **核心策略**: 此工具将复用为核心内容处理流程所构建的统一文档解析服务。
  - **工作流程**:
    1.  **解析与结构化**: 用户上传的文档 (如 `.docx`) 被解构成一个保留其原始布局（标题、段落、图片）的结构化 JSON "蓝图"。
    2.  **翻译**: JSON 中每个文本块的内容被发送到 `qwen-32b` 模型进行翻译。
    3.  **重构**: 翻译后的文本替换 JSON "蓝图"中的原文。系统根据更新后的蓝图，重建一个保留原始格式的全新 `.docx` 文档。

### 2.3. 学习社区

- 一个论坛或问答区，学习者可在此提问、分享学习笔记，并从同学、教师或高级水平用户那里获得帮助。

## 3. 技术架构与系统设计

本章节涵盖底层技术栈、数据处理流水线和系统级设计模式。

### 3.1. 技术栈

- **前端**: Next.js
- **后端**: FastAPI (Python)
- **数据库**: PostgreSQL
- **缓存与任务队列**: Redis
- **设计哲学**: 优先采用模块化架构，以确保可扩展性并简化未来的功能迭代。

### 3.1.1. 模块化设计与代码复用

Monorepo 结构中的 `packages/` 目录是为支持模块化和代码复用而设计的，可以进一步扩展以支持共享逻辑：

```plaintext
/packages
├── frontend/          # Next.js 前端应用
├── backend/           # FastAPI 后端应用
├── shared/            # 共享逻辑包
│   └── i18n/          # 国际化资源共享包
├── services/          # 独立微服务
│   ├── audio-processor/  # 音频处理服务
│   ├── camel-tools-api/  # Camel-Tools 阿拉伯语处理服务
│   └── qwen-service/     # Qwen 大语言模型服务
└── types/             # 共享 TypeScript 类型
```

这种结构能够解决文档解析和 camel-tools 等逻辑在不同模块间复用的问题，同时保持明确的职责分离。

### 3.1.2. 阅读模块流程图

下图展示了阅读模块的组件交互和共享服务的使用方式：

```mermaid
graph TD
    subgraph "前端 (frontend)"
        A[阅读页面组件] --> B[内容渲染引擎]
        B --> C[句子渲染器]
        B --> D[单词渲染器]
        C --> E[句子交互控制器]
        D --> F[单词交互控制器]
        E --> G[笔记系统]
        F --> H[单词分析面板]
        H --> I[单词API客户端]
    end

    subgraph "共享模块 (shared)"
        L[camel-tools-api客户端] <--> M[camel-tools接口]
    end

    subgraph "后端 (backend)"
        N[内容API] --> O[内容服务]
        O --> P[数据库适配器]
        Q[单词分析API] --> R[单词分析服务]
        R --> S[camel-tools适配器]
    end

    subgraph "微服务 (services)"
        T[word-analyzer服务] --> U[camel-tools集成]
    end

    I <--> L
    R <--> M
    Q <--> T

    style L fill:#f9f,stroke:#333,stroke-width:2px
    style M fill:#f9f,stroke:#333,stroke-width:2px
```

### 3.1.3. 学习工具模块流程图

下图展示了学习工具模块的组件交互和共享服务的使用方式：

```mermaid
graph TD
    subgraph "前端 (frontend)"
        A1[工具选择界面] --> B1[单词分析器UI]
        A1 --> C1[文档翻译UI]
        A1 --> D1[音频转录UI]
        B1 --> E1[单词API客户端]
        C1 --> F1[文档处理客户端]
        D1 --> G1[音频处理客户端]
    end

    subgraph "共享模块 (shared)"
        J1[camel-tools-api客户端] <--> K1[camel-tools接口]
    end

    subgraph "后端 (backend)"
        L1[工具API路由器] --> M1[单词分析控制器]
        L1 --> N1[文档翻译控制器]
        L1 --> O1[音频转录控制器]
        M1 --> P1[单词处理服务]
        N1 --> Q1[文档处理服务]
        O1 --> R1[音频处理服务]
    end

    subgraph "微服务 (services)"
        S1[word-analyzer服务] --> T1[camel-tools集成]
        U1[audio-processor服务] --> V1[WhisperX集成]
    end

    E1 <--> L1
    F1 <--> N1
    G1 <--> O1
    P1 <--> J1
    M1 <--> S1
    O1 <--> U1

    style J1 fill:#f9f,stroke:#333,stroke-width:2px
    style K1 fill:#f9f,stroke:#333,stroke-width:2px
```

### 3.1.4. 共享服务复用模式

以下图表展示了核心共享服务如何被不同模块复用：

```mermaid
graph TD
    subgraph "前端功能"
        A2[阅读页面] --> D2[单词级交互]
        B2[工具页面] --> E2[单词分析工具]
        C2[翻译页面] --> F2[文档翻译工具]
        B2 --> F3[音频转录工具]
    end

    subgraph "共享逻辑与服务"
        H2[camel-tools-api]
        I3[audio-processor 服务]
    end

    subgraph "后端与内容流水线"
        I2[Chapter处理]
        J2[单词分析服务]
        K2[文档翻译服务]
        L3[内容处理流水线 B]
    end

    D2 -->|使用| H2
    E2 -->|使用| H2
    I2 -->|使用| H2
    F2 -->|使用| G2
    K2 -->|使用| G2
    F3 -->|使用| I3
    L3 -->|使用| I3

    style G2 fill:#f96,stroke:#333,stroke-width:4px
    style H2 fill:#96f,stroke:#333,stroke-width:4px
    style I3 fill:#6cf,stroke:#333,stroke-width:4px
```

### 3.2. 统一内容处理流水线

一个统一的、自动化的后台工作流，旨在灵活处理各种输入组合，并将其标准化为平台可用的丰富内容。采用新的三级表结构（Books → Chapters → Sentences）。注意：音频处理服务已简化，移除了章节检测功能，专注于高质量的阿拉伯语转录。

#### 3.2.1. 核心设计原则

- **三级表结构**: 采用 Books → Chapters → Sentences 的标准化关系数据库设计
- **直接句子存储**: 每个句子作为独立记录存储，避免 JSON 解析开销
- **简化音频处理**: audio-processor 服务专注于高质量阿拉伯语转录，不包含章节检测
- **章节处理**: 章节检测和分割功能由其他服务或手动处理实现
- **灵活处理模式**: 支持将整个音频内容作为单一章节处理

#### 3.2.2. 整体流程图

```mermaid
graph TD
    UserInput1[A: 音频 + 章节标记]
    UserInput2[B: 纯音频]
    UserInput3[C: 纯文档]

    StepA1[Audio Processor: 转录音频]
    StepA2[Chapter Detector: 检测章节标记]
    DecisionA{启用章节检测?}
    StepA3[Audio Splitter: 分割音频和文本]

    StepB1[Audio Processor: 转录音频]
    DecisionB{启用章节检测?}

    StepC2[Chapter Detector: 检测章节]
    DecisionC{启用章节检测?}

    DatabaseA[存储到三级表结构]
    DatabaseB[存储为单一章节]

    UserInput1 --> StepA1
    StepA1 --> DecisionA
    DecisionA -->|是| StepA2
    DecisionA -->|否| DatabaseB
    StepA2 --> StepA3
    StepA3 --> DatabaseA

    UserInput2 --> StepB1
    StepB1 --> DecisionB
    DecisionB -->|是| StepA2
    DecisionB -->|否| DatabaseB

    UserInput3 --> StepC1
    StepC1 --> DecisionC
    DecisionC -->|是| StepC2
    DecisionC -->|否| DatabaseB
    StepC2 --> DatabaseA
```

#### 3.2.3. 工作流 A: 音频转录处理（主要工作流）

此工作流处理用户提供音频文件的场景，通过 WhisperX 转录获得准确的文本和时间戳。

1.  **音频转录**:

    - **Audio Processor** 执行音频转录：
      - 调用 `WhisperX` 进行"闭卷识别"，获取完整的转录文本
      - 生成单词级和句子级的精确时间戳
      - 输出带时间戳的结构化转录结果
    - **产出**: 转录文本和完整的时间戳信息。

2.  **章节处理选项**:

    - **用户控制选项**: 系统提供三种章节处理模式：
      - **章节标记模式**: 用户提供章节标题列表，系统在转录文本中精确匹配
      - **时间戳模式**: 用户提供精确的时间戳列表，直接按时间戳分割（推荐）
      - **跳过章节**: 将整个音频作为单一章节处理，返回完整音频

3.  **章节边界确定** (启用章节处理时):

    采用高级章节检测算法，支持多种检测模式，确保准确识别章节边界。

    ##### 基于用户标记的检测（推荐方案）

    - **适用场景**: 所有文本类型，用户明确知道章节结构
    - **检测方式**:
      - **章节标记列表**: 用户提供准确的章节标题列表
      - **时间戳列表**: 用户提供精确的章节边界时间戳
    - **优势**:
      - **准确性高**: 避免模式匹配的误判和随机性
      - **用户控制**: 用户完全掌控章节划分逻辑
      - **简化系统**: 减少复杂的检测算法，提高稳定性

    ##### 章节处理的两种模式

    **模式一：基于章节标记的检测（三阶段检测算法）**

    - **输入**:

      1. `WhisperX` 转录的带词级时间戳的文本
      2. 用户提供的章节标题列表 (例如：`["السعودي", "السلام عليكم"]`)

    - **三阶段检测算法**:

      1. **阶段一：数据预处理**

         - 对转录文本和章节标题进行预处理（去元音符、标准化字母、去标点）
         - 使用 camel-tools-api 进行文本标准化和提取词干
         - 准备文本用于精确匹配

      2. **阶段二：模式匹配**

         - **单个分段内匹配**: 检查章节标记是否完整包含在单个 WhisperX 输出文本段内
         - **独立段匹配**: 对每个文本段单独分析，寻找完全匹配的章节标记
         - **跨段匹配**: 当一个章节标记可能跨越多个连续文本段时，向前合并最多 3 个文本段并进行匹配
         - **比较方法**: 使用词干序列比较，保持词序，处理语法或拼写变体
         - **匹配后处理**: 合并匹配的文本段，修正时间戳

      3. **阶段三：语义验证**

         - **使用 qwen-service-api**: 对每个潜在匹配项进行验证
         - **上下文分析**: 分析匹配文本的前后上下文
         - **语义角色判定**: 确认文本在语义上是否确实扮演章节标题角色
         - **置信度筛选**: 只有高置信度匹配才被确认为章节边界

      4. **结果处理**:
         - **提取时间戳**: 使用确认章节标题的第一个单词的开始时间戳作为章节边界
         - **生成章节列表**: 创建包含标题、时间戳和内容的结构化章节列表

    **模式二：基于时间戳的分割（推荐）**

    - **输入**: 用户提供的精确时间戳列表
    - **处理流程**:
      1. **直接分割**: 根据时间戳直接分割音频文件
      2. **自动命名**: 按顺序命名为"第一章"、"第二章"等
      3. **文本同步**: 如果有转录文本，同步分割文本内容
    - **优势**:

      - **精确可控**: 用户完全控制章节边界
      - **处理简单**: 避免复杂的文本检测算法
      - **稳定可靠**: 不依赖文本匹配的不确定性

    - **产出**: 精确的章节边界时间戳列表和章节标题。

4.  **音频和文本分割**:

    - **Audio Splitter** 执行同步分割：
      - 根据章节边界时间戳分割原始音频文件
      - 同步分割转录文本，确保文本-音频对应关系
      - 为每个章节生成独立的音频文件和文本内容
    - **产出**: 按章节划分的音频文件和对应的文本内容。

5.  **数据存储**:

    - 将处理结果存储到三级表结构：
      - `books` 表：存储音频书籍信息
      - `chapters` 表：存储章节信息和分割后的音频文件路径
      - `sentences` 表：存储转录的句子内容和时间戳信息

#### 3.2.4. 工作流 B: 纯文档处理

此工作流处理用户只提供文档文件的场景，用于翻译和学习目的。根据文档类型，采用不同的章节检测策略。

1.  **章节检测与划分**:

    - **用户控制选项**: 系统提供章节处理选项：

      - **自动检测**: 基于文档结构（TOC、标题样式）自动检测章节
      - **手动指定**: 用户提供章节标记列表或章节边界
      - **跳过章节**: 将整个文档作为单一章节处理

    - **根据文档类型和用户选择的章节检测模式**:

      ##### A. 结构化文档（DOCX、EPUB 等）的章节检测

      - **自动检测模式**:
        - 直接从文档元数据中提取章节信息（目录、标题样式、PDF 书签等）
        - 无需复杂的文本匹配算法，依赖文档内置结构
        - 高效且准确，是结构化文档的首选方式

      ##### B. 纯文本文档的章节检测

      - **手动指定模式 (采用三阶段检测算法)**:

        - **输入**: 用户提供的章节标题列表
        - **处理流程**:
          1. **文本标准化**: 使用 camel-tools-api 对文本和章节标题进行预处理（去元音符、标准化字母）
          2. **模式匹配**: 使用词干提取进行精确匹配，保持词序比较，智能处理可能的细微变体
          3. **语义验证**: 使用 qwen-service-api 确认潜在章节标记的有效性，过滤误报
        - **输出**: 章节边界位置（标题、起始位置、结束位置）

      - **跳过章节处理**:
        - 将整个文档作为单一章节处理
        - 适用于短文档或不需要章节划分的场景

    - **产出**: 章节列表和每个章节的文本内容。

2.  **数据存储**:

    - 将处理结果存储到三级表结构：
      - `books` 表：存储书籍基本信息
      - `chapters` 表：存储章节信息（无音频文件）
      - `sentences` 表：存储每个句子的内容和位置信息（无时间戳）

#### 3.2.6. 最终产物与数据库设计

##### 3.2.6.1. 统一产出结构

无论通过哪个工作流，最终产物都存储在标准化的三级表结构中：

```sql
-- Books 表（顶级）
CREATE TABLE books (
    id SERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    author VARCHAR(255),
    language VARCHAR(10) DEFAULT 'ar',
    format VARCHAR(20) NOT NULL,
    total_word_count INTEGER,
    chapter_count INTEGER DEFAULT 0,
    sentence_count INTEGER DEFAULT 0,
    custom_properties JSONB,
    table_of_contents JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Chapters 表（中级）
CREATE TABLE chapters (
    id SERIAL PRIMARY KEY,
    book_id INTEGER NOT NULL REFERENCES books(id) ON DELETE CASCADE,
    chapter_number INTEGER NOT NULL,
    title VARCHAR(500),
    word_count INTEGER,
    sentence_count INTEGER DEFAULT 0,
    audio_file_path TEXT,
    audio_start_time FLOAT,
    audio_end_time FLOAT,
    elements JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(book_id, chapter_number)
);

-- Sentences 表（底级）
CREATE TABLE sentences (
    id SERIAL PRIMARY KEY,
    chapter_id INTEGER NOT NULL REFERENCES chapters(id) ON DELETE CASCADE,
    book_id INTEGER NOT NULL REFERENCES books(id) ON DELETE CASCADE,
    sentence_number INTEGER NOT NULL,
    sentence_id VARCHAR(100) NOT NULL UNIQUE,
    content TEXT NOT NULL,
    start_position INTEGER NOT NULL,
    end_position INTEGER NOT NULL,
    word_count INTEGER,
    audio_start_time FLOAT,
    audio_end_time FLOAT,
    words_metadata JSONB,
    difficulty_level INTEGER,
    has_translation BOOLEAN DEFAULT FALSE,
    has_audio BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(chapter_id, sentence_number)
);
```

##### 3.2.6.2. 数据关系和约束

- **一对多关系**: Books → Chapters → Sentences
- **级联删除**: 删除书籍时自动删除相关章节和句子
- **唯一性约束**: 确保章节编号和句子编号在各自范围内唯一
- **冗余字段**: `sentences.book_id` 便于跨章节查询
- **JSONB 字段**: 存储复杂结构化数据（TOC、元素、单词元数据等）

#### 3.2.7. 音频分割与章节处理技术细节

##### 3.2.7.1. 音频分割技术

- **工具**: 使用 FFmpeg 进行精确的音频切割
- **分割策略**:
  ```bash
  ffmpeg -i input.mp3 -ss [start_time] -t [duration]
         -af "afade=t=in:ss=0:d=0.1,afade=t=out:st=[end-0.1]:d=0.1"
         -acodec libmp3lame -ab 128k output_chapter.mp3
  ```
- **质量保证**:
  - 添加淡入淡出效果，避免音频突变
  - 保持原始音频质量和格式
  - 精确到毫秒级的时间戳切割

##### 3.2.7.2. 章节音频文件管理

- **文件命名规范**:
  ```
  {book_title}_chapter_{chapter_number:02d}_{chapter_title}.mp3
  例如: 阿拉伯历史_chapter_01_古代文明.mp3
  ```
- **存储结构**:
  ```
  /audio/books/{book_id}/
  ├── original.mp3              # 原始完整音频
  ├── chapters/
  │   ├── chapter_01.mp3       # 第一章音频
  │   ├── chapter_02.mp3       # 第二章音频
  │   └── ...
  └── metadata.json            # 音频元数据
  ```

##### 3.2.7.3. 错误处理和降级策略

- **章节检测失败**: 自动降级为单一章节模式
- **音频分割失败**: 保留完整音频，记录错误日志
- **时间戳不准确**: 提供手动调整接口
- **文件损坏**: 自动重试机制和备份策略

### 3.3. 异步任务处理 (优先级队列架构)

为了保证核心功能的即时响应性不受耗时操作的影响，系统将采用基于优先级队列的异步任务处理架构。

- **核心思想**: 将任务按其对用户体验的即时性要求，划分为"实时任务"和"批量任务"，并由不同的处理管道执行。
- **任务分类**:

  - **实时任务 (High Priority)**: 例如阅读模式中的单句翻译。这类任务要求极低的延迟，必须被立即处理。
  - **批量任务 (Low Priority)**: 例如"文档翻译"、"音频转录"等资源密集型操作。用户可以接受在后台等待其完成。

- **实现**:
  - **双队列系统**: 使用 Redis 管理两个独立的任务队列：`high_priority_queue` 和 `low_priority_queue`。
  - **专属工人 (Workers)**:
    - **实时工人**: 启动少量专门监听 `high_priority_queue` 的 Worker 进程，确保这些任务总能抢占到计算资源。API 请求会同步等待这类任务完成。
    - **批量工人**: 启动多个监听 `low_priority_queue` 的 Worker 进程，在后台持续处理耗时任务。API 请求会立即返回"任务已提交"的响应，不阻塞用户。
- **用户通知**: 长时间运行的批量任务完成后，将通过 WebSocket 或电子邮件通知用户。

### 3.4. 章节处理 API 设计

#### 3.4.1. 核心 API 端点

##### 音频转录与章节处理（主要 API）

```http
POST /api/v1/audio/transcribe
Content-Type: multipart/form-data

{
  "audio": [音频文件],
  "language": "ar", // 固定为阿拉伯语
  "vad_onset": 0.5, // 可选：语音活动检测阈值
  "vad_offset": 0.363, // 可选：语音活动检测偏移
  "chunk_size": 30, // 可选：处理块大小（秒）
  "enable_diarization": false, // 可选：启用说话人分离
  "min_speakers": null, // 可选：最小说话人数
  "max_speakers": null, // 可选：最大说话人数

  // 章节处理选项（新增）
  "chapter_detection": {
    "enabled": true, // 是否启用章节检测
    "mode": "markers|timestamps|none", // 章节检测模式
    "chapter_markers": ["الفصل الأول", "الفصل الثاني"], // markers模式需要
    "timestamps": [0, 120.5, 360.2, 500.8], // timestamps模式需要
    "return_audio_chunks": true // 是否返回分割后的音频文件（base64编码）
  }
}

// 响应内容增加章节信息和音频块
```

##### 纯文档处理

```http
POST /api/v1/content/process-document
Content-Type: multipart/form-data

{
  "document": [文档文件],
  "chapter_detection": {
    "enabled": true,
    "method": "auto|manual",
    "chapter_markers": ["第一章", "第二章"] // manual模式时必需
  },
  "language": "ar"
}
```

##### 章节检测

```http
POST /api/v1/content/detect-chapters
{
  "text": "阿拉伯语文档文本内容",
  "detection_method": "structure|manual", // structure: 基于文档结构, manual: 用户提供标记
  "chapter_markers": ["الفصل الأول", "الفصل الثاني"], // manual 模式必需
  "language": "ar" // 固定为阿拉伯语
}
```

##### 音频章节分割

```http
POST /api/v1/audio/split-by-chapters
{
  "audio_file": "音频文件路径",
  "chapter_boundaries": [
    {
      "chapter_number": 1,
      "title": "第一章",
      "start_time": 0.0,
      "end_time": 120.5
    }
  ],
  "output_format": "mp3",
  "fade_duration": 0.1,
  "return_audio_data": true // 是否直接返回音频数据（base64编码）
}
```

#### 3.4.2. 数据查询 API

##### 获取书籍信息

```http
GET /api/v1/books/{book_id}
GET /api/v1/books/{book_id}/chapters
GET /api/v1/books/{book_id}/chapters/{chapter_id}/sentences
```

##### 句子级查询

```http
GET /api/v1/sentences/{sentence_id}
GET /api/v1/sentences/search?q={query}&book_id={book_id}
```

### 3.5. 国际化 (i18n)

- **目标**: 使平台能服务全球用户。
- **支持语言**: 中文 (zh)、英文 (en)、阿拉伯文 (ar)、日文 (ja)、韩文 (ko)、马来文 (ms)、印地文 (hi) 和乌尔都文 (ur)。
- **框架**: `next-i18next`
- **功能**:
  - 语言特定的 URL (例如 `/en/`, `/zh/`)，并根据浏览器设置自动检测。
  - 完全翻译的 UI 文本。
  - 多语言 SEO (站点地图、meta 标签)。
  - 语言特定的静态资源 (图片、文档)。
- **`next.config.js` 示例**:
  ```js
  // next.config.js
  module.exports = {
    i18n: {
      locales: ["en", "zh", "ar", "ms", "ja", "ko", "hi", "ur"],
      defaultLocale: "en",
      localeDetection: true, // 根据浏览器语言自动重定向
    },
  };
  ```

### 3.6. SEO 策略 (搜索引擎优化)

- **`robots.txt`**: 在 `frontend/public` 目录下提供一个 `robots.txt` 文件，以指导搜索引擎爬虫的行为（例如，禁止抓取 API 路由或用户私人页面），并指定 `sitemap.xml` 的位置。
- **`sitemap.xml`**: 通过 Next.js 的服务器端功能（如 API 路由或 `generateSitemaps` 功能）动态生成，确保所有公开可访问的学习内容（文章、书籍、教材摘要）都被收录。
- **自动更新通知**: 内容更新后，自动向搜索引擎 (如 Google, Bing) 发送 ping 通知，促使其重新抓取站点地图。
- **其他最佳实践**: 实施结构化数据 (Schema.org)、清晰的 URL 结构、以及为所有页面生成唯一的 `title` 和 `meta` 描述。

## 4. 项目目录结构

为了保证项目的可维护性和扩展性，我们采用基于 **Monorepo (单体仓库)** 的目录结构，遵循行业最佳实践。

这种结构将前后端代码并置于同一仓库，使用 `pnpm` 作为包管理器，以高效管理 `workspaces`。其核心优势在于：

- **简化依赖管理**: 便于管理跨项目的共享代码或类型定义。
- **统一开发流程**: 可在单一工作流中同时启动和调试前后端服务。
- **原子化提交**: 相关联的功能变更（例如，一个后端 API 和使用它的前端页面）可以在一次提交中完成，使版本历史更清晰。

## 5. 蓝图更新总结

### 5.1. 核心架构变更

本次蓝图更新对系统架构进行了重大优化，主要变更包括：

#### 5.1.1. 数据库设计革新

- **从"魔法索引"到"三级表结构"**:

  - 废弃了复杂的 `full_text + sentences_meta JSONB` 混合方案
  - 采用标准化的 **Books → Chapters → Sentences** 三级关系表设计
  - 每个句子作为独立记录存储，避免 JSON 解析开销

- **性能优势**:
  - 查询性能大幅提升，避免复杂的 JSON 操作
  - 支持高效的全文搜索和句子级别索引
  - 便于数据统计和聚合查询

#### 5.1.2. 章节处理能力增强

- **智能章节检测**:

  - 支持基于文档结构的自动检测（TOC、标题样式）
  - 支持基于模式匹配的检测（阿拉伯语和英语章节标记）
  - 支持用户手动指定章节边界

- **音频反向分割**:
  - 根据检测到的章节边界，精确分割音频文件
  - 使用 FFmpeg 进行高质量音频切割
  - 支持淡入淡出效果，确保音频质量

#### 5.1.3. 处理流程优化

- **统一工作流**:

  - 工作流 A: 文档 + 音频处理
  - 工作流 B: 纯音频处理
  - 工作流 C: 纯文档处理

- **灵活配置**:
  - 用户可选择是否启用章节检测
  - 支持跳过章节处理，作为单一章节存储
  - 提供多种章节检测方式选择

### 5.2. 技术实现亮点

#### 5.2.1. 优化的章节处理算法

- **用户控制优先**: 移除不可靠的模式匹配，采用用户明确指定的方式
- **两种处理模式**: 章节标记匹配和时间戳直接分割
- **专注阿拉伯语**: 针对阿拉伯语优化，使用 `camel-tools` 进行词汇匹配和文本标准化
- **降级策略**: 检测失败时自动返回完整音频，避免错误分割

#### 5.2.2. 音频处理技术

- **精确分割**: 毫秒级精度的音频切割
- **质量保证**: 自动添加淡入淡出效果
- **文件管理**: 规范化的音频文件命名和存储结构

#### 5.2.3. API 设计完善

- **RESTful 设计**: 清晰的资源路径和 HTTP 方法
- **灵活配置**: 丰富的参数选项支持不同使用场景
- **错误处理**: 完善的降级策略和错误恢复机制

### 5.3. 系统优势

#### 5.3.1. 性能提升

- **查询效率**: 直接句子存储比 JSON 查询快 3-5 倍
- **存储优化**: 避免数据冗余，减少存储空间
- **索引效率**: 支持复杂的多表联合查询和全文搜索

#### 5.3.2. 扩展性增强

- **模块化设计**: 章节检测、音频分割等功能独立可扩展
- **配置灵活**: 支持多种处理模式和用户自定义选项
- **向后兼容**: 保持现有 API 的兼容性

#### 5.3.3. 维护性改善

- **标准化设计**: 遵循关系数据库最佳实践
- **清晰架构**: 职责分离，代码组织清晰
- **文档完善**: 详细的 API 文档和技术说明

### 5.4. 下一步实施建议

1. **数据迁移**: 制定从现有"魔法索引"到三级表结构的迁移方案
2. **功能开发**: 优先实现章节检测和音频分割核心功能
3. **测试验证**: 全面测试各种文档格式和音频处理场景
4. **性能优化**: 针对大文件和复杂章节结构进行性能调优
5. **用户体验**: 设计直观的章节处理配置界面

这次架构更新为阿拉伯语学习平台奠定了更加坚实的技术基础，将显著提升系统的性能、可扩展性和用户体验。
