# 本地监控环境设置指南

## 🎯 概述

这个指南将帮你设置一个完全本地化的监控环境，所有服务都直接在你的 Ubuntu 机器上运行，无需 Docker。

## 📋 系统要求

- Ubuntu 18.04+ 
- Python 3.8+
- Node.js 16+
- 至少 4GB RAM
- 至少 10GB 可用磁盘空间

## 🚀 快速开始

### 1. 一键设置监控环境

```bash
# 给脚本执行权限
chmod +x scripts/setup-local-monitoring.sh

# 运行设置脚本
./scripts/setup-local-monitoring.sh
```

这个脚本会自动安装和配置：
- PostgreSQL
- Redis
- Prometheus
- Grafana
- Node Exporter

### 2. 启动所有服务

```bash
# 给脚本执行权限
chmod +x scripts/start-local-services.sh

# 启动所有服务
./scripts/start-local-services.sh
```

### 3. 停止所有服务

```bash
# 给脚本执行权限
chmod +x scripts/stop-local-services.sh

# 停止所有服务
./scripts/stop-local-services.sh
```

## 📊 监控界面访问

设置完成后，你可以访问：

- **Grafana 监控面板**: http://localhost:3000
  - 默认用户名: `admin`
  - 默认密码: `admin`
  - 首次登录会要求更改密码

- **Prometheus 指标查询**: http://localhost:9090
  - 可以查看原始指标数据
  - 测试查询语句

- **Node Exporter 系统指标**: http://localhost:9100
  - 系统级别的监控数据

## 🔧 为微服务添加监控端点

### FastAPI 服务监控

在你的 FastAPI 应用中添加以下代码：

```python
# 在 main.py 或相应的路由文件中添加

from datetime import datetime
import psutil
import time

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "your-service-name"
    }

@app.get("/metrics")
async def metrics():
    """Prometheus 格式的指标端点"""
    # 基本的系统指标
    cpu_percent = psutil.cpu_percent()
    memory = psutil.virtual_memory()
    
    metrics = f"""
# HELP service_cpu_usage_percent CPU usage percentage
# TYPE service_cpu_usage_percent gauge
service_cpu_usage_percent {cpu_percent}

# HELP service_memory_usage_bytes Memory usage in bytes
# TYPE service_memory_usage_bytes gauge
service_memory_usage_bytes {memory.used}

# HELP service_memory_usage_percent Memory usage percentage
# TYPE service_memory_usage_percent gauge
service_memory_usage_percent {memory.percent}

# HELP service_uptime_seconds Service uptime in seconds
# TYPE service_uptime_seconds counter
service_uptime_seconds {time.time()}
"""
    
    return Response(content=metrics, media_type="text/plain")
```

### 安装依赖

```bash
# 在每个微服务目录中安装监控依赖
pip install psutil
```

## 🗄️ 数据库配置

### PostgreSQL 配置

设置脚本会自动创建：
- 用户: `arabic_user`
- 密码: `arabic_password`
- 数据库: `arabic_learning`

你可以在你的应用配置中使用：
```python
DATABASE_URL = "postgresql://arabic_user:arabic_password@localhost:5432/arabic_learning"
```

### Redis 配置

Redis 会在默认端口 6379 运行，你可以在应用中使用：
```python
REDIS_URL = "redis://localhost:6379/0"
```

## 📈 Grafana 仪表板

### 导入预设仪表板

1. 访问 http://localhost:3000
2. 登录 Grafana
3. 点击 "+" -> "Import"
4. 输入仪表板 ID:
   - Node Exporter: `1860`
   - PostgreSQL: `9628`
   - Redis: `763`

### 创建自定义仪表板

为你的微服务创建自定义监控面板：

1. 点击 "+" -> "Dashboard"
2. 添加面板
3. 使用 Prometheus 查询语句，例如：
   - `service_cpu_usage_percent` - CPU 使用率
   - `service_memory_usage_percent` - 内存使用率
   - `rate(http_requests_total[5m])` - HTTP 请求速率

## 🔍 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查端口占用
   sudo netstat -tlnp | grep :8000
   
   # 检查服务状态
   sudo systemctl status prometheus
   sudo systemctl status grafana-server
   ```

2. **Prometheus 无法抓取指标**
   - 确保微服务的 `/metrics` 端点可访问
   - 检查防火墙设置
   - 查看 Prometheus 日志: `sudo journalctl -u prometheus`

3. **Grafana 无法连接 Prometheus**
   - 确保 Prometheus 在 9090 端口运行
   - 检查数据源配置

### 日志查看

```bash
# 查看微服务日志
tail -f logs/backend.log
tail -f logs/audio-processor.log

# 查看系统服务日志
sudo journalctl -u prometheus -f
sudo journalctl -u grafana-server -f
```

## 🔄 日常使用

### 开发工作流

1. **启动开发环境**:
   ```bash
   ./scripts/start-local-services.sh
   ```

2. **开发和测试**:
   - 修改代码
   - 服务会自动重载（使用 --reload 参数）
   - 在 Grafana 中监控性能

3. **停止环境**:
   ```bash
   ./scripts/stop-local-services.sh
   ```

### 性能监控

- 在 Grafana 中设置告警规则
- 监控关键指标：CPU、内存、响应时间
- 定期检查日志文件

## 📝 下一步

1. 为每个微服务添加更详细的业务指标
2. 设置告警规则
3. 配置日志聚合（可选择添加 Loki）
4. 创建自动化测试脚本
