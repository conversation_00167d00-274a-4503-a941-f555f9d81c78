// 内容相关类型定义

export interface Content {
  id: number;
  title: string;
  full_text: string;
  sentences_meta: SentenceMeta[];
  content_type: ContentType;
  language: string;
  audio_url?: string;
  created_at: string;
  updated_at: string;
  metadata?: ContentMetadata;
}

export interface SentenceMeta {
  sentence_id: string;
  start_pos: number;
  end_pos: number;
  timestamp_start?: number;
  timestamp_end?: number;
  words_meta: WordMeta[];
  speaker?: string;
}

export interface WordMeta {
  word_id: string;
  start_pos: number;
  end_pos: number;
  timestamp_start?: number;
  timestamp_end?: number;
  speaker?: string;
  analysis?: WordAnalysis;
}

export interface WordAnalysis {
  root?: string;
  stem?: string;
  pos?: string;
  gender?: string;
  number?: string;
  morphology?: MorphologyInfo;
}

export interface MorphologyInfo {
  prefix?: string;
  stem: string;
  suffix?: string;
  features?: Record<string, string>;
}

export interface ContentMetadata {
  author?: string;
  source?: string;
  difficulty_level?: number;
  tags?: string[];
  category?: string;
  estimated_reading_time?: number;
}

export type ContentType = 
  | 'article' 
  | 'story' 
  | 'news' 
  | 'dialogue' 
  | 'poem' 
  | 'textbook' 
  | 'audio_transcript';

export interface UserInteraction {
  id: number;
  user_id: number;
  content_id: number;
  sentence_id: string;
  interaction_type: InteractionType;
  content: string;
  is_public: boolean;
  created_at: string;
}

export type InteractionType = 
  | 'note' 
  | 'translation' 
  | 'question' 
  | 'highlight' 
  | 'bookmark';

export interface Translation {
  id: number;
  content_id: number;
  sentence_id: string;
  target_language: string;
  translation_text: string;
  source: 'user' | 'ai' | 'professional';
  created_at: string;
}
