// 用户相关类型定义

export interface User {
  id: number;
  email: string;
  username: string;
  full_name?: string;
  avatar?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
  preferences: UserPreferences;
  subscription?: UserSubscription;
}

export interface UserPreferences {
  language: string;
  theme: 'light' | 'dark' | 'auto';
  auto_play_audio: boolean;
  default_transcription_language: string;
  show_word_analysis: boolean;
  show_translations: boolean;
  font_size: 'small' | 'medium' | 'large';
  reading_direction: 'ltr' | 'rtl';
}

export interface UserSubscription {
  id: number;
  plan: SubscriptionPlan;
  status: SubscriptionStatus;
  started_at: string;
  expires_at: string;
  auto_renew: boolean;
}

export type SubscriptionPlan = 
  | 'free' 
  | 'basic' 
  | 'premium' 
  | 'enterprise';

export type SubscriptionStatus = 
  | 'active' 
  | 'expired' 
  | 'cancelled' 
  | 'pending';

export interface UserProgress {
  user_id: number;
  total_reading_time: number;
  contents_read: number;
  words_learned: number;
  current_level: number;
  achievements: Achievement[];
  learning_streak: number;
  last_activity: string;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  earned_at: string;
  category: AchievementCategory;
}

export type AchievementCategory = 
  | 'reading' 
  | 'vocabulary' 
  | 'listening' 
  | 'consistency' 
  | 'milestone';

export interface UserSettings {
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  learning: LearningSettings;
}

export interface NotificationSettings {
  email_notifications: boolean;
  push_notifications: boolean;
  daily_reminder: boolean;
  weekly_summary: boolean;
}

export interface PrivacySettings {
  profile_visibility: 'public' | 'private';
  show_progress: boolean;
  allow_friend_requests: boolean;
}

export interface LearningSettings {
  daily_goal_minutes: number;
  difficulty_preference: 'beginner' | 'intermediate' | 'advanced' | 'mixed';
  content_types: ContentType[];
  focus_areas: string[];
}
