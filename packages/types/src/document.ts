// 文档解析相关类型定义

export interface DocumentMetadata {
  title: string;
  author?: string;
  language: string;
  pageCount?: number;
  wordCount?: number;
  createdAt?: Date;
  modifiedAt?: Date;
}

export interface DocumentStructure {
  type: 'heading' | 'paragraph' | 'image' | 'table' | 'list';
  level?: number; // 用于标题级别
  content: string;
  position: {
    start: number;
    end: number;
  };
  metadata?: Record<string, any>;
}

export interface ParsedDocument {
  metadata: DocumentMetadata;
  fullText: string;
  structure: DocumentStructure[];
  chapters?: ChapterInfo[];
}

export interface ChapterInfo {
  id: string;
  title: string;
  startPosition: number;
  endPosition: number;
  level: number;
}

export interface DocumentParseOptions {
  extractImages?: boolean;
  preserveFormatting?: boolean;
  detectChapters?: boolean;
  chapterMarkers?: string[];
}
