// 音频处理相关类型定义

export interface AudioTranscriptionRequest {
  file: File;
  language?: string;
  vad_onset?: number;
  vad_offset?: number;
  chunk_size?: number;
  enable_diarization?: boolean;
  min_speakers?: number;
  max_speakers?: number;
}

export interface AudioTranscriptionResponse {
  success: boolean;
  message: string;
  segments: TranscriptionSegment[];
  language?: string;
  duration?: number;
  processing_time?: number;
  word_segments?: WordSegment[];
}

export interface TranscriptionSegment {
  id: string;
  start: number;
  end: number;
  text: string;
  speaker?: string;
  confidence?: number;
  words?: WordSegment[];
}

export interface WordSegment {
  word: string;
  start: number;
  end: number;
  confidence?: number;
  speaker?: string;
}

export interface AudioProcessorStatus {
  status: 'healthy' | 'unhealthy';
  model_loaded: boolean;
  model_path: string;
  device: string;
  compute_type: string;
  supported_languages: string[];
}

export interface VADConfig {
  onset: number;
  offset: number;
  chunk_size: number;
  min_speech_duration?: number;
  min_silence_duration?: number;
}

export interface DiarizationConfig {
  enabled: boolean;
  min_speakers?: number;
  max_speakers?: number;
  use_auth_token?: string;
}
