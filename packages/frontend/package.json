{"name": "frontend", "version": "1.0.0", "description": "阿拉伯语学习平台前端应用", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@next/font": "^14.0.0", "next-i18next": "^15.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "clsx": "^2.0.0", "lucide-react": "^0.290.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "framer-motion": "^10.16.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.22.0", "axios": "^1.5.0", "swr": "^2.2.0", "react-query": "^3.39.0", "zustand": "^4.4.0", "react-hot-toast": "^2.4.0", "react-player": "^2.13.0", "wavesurfer.js": "^7.3.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.2.0", "eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0", "jest-environment-jsdom": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}