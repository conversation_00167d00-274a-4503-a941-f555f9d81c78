/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  i18n: {
    locales: ['en', 'zh', 'ar', 'ms', 'ja', 'ko', 'hi', 'ur'],
    defaultLocale: 'en',
    localeDetection: true,
  },
  images: {
    domains: ['localhost', 'api.arabic-learning.com'],
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`,
      },
      {
        source: '/audio/:path*',
        destination: `${process.env.NEXT_PUBLIC_AUDIO_PROCESSOR_URL}/:path*`,
      },
    ];
  },
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
    };
    return config;
  },
};

module.exports = nextConfig;
