"""
阿拉伯语学习平台后端 API
"""
from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager
from typing import Optional

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger

from app.api.v1.endpoints.qwen import initialize_qwen_api
from app.api.v1.router import api_router
from app.core.settings import get_settings
from app.infrastructure.backend_service_factory import BackendServiceFactory
from app.infrastructure.dicontainer import DIContainer


settings = get_settings()


# Application state to hold service instances
class AppState:
    """Application state container for service instances"""

    def __init__(self) -> None:
        self.factory: Optional[BackendServiceFactory] = None
        self.container: Optional[DIContainer] = None


# Global application state instance
app_state = AppState()


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager for startup and shutdown

    Args:
        app: FastAPI application instance

    Yields:
        None: Control back to FastAPI during application runtime
    """
    # Store app reference for potential future use
    _ = app

    try:
        logger.info("Starting Arabic Learning Platform Backend API...")

        # Initialize service factory and container
        app_state.factory = BackendServiceFactory(settings)
        app_state.container = DIContainer()
        app_state.container.register_factory(app_state.factory)

        # Initialize services
        await app_state.container.initialize_services()

        # Initialize API endpoints with DI container
        initialize_qwen_api(app_state.container)

        logger.success("Backend API started successfully")

        yield

    except Exception as e:
        logger.error(f"Failed to start backend API: {e}")
        raise
    finally:
        # Cleanup on shutdown
        logger.info("Shutting down Arabic Learning Platform Backend API...")

        if app_state.container:
            try:
                await app_state.container.cleanup_services()
            except Exception as e:
                logger.error(f"Error during container cleanup: {e}")

        if app_state.factory:
            try:
                await app_state.factory.cleanup_all_services()
            except Exception as e:
                logger.error(f"Error during factory cleanup: {e}")

        logger.info("Backend API shutdown complete")


app = FastAPI(
    title="Arabic Learning Platform API",
    description="阿拉伯语学习平台后端 API",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan,
)

# CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含 API 路由
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root() -> dict[str, str]:
    """
    Root endpoint providing API information

    Returns:
        Dictionary containing API message and version
    """
    return {"message": "Arabic Learning Platform API", "version": "1.0.0"}


@app.get("/health")
async def health_check() -> dict[str, str]:
    """
    Health check endpoint for monitoring API status

    Returns:
        Dictionary containing health status
    """
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("app.app_state:app", host="0.0.0.0", port=8000, reload=settings.DEBUG)
