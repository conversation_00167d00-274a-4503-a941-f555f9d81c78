"""
Qwen response adapter

Handles data format conversion from domain models to API models,
providing clean separation between internal business logic and external API contracts.
"""

from typing import Any, Optional

from app.services.qwen.interfaces import (
    GenerationResponse,
    SemanticValidationResponse,
)
from app.services.qwen.models import (
    QwenGenerationResponse,
    QwenSemanticValidationResponse,
)


class QwenResponseAdapter:
    """
    Adapter for converting domain response models to API models

    Handles the conversion from internal business logic models to
    external API contracts, including formatting and serialization.
    """

    @staticmethod
    def domain_to_api_generation(
        domain_response: GenerationResponse,
    ) -> QwenGenerationResponse:
        """
        Convert domain generation response to API model

        Args:
            domain_response: Domain response model from business logic

        Returns:
            API generation response model

        Raises:
            ValueError: If conversion fails due to invalid data
        """
        try:
            return QwenGenerationResponse(
                success=domain_response.success,
                message=domain_response.message,
                generated_text=domain_response.generated_text,
                processing_time=domain_response.processing_time,
                queue_time=domain_response.queue_time,
                request_id=domain_response.request_id,
            )
        except Exception as e:
            error_msg = (
                f"Failed to convert domain generation response to API model: {e}"
            )
            raise ValueError(error_msg) from e

    @staticmethod
    def domain_to_api_validation(
        domain_response: SemanticValidationResponse,
    ) -> QwenSemanticValidationResponse:
        """
        Convert domain validation response to API model (audio-processor compatibility)

        Args:
            domain_response: Domain validation response model

        Returns:
            API validation response model

        Raises:
            ValueError: If conversion fails due to invalid data
        """
        try:
            return QwenSemanticValidationResponse(
                success=domain_response.success,
                message=domain_response.message,
                generated_text=domain_response.generated_text,
                processing_time=domain_response.processing_time,
            )
        except Exception as e:
            error_msg = (
                f"Failed to convert domain validation response to API model: {e}"
            )
            raise ValueError(error_msg) from e

    @staticmethod
    def create_error_generation_response(
        error_message: str, request_id: Optional[str] = None
    ) -> QwenGenerationResponse:
        """
        Create error response for generation requests

        Args:
            error_message: Error description
            request_id: Optional request ID for tracking

        Returns:
            API error response model
        """
        return QwenGenerationResponse(
            success=False,
            message=error_message,
            generated_text="",
            processing_time=None,
            queue_time=None,
            request_id=request_id,
        )

    @staticmethod
    def create_error_validation_response(
        error_message: str,
    ) -> QwenSemanticValidationResponse:
        """
        Create error response for validation requests

        Args:
            error_message: Error description

        Returns:
            API error response model
        """
        return QwenSemanticValidationResponse(
            success=False,
            message=error_message,
            generated_text="",
            processing_time=None,
        )

    @staticmethod
    def qwen_service_to_domain_response(
        qwen_response: dict[str, Any],
        request_id: str,
        queue_time: Optional[float] = None,
    ) -> GenerationResponse:
        """
        Convert qwen-service API response to domain model

        Args:
            qwen_response: Raw response from qwen-service API
            request_id: Request tracking ID
            queue_time: Time spent in queue

        Returns:
            Domain generation response model

        Raises:
            ValueError: If conversion fails due to invalid response format
        """
        try:
            # qwen-service returns a detailed response with generated_text and
            # processing_time
            generated_text = qwen_response.get("generated_text", "")
            processing_time = qwen_response.get("processing_time", 0.0)

            return GenerationResponse(
                success=True,
                message="Text generated successfully",
                generated_text=generated_text,
                processing_time=processing_time,
                queue_time=queue_time,
                request_id=request_id,
            )
        except Exception as e:
            error_msg = f"Failed to convert qwen-service response to domain model: {e}"
            raise ValueError(error_msg) from e
