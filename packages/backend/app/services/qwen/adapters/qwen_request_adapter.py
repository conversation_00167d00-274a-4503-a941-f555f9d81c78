"""
Qwen request adapter

Handles data format conversion from API models to domain models,
providing clean separation between external API contracts and internal business logic.
"""

import uuid

from app.services.qwen.interfaces import (
    GenerationRequest,
    SemanticValidationRequest,
)
from app.services.qwen.models import (
    QwenGenerationRequest,
    QwenSemanticValidationRequest,
    RequestPriorityAPI,
)
from app.shared.models.request_priority import RequestPriority


class QwenRequestAdapter:
    """
    Adapter for converting API request models to domain models

    Handles the conversion from external API contracts to internal
    business logic models, including validation and enrichment.
    """

    @staticmethod
    def api_to_domain_generation(
        api_request: QwenGenerationRequest, source_service: str = "backend-api"
    ) -> GenerationRequest:
        """
        Convert API generation request to domain model

        Args:
            api_request: API request model from REST endpoint
            source_service: Source service identifier

        Returns:
            Domain generation request model

        Raises:
            ValueError: If conversion fails due to invalid data
        """
        try:
            # Convert priority enum
            priority_mapping = {
                RequestPriorityAPI.HIGH: RequestPriority.HIGH,
                RequestPriorityAPI.MEDIUM: RequestPriority.MEDIUM,
                RequestPriorityAPI.LOW: RequestPriority.LOW,
            }

            domain_priority = priority_mapping.get(
                api_request.priority, RequestPriority.MEDIUM
            )

            # Generate request ID if not provided
            request_id = str(uuid.uuid4())

            return GenerationRequest(
                prompt=api_request.prompt,
                system_prompt=api_request.system_prompt,
                enable_thinking=api_request.enable_thinking,
                priority=domain_priority,
                timeout=api_request.timeout,
                request_id=request_id,
                source_service=api_request.source_service or source_service,
            )
        except Exception as e:
            error_msg = f"Failed to convert API generation request to domain model: {e}"
            raise ValueError(error_msg) from e

    @staticmethod
    def api_to_domain_validation(
        api_request: QwenSemanticValidationRequest,
        source_service: str = "audio-processor",  # noqa: ARG004
    ) -> SemanticValidationRequest:
        """
        Convert API validation request to domain model (audio-processor compatibility)

        Args:
            api_request: API validation request model
            source_service: Source service identifier

        Returns:
            Domain validation request model

        Raises:
            ValueError: If conversion fails due to invalid data
        """
        try:
            # Generate request ID
            request_id = str(uuid.uuid4())

            return SemanticValidationRequest(
                prompt=api_request.prompt,
                system_prompt=api_request.system_prompt,
                enable_thinking=api_request.enable_thinking,
                priority=RequestPriority.HIGH,  # Audio-processor needs high priority
                timeout=None,  # Use default timeout for validation
                request_id=request_id,
            )
        except Exception as e:
            error_msg = f"Failed to convert API validation request to domain model: {e}"
            raise ValueError(error_msg) from e
