"""
Qwen call manager interface

Contains the QwenCallManagerInterface protocol for unified qwen service call management,
defining the contract for complete request processing workflow.
"""

from typing import Any, Protocol, runtime_checkable

from .generation_request import GenerationRequest
from .generation_response import GenerationResponse
from .semantic_validation_request import SemanticValidationRequest
from .semantic_validation_response import SemanticValidationResponse


@runtime_checkable
class QwenCallManagerInterface(Protocol):
    """Protocol for unified qwen service call management"""

    async def process_request(self, request: GenerationRequest) -> GenerationResponse:
        """
        Process generation request through complete workflow

        Args:
            request: Generation request with priority and configuration

        Returns:
            GenerationResponse containing result and metadata

        Raises:
            RuntimeError: If processing fails
        """
        ...

    async def process_validation(
        self, request: SemanticValidationRequest
    ) -> SemanticValidationResponse:
        """
        Process semantic validation request (audio-processor compatibility)

        Args:
            request: Semantic validation request

        Returns:
            SemanticValidationResponse containing validation result

        Raises:
            RuntimeError: If validation fails
        """
        ...

    async def get_system_status(self) -> dict[str, Any]:
        """
        Get comprehensive system status

        Returns:
            Dictionary containing queue status, resource status, and metrics
        """
        ...

    async def initialize(self) -> None:
        """
        Initialize call manager and all dependencies

        Raises:
            RuntimeError: If initialization fails
        """
        ...

    async def cleanup(self) -> None:
        """
        Cleanup call manager and release all resources
        """
        ...
