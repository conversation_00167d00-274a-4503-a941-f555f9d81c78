"""
Resource status data class

Contains the ResourceStatus data class for qwen service interfaces,
providing resource allocation and usage status information.
"""

from dataclasses import dataclass
from datetime import datetime

from .circuit_breaker_state import CircuitBreakerState


@dataclass
class ResourceStatus:
    """Resource allocation and usage status"""

    gpu_utilization: float
    memory_usage: float
    active_connections: int
    max_connections: int
    circuit_breaker_state: CircuitBreakerState
    last_health_check: datetime
