"""
Queue manager interface

Contains the QueueManagerInterface protocol for priority queue management,
defining the contract for request queuing and processing.
"""

from typing import Optional, Protocol, runtime_checkable

from app.shared.models.request_priority import RequestPriority

from .generation_request import GenerationRequest
from .queue_status import QueueStatus


@runtime_checkable
class QueueManagerInterface(Protocol):
    """Protocol for priority queue management"""

    async def enqueue_request(
        self, request: GenerationRequest, priority: RequestPriority
    ) -> str:
        """
        Add request to appropriate priority queue

        Args:
            request: Generation request to queue
            priority: Request priority level

        Returns:
            Request ID for tracking

        Raises:
            RuntimeError: If queue is full or system is overloaded
        """
        ...

    async def dequeue_request(self) -> Optional[GenerationRequest]:
        """
        Get next request from highest priority queue

        Returns:
            Next request to process or None if queues are empty
        """
        ...

    async def get_queue_status(self) -> QueueStatus:
        """
        Get current queue status and metrics

        Returns:
            QueueStatus containing queue lengths and wait times
        """
        ...

    async def cancel_request(self, request_id: str) -> bool:
        """
        Cancel a pending request

        Args:
            request_id: ID of request to cancel

        Returns:
            True if request was cancelled, False if not found
        """
        ...
