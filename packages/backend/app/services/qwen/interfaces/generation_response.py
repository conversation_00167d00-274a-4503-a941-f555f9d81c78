"""
Generation response data class

Contains the GenerationResponse data class for qwen service interfaces,
providing response structure for text generation operations.
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class GenerationResponse:
    """Response from qwen-service text generation"""

    success: bool
    message: str
    generated_text: str
    processing_time: Optional[float] = None
    queue_time: Optional[float] = None
    request_id: Optional[str] = None
