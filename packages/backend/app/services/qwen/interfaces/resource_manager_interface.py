"""
Resource manager interface

Contains the ResourceManagerInterface protocol for GPU resource management,
defining the contract for resource allocation and monitoring.
"""

from typing import Protocol, runtime_checkable

from app.shared.models.request_priority import RequestPriority

from .resource_status import ResourceStatus


@runtime_checkable
class ResourceManagerInterface(Protocol):
    """Protocol for GPU resource management and monitoring"""

    async def acquire_resource(self, priority: RequestPriority) -> bool:
        """
        Acquire GPU resource for processing

        Args:
            priority: Request priority for resource allocation

        Returns:
            True if resource acquired, False if unavailable

        Raises:
            RuntimeError: If resource allocation fails
        """
        ...

    async def release_resource(self) -> None:
        """
        Release GPU resource after processing
        """
        ...

    async def get_resource_status(self) -> ResourceStatus:
        """
        Get current resource usage and status

        Returns:
            ResourceStatus containing utilization and health metrics
        """
        ...

    async def check_circuit_breaker(self) -> bool:
        """
        Check if circuit breaker allows requests

        Returns:
            True if requests are allowed, False if circuit is open
        """
        ...

    async def record_success(self, response_time: float) -> None:
        """
        Record successful request for circuit breaker metrics

        Args:
            response_time: Request processing time in seconds
        """
        ...

    async def record_failure(self, error: Exception) -> None:
        """
        Record failed request for circuit breaker metrics

        Args:
            error: Exception that caused the failure
        """
        ...
