"""
Qwen service interface

Contains the QwenServiceInterface protocol for qwen service operations,
defining the contract for text generation and semantic validation.
"""

from typing import Protocol, runtime_checkable

from .generation_request import GenerationRequest
from .generation_response import GenerationResponse
from .semantic_validation_request import SemanticValidationRequest
from .semantic_validation_response import SemanticValidationResponse


@runtime_checkable
class QwenServiceInterface(Protocol):
    """Protocol for qwen-service operations"""

    async def generate_text(self, request: GenerationRequest) -> GenerationResponse:
        """
        Generate text using qwen-service with resource management

        Args:
            request: Text generation request with priority and configuration

        Returns:
            GenerationResponse containing generated text and metadata

        Raises:
            RuntimeError: If generation fails
        """
        ...

    async def validate_semantically(
        self, request: SemanticValidationRequest
    ) -> SemanticValidationResponse:
        """
        Perform semantic validation using qwen-service (audio-processor compatibility)

        Args:
            request: Semantic validation request with prompt and configuration

        Returns:
            SemanticValidationResponse containing validation result

        Raises:
            RuntimeError: If validation fails
        """
        ...

    async def health_check(self) -> bool:
        """
        Check if qwen-service is available and healthy

        Returns:
            True if service is healthy, False otherwise
        """
        ...
