"""
Generation request data class

Contains the GenerationRequest data class for qwen service interfaces,
providing request structure for text generation operations.
"""

from dataclasses import dataclass
from typing import Optional

from app.shared.models.request_priority import RequestPriority


@dataclass
class GenerationRequest:
    """Request for text generation via qwen-service"""

    prompt: str
    system_prompt: Optional[str] = None
    enable_thinking: bool = True
    priority: RequestPriority = RequestPriority.MEDIUM
    timeout: Optional[float] = None
    request_id: Optional[str] = None
    source_service: Optional[str] = None
