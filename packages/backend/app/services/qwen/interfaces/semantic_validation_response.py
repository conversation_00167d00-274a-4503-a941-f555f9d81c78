"""
Semantic validation response data class

Contains the SemanticValidationResponse data class for qwen service interfaces,
providing response structure for semantic validation operations.
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class SemanticValidationResponse:
    """Response from qwen-service semantic validation (audio-processor compatibility)"""

    success: bool
    message: str
    generated_text: str
    processing_time: Optional[float] = None
    queue_time: Optional[float] = None
