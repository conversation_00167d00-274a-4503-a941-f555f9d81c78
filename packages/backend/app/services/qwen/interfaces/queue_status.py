"""
Queue status data class

Contains the QueueStatus data class for qwen service interfaces,
providing queue status information for monitoring.
"""

from dataclasses import dataclass


@dataclass
class QueueStatus:
    """Queue status information"""

    high_priority_count: int
    medium_priority_count: int
    low_priority_count: int
    total_pending: int
    average_wait_time: float
    processing_count: int
