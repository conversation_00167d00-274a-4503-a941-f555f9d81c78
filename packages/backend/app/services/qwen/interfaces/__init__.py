"""
Qwen service interfaces

Contains Qwen-specific interface definitions that extend or specialize
the generic interfaces for Qwen service requirements.
"""

from .circuit_breaker_state import Circuit<PERSON>reakerState
from .generation_request import GenerationRequest
from .generation_response import GenerationResponse
from .queue_manager_interface import QueueManagerInterface
from .queue_status import QueueStatus
from .qwen_call_manager_interface import QwenCallManagerInterface
from .qwen_service_interface import QwenServiceInterface
from .resource_manager_interface import ResourceManagerInterface
from .resource_status import ResourceStatus
from .semantic_validation_request import SemanticValidationRequest
from .semantic_validation_response import SemanticValidationResponse


__all__ = [
    "CircuitBreakerState",
    "GenerationRequest",
    "GenerationResponse",
    "QueueManagerInterface",
    "QueueStatus",
    "QwenCallManagerInterface",
    "QwenServiceInterface",
    "ResourceManagerInterface",
    "ResourceStatus",
    "SemanticValidationRequest",
    "SemanticValidationResponse",
]
