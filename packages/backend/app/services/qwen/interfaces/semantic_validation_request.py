"""
Semantic validation request data class

Contains the SemanticValidationRequest data class for qwen service interfaces,
providing request structure for semantic validation operations.
"""

from dataclasses import dataclass
from typing import Optional

from app.shared.models.request_priority import RequestPriority


@dataclass
class SemanticValidationRequest:
    """
    Request for semantic validation via qwen-service (audio-processor compatibility)
    """

    prompt: str
    system_prompt: str
    enable_thinking: bool = True
    priority: RequestPriority = (
        RequestPriority.HIGH
    )  # Audio-processor needs high priority
    timeout: Optional[float] = None
    request_id: Optional[str] = None
