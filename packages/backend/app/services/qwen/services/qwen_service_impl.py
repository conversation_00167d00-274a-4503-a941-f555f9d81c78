"""
Qwen service implementation

High-level service wrapper for qwen operations, providing a clean interface
for the API layer while delegating to the call manager for actual processing.
"""

from loguru import logger

from app.core.settings import Settings
from app.services.qwen.interfaces import (
    GenerationRequest,
    GenerationResponse,
    QwenCallManagerInterface,
    QwenServiceInterface,
    SemanticValidationRequest,
    SemanticValidationResponse,
)


class QwenServiceImpl(QwenServiceInterface):
    """
    Implementation of qwen service interface

    Provides a high-level interface for qwen operations while delegating
    the actual processing to the call manager for resource management
    and priority handling.
    """

    def __init__(self, settings: Settings, call_manager: QwenCallManagerInterface):
        """
        Initialize qwen service with dependencies

        Args:
            settings: Application settings containing configuration
            call_manager: Call manager for handling requests
        """
        self.settings = settings
        self.call_manager = call_manager

        logger.debug("QwenService initialized")

    async def generate_text(self, request: GenerationRequest) -> GenerationResponse:
        """
        Generate text using qwen-service with resource management

        Args:
            request: Text generation request with priority and configuration

        Returns:
            GenerationResponse containing generated text and metadata

        Raises:
            RuntimeError: If generation fails
        """
        try:
            logger.debug(f"Processing text generation request: {request.request_id}")

            # Delegate to call manager for processing
            result = await self.call_manager.process_request(request)

            logger.debug(
                f"Text generation completed: {request.request_id} "
                f"(success: {result.success})"
            )

            return result

        except Exception as e:
            error_msg = f"Text generation failed: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    async def validate_semantically(
        self, request: SemanticValidationRequest
    ) -> SemanticValidationResponse:
        """
        Perform semantic validation using qwen-service (audio-processor compatibility)

        Args:
            request: Semantic validation request with prompt and configuration

        Returns:
            SemanticValidationResponse containing validation result

        Raises:
            RuntimeError: If validation fails
        """
        try:
            logger.debug(
                f"Processing semantic validation request: {request.request_id}"
            )

            # Delegate to call manager for processing
            result = await self.call_manager.process_validation(request)

            logger.debug(
                f"Semantic validation completed: {request.request_id} "
                f"(success: {result.success})"
            )

            return result

        except Exception as e:
            error_msg = f"Semantic validation failed: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    async def health_check(self) -> bool:
        """
        Check if qwen-service is available and healthy

        Returns:
            True if service is healthy, False otherwise
        """
        try:
            # Get system status from call manager
            status = await self.call_manager.get_system_status()

            # Check if system is healthy
            if "error" in status:
                logger.warning(f"Health check failed: {status['error']}")
                return False

            # Check circuit breaker state
            resource_status = status.get("resource_status", {})
            circuit_breaker_state = resource_status.get("circuit_breaker_state", "open")

            if circuit_breaker_state == "open":
                logger.warning("Health check failed: circuit breaker is open")
                return False

            # Check if queues are not overloaded
            queue_status = status.get("queue_status", {})
            total_pending = queue_status.get("total_pending", 0)
            max_queue_size = getattr(self.settings, "QWEN_MAX_QUEUE_SIZE", 1000)

            if total_pending >= max_queue_size * 0.9:  # 90% threshold
                logger.warning(
                    f"Health check warning: queue nearly full "
                    f"({total_pending}/{max_queue_size})"
                )
                # Still return True but log warning

            logger.debug("Health check passed")
            return True

        except Exception as e:
            logger.error(f"Health check failed with exception: {e}")
            return False
