"""
Qwen generation response model

Contains the QwenGenerationResponse model for qwen service API,
providing validation and serialization for text generation responses.
"""

from typing import ClassVar, Optional

from pydantic import BaseModel, Field


class QwenGenerationResponse(BaseModel):
    """API model for text generation responses"""

    success: bool = Field(..., description="Whether the request was successful")
    message: str = Field(..., description="Response message or error description")
    generated_text: str = Field(..., description="Generated text result")
    processing_time: Optional[float] = Field(
        None, description="Processing time in seconds"
    )
    queue_time: Optional[float] = Field(
        None, description="Time spent in queue in seconds"
    )
    request_id: Optional[str] = Field(None, description="Request tracking ID")

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "success": True,
                "message": "Text generated successfully",
                "generated_text": "هذا نص مترجم إلى العربية",
                "processing_time": 2.5,
                "queue_time": 0.1,
                "request_id": "req_123456",
            }
        }
