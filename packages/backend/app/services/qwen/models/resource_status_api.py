"""
Resource status API model

Contains the ResourceStatusAPI model for qwen service API,
providing validation and serialization for resource status information.
"""

from pydantic import BaseModel, Field

from .circuit_breaker_state_api import CircuitBreakerStateAPI


class ResourceStatusAPI(BaseModel):
    """API model for resource status"""

    gpu_utilization: float = Field(..., description="GPU utilization percentage")
    memory_usage: float = Field(..., description="Memory usage percentage")
    active_connections: int = Field(
        ..., description="Number of active HTTP connections"
    )
    max_connections: int = Field(..., description="Maximum allowed connections")
    circuit_breaker_state: CircuitBreakerStateAPI = Field(
        ..., description="Circuit breaker state"
    )
    last_health_check: str = Field(..., description="Last health check timestamp")
