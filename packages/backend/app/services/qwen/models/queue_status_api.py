"""
Queue status API model

Contains the QueueStatusAPI model for qwen service API,
providing validation and serialization for queue status information.
"""

from pydantic import BaseModel, Field


class QueueStatusAPI(BaseModel):
    """API model for queue status"""

    high_priority_count: int = Field(
        ..., description="Number of high priority requests in queue"
    )
    medium_priority_count: int = Field(
        ..., description="Number of medium priority requests in queue"
    )
    low_priority_count: int = Field(
        ..., description="Number of low priority requests in queue"
    )
    total_pending: int = Field(..., description="Total pending requests")
    average_wait_time: float = Field(..., description="Average wait time in seconds")
    processing_count: int = Field(
        ..., description="Number of requests currently processing"
    )
