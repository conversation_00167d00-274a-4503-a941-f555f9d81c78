"""
Qwen generation request model

Contains the QwenGenerationRequest model for qwen service API,
providing validation and serialization for text generation requests.
"""

from typing import ClassVar, Optional

from pydantic import BaseModel, Field

from .request_priority_api import RequestPriorityAPI


class QwenGenerationRequest(BaseModel):
    """API model for text generation requests"""

    prompt: str = Field(..., description="Text prompt for generation")
    system_prompt: Optional[str] = Field(None, description="System prompt for context")
    enable_thinking: bool = Field(True, description="Enable thinking mode")
    priority: RequestPriorityAPI = Field(
        RequestPriorityAPI.MEDIUM, description="Request priority"
    )
    timeout: Optional[float] = Field(None, description="Request timeout in seconds")
    source_service: Optional[str] = Field(None, description="Source service identifier")

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "prompt": "Translate this text to Arabic",
                "system_prompt": "You are a helpful translation assistant",
                "enable_thinking": True,
                "priority": "medium",
                "timeout": 30.0,
                "source_service": "audio-processor",
            }
        }
