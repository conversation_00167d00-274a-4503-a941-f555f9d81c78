"""
Qwen semantic validation request model

Contains the QwenSemanticValidationRequest model for qwen service API,
providing validation and serialization for semantic validation requests.
"""

from typing import Any, ClassVar

from pydantic import BaseModel, Field


class QwenSemanticValidationRequest(BaseModel):
    """API model for semantic validation requests (audio-processor compatibility)"""

    prompt: str = Field(..., description="Text prompt for validation")
    system_prompt: str = Field(..., description="System prompt for validation context")
    enable_thinking: bool = Field(True, description="Enable thinking mode")

    class Config:
        json_schema_extra: ClassVar[dict[str, Any]] = {
            "example": {
                "prompt": "Is this a valid chapter title: 'الفصل الأول'?",
                "system_prompt": "You are an Arabic text analysis expert",
                "enable_thinking": True,
            }
        }
