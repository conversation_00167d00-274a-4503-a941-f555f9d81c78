"""
Metrics data API model

Contains the MetricsDataAPI model for qwen service API,
providing validation and serialization for metrics data.
"""

from pydantic import BaseModel, Field


class MetricsDataAPI(BaseModel):
    """API model for metrics data"""

    requests_per_minute: float = Field(..., description="Requests per minute")
    average_response_time: float = Field(
        ..., description="Average response time in seconds"
    )
    success_rate: float = Field(..., description="Success rate percentage")
    error_rate: float = Field(..., description="Error rate percentage")
    queue_wait_time: float = Field(
        ..., description="Average queue wait time in seconds"
    )
    gpu_utilization: float = Field(
        ..., description="Average GPU utilization percentage"
    )
