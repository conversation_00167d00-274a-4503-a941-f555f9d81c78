"""
Shared services for generic, reusable components

This module contains generic service implementations that can be used across
different services, providing consistent behavior and reducing code duplication.
"""

from .generic_call_manager import GenericCallManager
from .generic_queue_manager import GenericQueueManager
from .generic_resource_manager import GenericResourceManager


__all__ = [
    "GenericQueueManager",
    "GenericResourceManager",
    "GenericCallManager",
]
