"""
Shared interfaces for generic, reusable components

This module contains generic interfaces that can be used across different services,
implementing the Dependency Inversion Principle and enabling clean architecture.
"""

from .call_manager_interface import CallManagerInterface
from .circuit_breaker_interface import CircuitBreakerInterface
from .external_service_client_interface import ExternalServiceClientInterface
from .queue_manager_interface import QueueManagerInterface
from .resource_manager_interface import ResourceManagerInterface


__all__ = [
    "CallManagerInterface",
    "QueueManagerInterface",
    "ResourceManagerInterface",
    "ExternalServiceClientInterface",
    "CircuitBreakerInterface",
]
