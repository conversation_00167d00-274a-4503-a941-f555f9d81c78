"""
Generic resource manager interface

Defines the protocol for resource allocation, monitoring, and circuit breaker
functionality with intelligent resource management capabilities.
"""

from typing import Protocol, runtime_checkable

from app.shared.models.request_priority import RequestPriority
from app.shared.models.resource_status import ResourceStatus


@runtime_checkable
class ResourceManagerInterface(Protocol):
    """Generic protocol for resource management and monitoring"""

    async def acquire_resource(self, priority: RequestPriority) -> bool:
        """
        Acquire resource for processing

        Args:
            priority: Request priority for resource allocation

        Returns:
            True if resource acquired, False if unavailable

        Raises:
            RuntimeError: If resource allocation fails
        """
        ...

    async def release_resource(self) -> None:
        """
        Release resource after processing
        """
        ...

    async def get_resource_status(self) -> ResourceStatus:
        """
        Get current resource usage and status

        Returns:
            ResourceStatus containing utilization and health metrics
        """
        ...

    async def record_success(self, processing_time: float) -> None:
        """
        Record successful operation for metrics

        Args:
            processing_time: Time taken for the operation
        """
        ...

    async def record_failure(self, error: Exception) -> None:
        """
        Record failed operation for metrics and circuit breaker

        Args:
            error: Exception that caused the failure
        """
        ...

    async def check_circuit_breaker(self) -> bool:
        """
        Check if circuit breaker allows requests

        Returns:
            True if requests are allowed, False if circuit is open
        """
        ...

    async def get_performance_metrics(self) -> dict:
        """
        Get performance metrics for monitoring

        Returns:
            Dictionary containing performance metrics
        """
        ...

    async def initialize(self) -> None:
        """
        Initialize resource manager

        Raises:
            RuntimeError: If initialization fails
        """
        ...

    async def cleanup(self) -> None:
        """
        Cleanup resource manager and release resources
        """
        ...
