"""
Generic queue manager interface

Defines the protocol for priority queue management with intelligent
scheduling and resource allocation based on request priorities.
"""

from typing import Generic, Optional, Protocol, TypeVar, runtime_checkable

from app.shared.models.queue_status import QueueStatus
from app.shared.models.request_priority import RequestPriority


T = TypeVar("T")


@runtime_checkable
class QueueManagerInterface(Protocol, Generic[T]):
    """Generic protocol for priority queue management"""

    async def enqueue_request(self, request: T, priority: RequestPriority) -> str:
        """
        Add request to appropriate priority queue

        Args:
            request: Service request to queue
            priority: Request priority level

        Returns:
            Request ID for tracking

        Raises:
            RuntimeError: If queue is full or system is overloaded
        """
        ...

    async def dequeue_request(self) -> Optional[T]:
        """
        Get next request from highest priority queue

        Returns:
            Next request to process or None if queues are empty
        """
        ...

    async def cancel_request(self, request_id: str) -> bool:
        """
        Cancel queued request by ID

        Args:
            request_id: ID of request to cancel

        Returns:
            True if request was cancelled, False if not found
        """
        ...

    async def get_queue_status(self) -> QueueStatus:
        """
        Get current queue status and metrics

        Returns:
            QueueStatus containing queue lengths and wait times
        """
        ...

    async def initialize(self) -> None:
        """
        Initialize queue manager

        Raises:
            RuntimeError: If initialization fails
        """
        ...

    async def cleanup(self) -> None:
        """
        Cleanup queue manager and release resources
        """
        ...
