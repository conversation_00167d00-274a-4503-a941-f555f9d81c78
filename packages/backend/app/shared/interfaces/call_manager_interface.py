"""
Generic call manager interface

Defines the protocol for service call management with priority queuing,
resource allocation, and monitoring capabilities.
"""

from typing import Any, Generic, Protocol, TypeVar, runtime_checkable


TRequest_contra = TypeVar("TRequest_contra", contravariant=True)
TResponse_co = TypeVar("TResponse_co", covariant=True)


@runtime_checkable
class CallManagerInterface(Protocol, Generic[TRequest_contra, TResponse_co]):
    """Generic protocol for service call management"""

    async def process_request(self, request: TRequest_contra) -> TResponse_co:
        """
        Process service request through complete workflow

        Args:
            request: Service request with priority and configuration

        Returns:
            Service response containing result and metadata

        Raises:
            RuntimeError: If processing fails
        """
        ...

    async def get_system_status(self) -> dict[str, Any]:
        """
        Get comprehensive system status

        Returns:
            Dictionary containing queue status, resource status, and metrics
        """
        ...

    async def initialize(self) -> None:
        """
        Initialize call manager and all dependencies

        Raises:
            RuntimeError: If initialization fails
        """
        ...

    async def cleanup(self) -> None:
        """
        Cleanup call manager and release all resources
        """
        ...
