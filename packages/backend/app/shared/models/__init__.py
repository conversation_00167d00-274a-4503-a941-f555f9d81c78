"""
Shared models for generic, reusable components

This module contains generic data models and DTOs that can be used across
different services, providing consistent data structures and validation.
"""

from .base_request import BaseRequest
from .base_response import BaseResponse
from .circuit_breaker_state import CircuitBreakerState
from .queue_status import QueueStatus
from .queued_request import QueuedRequest
from .request_priority import RequestPriority
from .resource_status import ResourceStatus
from .service_config import ServiceConfig


__all__ = [
    "RequestPriority",
    "ServiceConfig",
    "BaseRequest",
    "BaseResponse",
    "QueueStatus",
    "QueuedRequest",
    "ResourceStatus",
    "CircuitBreakerState",
]
