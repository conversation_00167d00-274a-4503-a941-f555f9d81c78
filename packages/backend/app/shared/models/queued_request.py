"""
Queued request data class

Contains container for queued requests with metadata,
including timing information and priority handling.
"""

import time
from dataclasses import dataclass, field
from typing import Generic, TypeVar

from .request_priority import RequestPriority


T = TypeVar("T")


@dataclass
class QueuedRequest(Generic[T]):
    """Container for queued requests with metadata"""

    request: T
    enqueue_time: float = field(default_factory=time.time)
    priority: RequestPriority = RequestPriority.MEDIUM
    request_id: str = ""

    def get_wait_time(self) -> float:
        """Get current wait time in seconds"""
        return time.time() - self.enqueue_time
