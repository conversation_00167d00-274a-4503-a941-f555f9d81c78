"""
Base request data class

Contains the fundamental structure for service requests,
including priority, timeout, and identification information.
"""

from dataclasses import dataclass
from typing import Optional

from .request_priority import RequestPriority


@dataclass
class BaseRequest:
    """Base class for service requests"""

    request_id: Optional[str] = None
    priority: RequestPriority = RequestPriority.MEDIUM
    timeout: Optional[float] = None
    source_service: Optional[str] = None
