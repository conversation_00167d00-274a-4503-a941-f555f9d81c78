"""
Service configuration data class

Contains configuration parameters for generic service management,
including resource allocation, timeouts, and circuit breaker settings.
"""

from dataclasses import dataclass
from typing import Optional


# Configuration validation constants
_ALLOCATION_TOLERANCE = 0.01  # Allow small floating point errors


@dataclass
class ServiceConfig:
    """Generic service configuration"""

    name: str
    max_concurrent_requests: int
    high_priority_allocation: float = 0.6
    medium_priority_allocation: float = 0.3
    low_priority_allocation: float = 0.1
    max_queue_size: int = 1000
    timeout: float = 30.0
    circuit_breaker_failure_threshold: int = 5
    circuit_breaker_recovery_timeout: float = 60.0
    circuit_breaker_expected_exception: Optional[type] = None

    def __post_init__(self) -> None:
        """Validate configuration values"""
        if not (0 < self.high_priority_allocation <= 1):
            raise ValueError("high_priority_allocation must be between 0 and 1")
        if not (0 < self.medium_priority_allocation <= 1):
            raise ValueError("medium_priority_allocation must be between 0 and 1")
        if not (0 < self.low_priority_allocation <= 1):
            raise ValueError("low_priority_allocation must be between 0 and 1")

        total_allocation = (
            self.high_priority_allocation
            + self.medium_priority_allocation
            + self.low_priority_allocation
        )
        if abs(total_allocation - 1.0) > _ALLOCATION_TOLERANCE:
            raise ValueError(
                f"Priority allocations must sum to 1.0, " f"got {total_allocation}"
            )

        if self.max_concurrent_requests <= 0:
            raise ValueError("max_concurrent_requests must be positive")
        if self.max_queue_size <= 0:
            raise ValueError("max_queue_size must be positive")
        if self.timeout <= 0:
            raise ValueError("timeout must be positive")
