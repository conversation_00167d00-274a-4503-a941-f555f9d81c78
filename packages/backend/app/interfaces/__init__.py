"""
Backend service interfaces

This module defines the core interfaces and protocols for the backend service,
enabling clean separation of concerns and dependency injection.
"""

from app.services.qwen.interfaces import (
    QwenCallManagerInterface,
    QwenServiceInterface,
)
from app.shared.interfaces.queue_manager_interface import QueueManagerInterface
from app.shared.interfaces.resource_manager_interface import ResourceManagerInterface

from .circuit_breaker_config import CircuitBreakerConfig
from .circuit_breaker_interface import CircuitBreakerInterface
from .external_service_client_interface import ExternalServiceClientInterface
from .http_client_config import HttpClientConfig


__all__ = [
    "QwenServiceInterface",
    "QwenCallManagerInterface",
    "QueueManagerInterface",
    "ResourceManagerInterface",
    "ExternalServiceClientInterface",
    "CircuitBreakerInterface",
    "CircuitBreakerConfig",
    "HttpClientConfig",
]
