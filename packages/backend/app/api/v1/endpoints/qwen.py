"""
Qwen service API endpoints

REST API endpoints for qwen service calls, providing unified access
to qwen-service through the call management system.
"""

from datetime import datetime, timezone
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, status
from loguru import logger

from app.infrastructure.dicontainer import DIContainer
from app.services.qwen.adapters import (
    QwenRequestAdapter,
    QwenResponseAdapter,
)
from app.services.qwen.interfaces import QwenServiceInterface
from app.services.qwen.models import (
    QwenGenerationRequest,
    QwenGenerationResponse,
    QwenHealthResponse,
    QwenMetricsResponse,
    QwenSemanticValidationRequest,
    QwenSemanticValidationResponse,
    QwenStatusResponse,
)


router = APIRouter()

# Global DI container instance (will be initialized in main.py)
_container: Optional[DIContainer] = None


def get_container() -> DIContainer:
    """
    Get DI container instance

    Returns:
        DI container instance

    Raises:
        HTTPException: If container is not initialized
    """
    if _container is None or not _container.is_initialized():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service not initialized",
        )
    return _container


async def get_qwen_service(
    container: DIContainer = Depends(get_container),  # noqa: B008
) -> QwenServiceInterface:
    """
    Get qwen service instance from DI container

    Args:
        container: DI container instance

    Returns:
        QwenServiceInterface instance

    Raises:
        HTTPException: If service cannot be retrieved
    """
    try:
        service = container.get_service(QwenServiceInterface)  # type: ignore[type-abstract]

        # Ensure the external client is properly initialized
        from app.interfaces.external_service_client_interface import (
            ExternalServiceClientInterface,
        )

        external_client = container.get_service(
            ExternalServiceClientInterface  # type: ignore[type-abstract]
        )
        if (
            hasattr(external_client, "_initialized")
            and not external_client._initialized
        ):
            await external_client.initialize()  # type: ignore[attr-defined]

        return service
    except Exception as e:
        logger.error(f"Failed to get qwen service: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Qwen service unavailable",
        ) from e


@router.post(
    "/generate",
    response_model=QwenGenerationResponse,
    summary="Generate text using qwen-service",
    description=(
        "Generate text using qwen-service with priority-based " "resource management"
    ),
)
async def generate_text(
    request: QwenGenerationRequest,
    qwen_service: QwenServiceInterface = Depends(get_qwen_service),  # noqa: B008
) -> QwenGenerationResponse:
    """
    Generate text using qwen-service

    Args:
        request: Text generation request
        qwen_service: Qwen service instance
        settings: Application settings

    Returns:
        Generated text response

    Raises:
        HTTPException: If generation fails
    """
    try:
        logger.info(f"Received text generation request from {request.source_service}")

        # Convert API request to domain model
        domain_request = QwenRequestAdapter.api_to_domain_generation(
            request, source_service="backend-api"
        )

        # Process request through qwen service
        domain_response = await qwen_service.generate_text(domain_request)

        # Convert domain response to API model
        api_response = QwenResponseAdapter.domain_to_api_generation(domain_response)

        logger.info(
            f"Text generation completed: {domain_response.request_id} "
            f"(success: {domain_response.success})"
        )

        return api_response

    except Exception as e:
        logger.error(f"Text generation failed: {e}")

        # Don't raise HTTPException for business logic errors
        # Return error response with success=False instead
        return QwenResponseAdapter.create_error_generation_response(
            f"Text generation failed: {e}"
        )


@router.post(
    "/validate",
    response_model=QwenSemanticValidationResponse,
    summary="Perform semantic validation",
    description=(
        "Perform semantic validation using qwen-service "
        "(audio-processor compatibility)"
    ),
)
async def validate_semantically(
    request: QwenSemanticValidationRequest,
    qwen_service: QwenServiceInterface = Depends(get_qwen_service),  # noqa: B008
) -> QwenSemanticValidationResponse:
    """
    Perform semantic validation using qwen-service

    Args:
        request: Semantic validation request
        qwen_service: Qwen service instance
        settings: Application settings

    Returns:
        Semantic validation response

    Raises:
        HTTPException: If validation fails
    """
    try:
        logger.info(
            "Received semantic validation request (audio-processor compatibility)"
        )

        # Convert API request to domain model
        domain_request = QwenRequestAdapter.api_to_domain_validation(
            request, source_service="audio-processor"
        )

        # Process request through qwen service
        domain_response = await qwen_service.validate_semantically(domain_request)

        # Convert domain response to API model
        api_response = QwenResponseAdapter.domain_to_api_validation(domain_response)

        logger.info(
            "Semantic validation completed: %s (success: %s)",
            getattr(domain_response, "request_id", "unknown"),
            domain_response.success,
        )

        return api_response

    except Exception as e:
        logger.error(f"Semantic validation failed: {e}")

        # Don't raise HTTPException for business logic errors
        # Return error response with success=False instead
        return QwenResponseAdapter.create_error_validation_response(
            f"Semantic validation failed: {e}"
        )


@router.get(
    "/health",
    response_model=QwenHealthResponse,
    summary="Check qwen service health",
    description="Check if qwen-service and call management system are healthy",
)
async def health_check(
    qwen_service: QwenServiceInterface = Depends(get_qwen_service),  # noqa: B008
) -> QwenHealthResponse:
    """
    Check qwen service health

    Args:
        qwen_service: Qwen service instance
        settings: Application settings

    Returns:
        Health check response
    """
    try:
        # Perform health check
        is_healthy = await qwen_service.health_check()

        response = QwenHealthResponse(
            healthy=is_healthy,
            service="qwen-call-manager",
            timestamp=datetime.now(timezone.utc).isoformat().replace("+00:00", "Z"),
            details={
                "qwen_service_healthy": is_healthy,
                "call_manager_status": "operational" if is_healthy else "degraded",
            },
        )

        logger.debug(f"Health check completed: {is_healthy}")
        return response

    except Exception as e:
        logger.error(f"Health check failed: {e}")

        # Return unhealthy response
        return QwenHealthResponse(
            healthy=False,
            service="qwen-call-manager",
            timestamp=datetime.now(timezone.utc).isoformat().replace("+00:00", "Z"),
            details={
                "error": str(e),
                "call_manager_status": "error",
            },
        )


@router.get(
    "/status",
    response_model=QwenStatusResponse,
    summary="Get system status",
    description="Get comprehensive status of qwen call management system",
)
async def get_status(
    container: DIContainer = Depends(get_container),  # noqa: B008
) -> QwenStatusResponse:
    """
    Get comprehensive system status

    Args:
        container: DI container instance
        settings: Application settings

    Returns:
        System status response

    Raises:
        HTTPException: If status retrieval fails
    """
    try:
        # Get call manager for system status
        from app.services.qwen.services.qwen_call_manager_impl import (
            QwenCallManagerImpl,
        )

        call_manager = container.get_service(QwenCallManagerImpl)

        # Get system status
        status_data = await call_manager.get_system_status()

        # Convert to API response format
        response = QwenStatusResponse(
            queue_status=status_data["queue_status"],
            resource_status=status_data["resource_status"],
            uptime=status_data["uptime"],
            total_requests=status_data["total_requests"],
            successful_requests=status_data.get("successful_requests", 0),
            failed_requests=status_data.get("failed_requests", 0),
        )

        logger.debug("System status retrieved successfully")
        return response

    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system status: {e}",
        ) from e


@router.get(
    "/metrics",
    response_model=QwenMetricsResponse,
    summary="Get performance metrics",
    description="Get detailed performance metrics for monitoring and analysis",
)
async def get_metrics(
    container: DIContainer = Depends(get_container),  # noqa: B008
) -> QwenMetricsResponse:
    """
    Get performance metrics

    Args:
        container: DI container instance
        settings: Application settings

    Returns:
        Performance metrics response

    Raises:
        HTTPException: If metrics retrieval fails
    """
    try:
        # Get resource manager for metrics
        from app.shared.services.generic_resource_manager import GenericResourceManager

        resource_manager = container.get_service(GenericResourceManager)

        # Get performance metrics
        metrics_data = await resource_manager.get_performance_metrics()

        # For now, return simplified metrics
        # In a real implementation, you would collect and aggregate metrics over time
        from app.services.qwen.models.metrics_data_api import MetricsDataAPI

        current_metrics = MetricsDataAPI(
            requests_per_minute=0.0,  # Would be calculated from actual data
            average_response_time=metrics_data.get("average_response_time", 0.0),
            success_rate=metrics_data.get("success_rate", 0.0),
            error_rate=100.0 - metrics_data.get("success_rate", 0.0),
            queue_wait_time=0.0,  # Would be calculated from queue metrics
            gpu_utilization=0.0,  # Would be calculated from resource metrics
        )

        response = QwenMetricsResponse(
            current=current_metrics,
            last_hour=current_metrics,  # Placeholder
            last_day=current_metrics,  # Placeholder
            timestamp=datetime.now(timezone.utc).isoformat().replace("+00:00", "Z"),
        )

        logger.debug("Performance metrics retrieved successfully")
        return response

    except Exception as e:
        logger.error(f"Failed to get performance metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get performance metrics: {e}",
        ) from e


def initialize_qwen_api(container: DIContainer) -> None:
    """
    Initialize qwen API with DI container

    Args:
        container: DI container instance
    """
    global _container  # noqa: PLW0603
    _container = container
    logger.info("Qwen API initialized with DI container")
