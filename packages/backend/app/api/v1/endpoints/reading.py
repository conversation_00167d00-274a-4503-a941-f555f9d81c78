"""
阅读相关 API 端点
"""

from typing import Any

from fastapi import APIRouter


router = APIRouter()


@router.get("/contents")
async def get_contents() -> dict[str, Any]:
    """获取阅读内容列表"""
    return {"message": "Reading contents endpoint"}


@router.get("/contents/{content_id}")
async def get_content(content_id: int) -> dict[str, Any]:
    """获取特定内容详情"""
    return {"content_id": content_id, "message": "Content detail endpoint"}
