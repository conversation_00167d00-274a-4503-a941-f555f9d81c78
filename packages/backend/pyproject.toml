[tool.poetry]
name = "arabic-learning-backend"
version = "1.0.0"
description = "阿拉伯语学习平台后端 API"
authors = ["Arabic Learning Platform Team <<EMAIL>>"]
readme = "README.md"
packages = [{ include = "app" }]

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.104.0"
uvicorn = { extras = ["standard"], version = "^0.24.0" }
sqlalchemy = "^2.0.0"
alembic = "^1.12.0"
psycopg2-binary = "^2.9.7"
redis = "^5.0.0"
pydantic = "^2.0.0"
pydantic-settings = "^2.0.0"
python-jose = { extras = ["cryptography"], version = "^3.3.0" }
passlib = { extras = ["bcrypt"], version = "^1.7.4" }
python-multipart = "^0.0.6"
python-dotenv = "^1.0.0"
celery = "^5.3.0"
httpx = "^0.25.0"
jinja2 = "^3.1.0"
aiofiles = "^23.2.0"
python-slugify = "^8.0.0"
pillow = "^10.0.0"
email-validator = "^2.0.0"
loguru = "^0.7.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
ruff = "^0.1.0"
mypy = "^1.5.0"
pre-commit = "^3.4.0"
factory-boy = "^3.3.0"
faker = "^19.6.0"
httpx = "^0.25.0"
# Removed: black, isort, flake8 (replaced by ruff)

[tool.poetry.scripts]
backend = "app.main:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# Removed [tool.black] and [tool.isort] - replaced by Ruff

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
# Enable namespace packages for relative imports
namespace_packages = true
explicit_package_bases = true
# Set the package root
mypy_path = "."

[[tool.mypy.overrides]]
module = ["sqlalchemy.*", "alembic.*", "celery.*", "redis.*"]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short --cov=app --cov-report=term-missing"
asyncio_mode = "auto"

[tool.coverage.run]
source = ["app"]
omit = ["*/tests/*", "*/venv/*", "*/__pycache__/*"]

# ===== RUFF CONFIGURATION (Replaces Black + Flake8 + isort) =====
[tool.ruff]
line-length = 88
target-version = "py39"

# Exclude directories
exclude = [
  ".eggs",
  ".git",
  ".hg",
  ".mypy_cache",
  ".tox",
  ".venv",
  "build",
  "dist",
  "__pycache__",
]

# Ruff linting configuration
select = [
  "E",   # pycodestyle errors
  "W",   # pycodestyle warnings
  "F",   # Pyflakes
  "I",   # isort (import sorting)
  "N",   # pep8-naming
  "UP",  # pyupgrade
  "B",   # flake8-bugbear
  "C4",  # flake8-comprehensions
  "PIE", # flake8-pie
  "PT",  # flake8-pytest-style
  "RET", # flake8-return
  "SIM", # flake8-simplify
  "ARG", # flake8-unused-arguments
  "PTH", # flake8-use-pathlib
  "PL",  # Pylint rules
  "RUF", # Ruff-specific rules
]

# Ignore specific rules for compatibility
ignore = [
  "E203",    # Whitespace before ':' (Black compatibility)
  "PLR0913", # Too many arguments (sometimes necessary)
  "PLR0912", # Too many branches (sometimes necessary)
  "PLR0915", # Too many statements (sometimes necessary)
]

[tool.ruff.lint.isort]
# isort configuration (replaces [tool.isort])
known-first-party = ["app"]
lines-after-imports = 2

[tool.ruff.format]
# Black-compatible formatting
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
docstring-code-format = true
