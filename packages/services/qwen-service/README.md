# Qwen Text Generation Service

A high-performance text generation microservice powered by Qwen3-32B model using llama-cpp-python backend with GPU acceleration.

## Features

- **High-Quality Text Generation**: Powered by Qwen3-32B Q5_0 quantized model
- **Thinking Mode**: Advanced reasoning capabilities with structured thinking
- **Arabic Language Learning Focus**: Specialized system prompt for Arabic language learning assistance
- **GPU Acceleration**: Optimized for NVIDIA RTX 5090 with CUDA support
- **RESTful API**: FastAPI-based web service with comprehensive endpoints
- **Memory Management**: Intelligent GPU memory management and monitoring

## Model Information

- **Model**: Qwen3-32B-Q5_0.gguf (21.1 GB)
- **Location**: `/home/<USER>/website_project/models/nlp/Qwen3-32B-Q5_0.gguf`
- **Backend**: llama-cpp-python with CUDA support
- **Context Length**: 32,768 tokens (expandable to 38,912 for complex tasks)

## Installation

1. **Prerequisites**:

   - Python 3.9-3.12
   - CUDA-compatible GPU (RTX 5090 recommended)
   - Poetry package manager

2. **Install Dependencies**:

   ```bash
   cd /home/<USER>/website_project/packages/services/qwen-service
   poetry install
   ```

3. **GPU Support**:
   The service requires llama-cpp-python with CUDA support. If installation fails, you may need to reinstall with specific CUDA flags:
   ```bash
   poetry run pip uninstall llama-cpp-python -y
   CMAKE_ARGS="-DLLAMA_CUBLAS=on" poetry run pip install llama-cpp-python --no-cache-dir
   ```

## Usage

### Start the Service

```bash
# Development mode
poetry run qwen-service

# Or directly with Python
poetry run python src/main.py
```

The service will start on `http://localhost:8004` by default.

### API Endpoints

- **POST /api/v1/generate**: Generate text with various parameters
- **GET /api/v1/model/info**: Get model information and status
- **POST /api/v1/model/unload**: Unload model to free GPU memory
- **POST /api/v1/model/reload**: Reload model
- **GET /api/v1/health**: Health check endpoint

### Example Request

```python
import requests

response = requests.post("http://localhost:8004/api/v1/generate", json={
    "prompt": "How do I pronounce the Arabic word مرحبا?",
    "temperature": 0.7,
    "enable_thinking": True
})

print(response.json()["generated_text"])
```

## Configuration

The service uses centralized model management and follows project configuration patterns. Key settings:

- **Model Path**: Automatically resolved from centralized models directory
- **GPU Layers**: -1 (use all GPU layers for maximum performance)
- **Context Length**: 32,768 tokens (auto-expanded for complex tasks)
- **Memory Management**: Automatic GPU memory monitoring and cleanup

### Default Behavior Configuration

The following default behaviors can be configured in `src/core/config.py`:

- `DEFAULT_ENABLE_THINKING`: Whether to enable thinking mode by default (default: `True`)
- `DEFAULT_CLEAR_CACHE_AFTER`: Whether to clear GPU cache after generation by default (default: `True`)
- `DEFAULT_UNLOAD_MODEL_AFTER`: Whether to unload model after generation by default (default: `False`)

These defaults can be overridden on a per-request basis by specifying the corresponding parameters in the API request.

## Development

### Code Quality

The project uses Ruff for linting and formatting:

```bash
# Check code quality
poetry run ruff check src/

# Format code
poetry run ruff format src/
```

## Architecture

The service follows the project's microservice patterns:

```
src/
├── api/           # FastAPI routes and dependencies
├── core/          # Core business logic and configuration
├── models/        # Pydantic data models
└── main.py        # Application entry point
```

## GPU Requirements

- **Minimum**: 16GB VRAM
- **Recommended**: 32GB VRAM (RTX 5090)
- **CUDA Version**: 11.8 or higher

## Performance

- **Model Loading**: ~30-60 seconds (depending on system)
- **Generation Speed**: ~10-50 tokens/second (varies by complexity)
- **Memory Usage**: ~20-22GB VRAM when fully loaded
