# Qwen Text Generation Service Environment Configuration
# Copy this file to .env and modify as needed

# Service Configuration
# HOST=0.0.0.0
# PORT=8002
# DEBUG=true
# LOG_LEVEL=INFO

# Model Configuration (optional overrides)
# MODEL_PATH=./models/nlp/Qwen3-32B-Q5_0.gguf
# DEVICE=cuda
# N_GPU_LAYERS=-1
# N_CTX=32768
# N_BATCH=512

# Generation Parameters (optional overrides)
# DEFAULT_TEMPERATURE=0.7
# DEFAULT_TOP_P=0.8
# DEFAULT_MAX_TOKENS=32768

# Memory Management
# AUTO_CLEAR_MEMORY=false
# MEMORY_THRESHOLD_GB=28.0

# Note: Most configuration is handled in config.py with sensible defaults.
# Only add environment variables here for sensitive information or
# deployment-specific overrides.
