"""
Qwen text generation service core implementation

Provides text generation capabilities using Qwen3-32B model with llama-cpp-python backend.
Supports thinking mode, task-specific optimization, and GPU-only operation.
"""

import gc
import time
from pathlib import Path
from typing import Any, Optional

from llama_cpp import <PERSON>lama
from loguru import logger

from .config import Settings, get_cuda_manager, get_settings


class TextGenerationService:
    """Core text generation service using Qwen3-32B model"""

    def __init__(self, settings: Optional[Settings] = None):
        """Initialize the text generation service"""
        self.settings = settings or get_settings()
        self.cuda_manager = get_cuda_manager()
        self.model: Optional[Llama] = None
        self._model_loaded = False

        # Validate CUDA availability
        self.cuda_manager.validate_device_config()
        self.device_info = self.cuda_manager.get_gpu_info()

        logger.info("Initializing Qwen text generation service")
        logger.info(f"Model path: {self.settings.MODEL_PATH}")
        logger.info(f"Device info: {self.device_info}")

    def load_model(self) -> None:
        """Load the Qwen model"""
        if self._model_loaded:
            logger.info("Model already loaded")
            return

        model_path = Path(self.settings.MODEL_PATH)
        logger.info(f"Loading Qwen model from: {model_path}")

        try:
            # Load model with RTX 5090 optimized settings
            self.model = Llama(
                model_path=str(model_path),
                n_gpu_layers=self.settings.N_GPU_LAYERS,  # -1 = use all GPU layers
                n_ctx=self.settings.N_CTX,  # 16384 - conservative for stability
                n_batch=self.settings.N_BATCH,  # 256 - optimized for RTX 5090
                n_threads=self.settings.N_THREADS,
                verbose=True,  # Enable verbose for debugging
                use_mmap=True,  # Use memory mapping
                use_mlock=False,  # Disable memory lock for stability
            )

            self._model_loaded = True
            logger.info("Qwen model loaded successfully")

        except Exception as e:
            logger.error(f"Failed to load Qwen model: {e}")
            raise RuntimeError(f"Model loading failed: {e}") from e

    def unload_model(self) -> dict[str, Any]:
        """Unload model to free GPU memory"""
        if not self._model_loaded or self.model is None:
            return {"success": True, "message": "Model not loaded"}

        try:
            logger.info("Unloading Qwen model...")

            # Delete model instance
            del self.model
            self.model = None
            self._model_loaded = False

            # Force garbage collection
            gc.collect()

            logger.info("Qwen model unloaded successfully")
            return {
                "success": True,
                "message": "Model unloaded successfully",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            }

        except Exception as e:
            logger.error(f"Failed to unload model: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            }

    def reload_model(self) -> dict[str, Any]:
        """Reload the model"""
        try:
            logger.info("Reloading Qwen model...")

            # Unload existing model
            unload_result = self.unload_model()
            if not unload_result["success"]:
                return unload_result

            # Load model again
            self.load_model()

            return {
                "success": True,
                "message": "Model reloaded successfully",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            }

        except Exception as e:
            logger.error(f"Failed to reload model: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            }

    def is_model_loaded(self) -> bool:
        """Check if model is loaded"""
        return self._model_loaded and self.model is not None

    def get_model_info(self) -> dict[str, Any]:
        """Get model information"""
        model_path = Path(self.settings.MODEL_PATH)
        file_size_gb = (
            model_path.stat().st_size / (1024**3) if model_path.exists() else 0.0
        )

        return {
            "model_path": str(model_path),
            "model_loaded": self._model_loaded,
            "file_size_gb": round(file_size_gb, 1),
            "context_length": self.settings.N_CTX,
            "gpu_layers": self.settings.N_GPU_LAYERS,
            "device_info": self.device_info,
            "backend": "llama-cpp-python",
        }

    def _detect_thinking_mode(
        self, prompt: str, enable_thinking: Optional[bool]
    ) -> bool:
        """Detect if thinking mode should be enabled"""
        if enable_thinking is not None:
            return enable_thinking

        # If not explicitly specified, use the default from settings
        # unless auto-detection is needed based on prompt content
        if self.settings.DEFAULT_ENABLE_THINKING:
            return True

        # Auto-detect based on keywords
        thinking_keywords = [
            "分析",
            "解释",
            "推理",
            "思考",
            "计算",
            "证明",
            "比较",
            "analyze",
            "explain",
            "reasoning",
            "think",
            "calculate",
            "prove",
            "compare",
            "为什么",
            "怎样",
            "如何",
            "原因",
            "方法",
            "步骤",
            "why",
            "how",
            "reason",
            "method",
            "step",
            "process",
        ]

        prompt_lower = prompt.lower()
        return any(keyword in prompt_lower for keyword in thinking_keywords)

    def _build_prompt_with_system(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        enable_thinking: Optional[bool] = None,
    ) -> str:
        """Build complete prompt with system prompt and thinking tags"""

        # Use default system prompt if none provided
        if system_prompt is None:
            system_prompt = self.settings.DEFAULT_SYSTEM_PROMPT

        # Build prompt with Qwen format
        if system_prompt:
            final_prompt = f"<|im_start|>system\n{system_prompt}<|im_end|>\n<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n"
        else:
            final_prompt = (
                f"<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n"
            )

        # Add thinking tags if needed
        final_prompt = self._add_thinking_tags(final_prompt, enable_thinking)

        return final_prompt

    def _add_thinking_tags(self, prompt: str, enable_thinking: Optional[bool]) -> str:
        """Add thinking mode tags to prompt"""
        if enable_thinking is True:
            # Add /think tag
            if "<|im_start|>user\n" in prompt:
                return prompt.replace("<|im_start|>user\n", "<|im_start|>user\n/think ")
            return "/think " + prompt
        if enable_thinking is False:
            # Add /no_think tag
            if "<|im_start|>user\n" in prompt:
                return prompt.replace(
                    "<|im_start|>user\n", "<|im_start|>user\n/no_think "
                )
            return "/no_think " + prompt

        # No explicit thinking control
        return prompt

    def _get_generation_params(
        self,
        enable_thinking: bool = False,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        top_k: Optional[int] = None,
        min_p: Optional[float] = None,
        repeat_penalty: Optional[float] = None,
        presence_penalty: Optional[float] = None,
        stop: Optional[list[str]] = None,
    ) -> dict[str, Any]:
        """Get optimized generation parameters"""

        # Base parameters
        if enable_thinking:
            params = {
                "max_tokens": self.settings.DEFAULT_MAX_TOKENS,
                "temperature": self.settings.THINKING_TEMPERATURE,
                "top_p": self.settings.THINKING_TOP_P,
                "top_k": self.settings.THINKING_TOP_K,
                "min_p": self.settings.DEFAULT_MIN_P,
                "repeat_penalty": self.settings.DEFAULT_REPEAT_PENALTY,
                "presence_penalty": self.settings.THINKING_PRESENCE_PENALTY,
            }
        else:
            params = {
                "max_tokens": self.settings.DEFAULT_MAX_TOKENS,
                "temperature": self.settings.DEFAULT_TEMPERATURE,
                "top_p": self.settings.DEFAULT_TOP_P,
                "top_k": self.settings.DEFAULT_TOP_K,
                "min_p": self.settings.DEFAULT_MIN_P,
                "repeat_penalty": self.settings.DEFAULT_REPEAT_PENALTY,
                "presence_penalty": self.settings.DEFAULT_PRESENCE_PENALTY,
            }

        # Apply user overrides
        if temperature is not None:
            params["temperature"] = temperature
        if top_p is not None:
            params["top_p"] = top_p
        if top_k is not None:
            params["top_k"] = top_k
        if min_p is not None:
            params["min_p"] = min_p
        if repeat_penalty is not None:
            params["repeat_penalty"] = repeat_penalty
        if presence_penalty is not None:
            params["presence_penalty"] = presence_penalty
        if stop is not None:
            params["stop"] = stop

        return params

    def _clean_thinking_content(self, text: str) -> str:
        """Clean thinking content from generated text"""
        import re

        # Remove <think>...</think> tags and content
        think_pattern = r"<think>.*?</think>\s*"
        cleaned_text = re.sub(think_pattern, "", text, flags=re.DOTALL)

        # Handle unclosed <think> tags
        if "<think>" in cleaned_text:
            parts = cleaned_text.split("<think>")
            cleaned_text = parts[0] if parts else cleaned_text

        # Clean up extra whitespace
        cleaned_text = re.sub(r"\n\s*\n", "\n\n", cleaned_text.strip())

        # Return original if cleaning resulted in empty text
        if not cleaned_text.strip():
            logger.warning(
                "Thinking content cleaning resulted in empty text, returning original"
            )
            return text.strip()

        return cleaned_text

    def generate_text(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        top_k: Optional[int] = None,
        min_p: Optional[float] = None,
        repeat_penalty: Optional[float] = None,
        presence_penalty: Optional[float] = None,
        stop: Optional[list[str]] = None,
        enable_thinking: Optional[bool] = None,
    ) -> dict[str, Any]:
        """Generate text using the Qwen model"""

        if not self.is_model_loaded():
            raise RuntimeError("Model not loaded. Call load_model() first.")

        if not prompt.strip():
            raise ValueError("Prompt cannot be empty")

        logger.info(f"Generating text for prompt (length: {len(prompt)})")

        start_time = time.time()

        try:
            # Detect thinking mode
            is_thinking_mode = self._detect_thinking_mode(prompt, enable_thinking)

            # Get generation parameters
            generation_params = self._get_generation_params(
                enable_thinking=is_thinking_mode,
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                min_p=min_p,
                repeat_penalty=repeat_penalty,
                presence_penalty=presence_penalty,
                stop=stop,
            )

            # Build final prompt
            final_prompt = self._build_prompt_with_system(
                prompt, system_prompt, is_thinking_mode
            )

            logger.info(f"Thinking mode: {is_thinking_mode}")
            logger.info(f"Generation params: {generation_params}")

            # Generate text
            logger.info("Generating text...")
            response = self.model.create_completion(
                prompt=final_prompt, **generation_params
            )

            end_time = time.time()
            processing_time = end_time - start_time

            # Extract generated text
            generated_text = response["choices"][0]["text"]

            # Clean thinking content
            generated_text = self._clean_thinking_content(generated_text)

            # Build result
            result = {
                "generated_text": generated_text,
                "prompt": prompt,
                "system_prompt": system_prompt,
                "final_prompt": final_prompt,
                "processing_time": processing_time,
                "model_path": self.settings.MODEL_PATH,
                "generation_params": generation_params,
                "usage": response.get("usage", {}),
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "thinking_mode": is_thinking_mode,
                "finish_reason": response["choices"][0].get("finish_reason"),
            }

            logger.info(f"Text generation completed in {processing_time:.2f}s")
            logger.info("=== GENERATED TEXT CONTENT ===")
            logger.info(f"{generated_text}")
            logger.info("=== END GENERATED TEXT ===")
            return result

        except Exception as e:
            logger.error(f"Text generation failed: {e}")
            raise RuntimeError(f"Text generation failed: {e}") from e
