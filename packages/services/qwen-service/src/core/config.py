"""
Qwen text generation service configuration

Configuration Philosophy:
- Default values in this file provide sensible, production-ready settings
- Environment variables (.env) should only contain sensitive information
- Non-sensitive configuration can be overridden via environment variables if needed
- This approach keeps .env files minimal and focused on secrets
"""

from functools import lru_cache
from pathlib import Path
from typing import Optional

from loguru import logger
from pydantic import ValidationInfo, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application configuration"""

    # Service root directory (qwen-service root)
    BASE_DIR: Path = Path(__file__).resolve().parent.parent.parent

    # Distributed model configuration - model stored locally in service assets
    MODEL_PATH: str = "./assets/Qwen3-32B-Q5_0.gguf"
    DEVICE: str = "cuda"  # cuda only - no CPU fallback

    # Service configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8004  # Changed from 8002 to avoid conflict with word-analyzer
    DEBUG: bool = True

    # Model configuration (RTX 5090 optimized with full optimization flags)
    N_GPU_LAYERS: int = -1  # Use all GPU layers
    N_CTX: int = 8192  # total context window size(input + output = N_CTX)
    N_BATCH: int = 128  # Batch size (tested optimal balance for RTX 5090)
    N_THREADS: Optional[int] = None  # Auto-detect

    # Generation defaults (reasonable limits for RTX 5090)
    DEFAULT_MAX_TOKENS: int = 4096  # Balanced for output quality and memory
    DEFAULT_TEMPERATURE: float = 0.7
    DEFAULT_TOP_P: float = 0.8
    DEFAULT_TOP_K: int = 20
    DEFAULT_MIN_P: float = 0.0
    DEFAULT_REPEAT_PENALTY: float = 1.0
    DEFAULT_PRESENCE_PENALTY: float = 1.5

    # Thinking mode parameters
    THINKING_TEMPERATURE: float = 0.6
    THINKING_TOP_P: float = 0.95
    THINKING_TOP_K: int = 20
    THINKING_PRESENCE_PENALTY: float = 1.5

    # System prompt
    DEFAULT_SYSTEM_PROMPT: str = "You are a professional Arabic language learning assistant. Help users understand Arabic vocabulary, grammar, pronunciation, and cultural context. Provide clear explanations and examples that aid in learning Modern Standard Arabic and common dialects. When appropriate, include transliteration to help with pronunciation."

    # Memory management
    AUTO_CLEAR_MEMORY: bool = False
    MEMORY_THRESHOLD_GB: float = 28.0  # Clear memory if usage exceeds this

    # Default request parameters
    DEFAULT_ENABLE_THINKING: bool = True  # Disable thinking mode to save GPU memory
    DEFAULT_CLEAR_CACHE_AFTER: bool = True  # Default to clear cache after generation
    DEFAULT_UNLOAD_MODEL_AFTER: bool = False  # Default to keep model loaded

    # Logging configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"

    @field_validator("MODEL_PATH")
    @classmethod
    def validate_model_path(cls, v: str, info: ValidationInfo) -> str:
        """Validate that model file exists"""
        if "BASE_DIR" not in info.data:
            # Fallback to service root directory
            base_dir = Path(__file__).resolve().parent.parent.parent
        else:
            base_dir = info.data["BASE_DIR"]

        # If model path is relative, make it relative to service root directory
        model_path = Path(v) if Path(v).is_absolute() else base_dir / v

        if not model_path.exists():
            msg = f"Model file does not exist: {model_path}"
            raise ValueError(msg)
        if not model_path.is_file():
            msg = f"Model path is not a file: {model_path}"
            raise ValueError(msg)
        if model_path.suffix != ".gguf":
            msg = f"Model file must be in GGUF format: {model_path}"
            raise ValueError(msg)

        # Check file size (should be around 21GB for Qwen3-32B-Q5_0)
        file_size_gb = model_path.stat().st_size / (1024**3)
        if file_size_gb < 15.0:  # Minimum reasonable size
            logger.warning(
                f"Model file seems small ({file_size_gb:.1f}GB), expected ~21GB"
            )

        logger.info(f"Valid Qwen model found: {model_path} ({file_size_gb:.1f}GB)")
        return str(model_path.resolve())

    @field_validator("DEVICE")
    @classmethod
    def validate_device(cls, v: str) -> str:
        """Validate device configuration - GPU only"""
        if v != "cuda":
            msg = "Only CUDA GPU is supported. CPU fallback is not allowed."
            raise ValueError(msg)
        return v

    class Config:
        # In Pydantic V2+, the env_file path is relative to the current working dir
        # We build an absolute path here to ensure it can always be found
        env_file = str(Path(__file__).resolve().parent.parent.parent / ".env")
        case_sensitive = True
        extra = "ignore"


class CudaManager:
    """Manages CUDA availability detection for llama-cpp-python"""

    def __init__(self, settings: Settings):
        self.settings = settings
        self._cuda_available: Optional[bool] = None

    @property
    def cuda_available(self) -> bool:
        """Check CUDA availability (cached)"""
        if self._cuda_available is None:
            self._cuda_available = self._check_cuda_availability()
        return self._cuda_available

    def _check_cuda_availability(self) -> bool:
        """Check if CUDA is available for llama-cpp-python"""
        try:
            # For llama-cpp-python, we check if CUDA libraries are available
            import subprocess

            result = subprocess.run(
                ["nvidia-smi"], capture_output=True, text=True, timeout=10, check=False
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
            logger.warning(f"CUDA availability check failed: {e}")
            return False

    def validate_device_config(self) -> None:
        """Validate device configuration against CUDA availability"""
        if not self.cuda_available:
            error_msg = (
                "CUDA not available, but configuration requires GPU. "
                "Please check CUDA installation and nvidia-smi command."
            )
            raise RuntimeError(error_msg)
        logger.info("CUDA validation passed - GPU mode enabled for llama-cpp-python")

    def get_gpu_info(self) -> dict:
        """Get GPU information"""
        try:
            import subprocess

            result = subprocess.run(
                [
                    "nvidia-smi",
                    "--query-gpu=name,memory.total,driver_version",
                    "--format=csv,noheader,nounits",
                ],
                capture_output=True,
                text=True,
                timeout=10,
                check=False,
            )
            if result.returncode == 0:
                lines = result.stdout.strip().split("\n")
                gpus = []
                for i, line in enumerate(lines):
                    parts = line.split(", ")
                    if len(parts) >= 3:
                        gpus.append(
                            {
                                "id": i,
                                "name": parts[0].strip(),
                                "memory_total_gb": float(parts[1]) / 1024,
                                "driver_version": parts[2].strip(),
                            }
                        )
                return {
                    "cuda_available": True,
                    "gpu_count": len(gpus),
                    "gpu_devices": gpus,
                    "preferred_device": "cuda",
                }
        except Exception as e:
            logger.warning(f"Failed to get GPU info: {e}")

        return {
            "cuda_available": self.cuda_available,
            "gpu_count": 0,
            "gpu_devices": [],
            "preferred_device": "cuda",
        }


@lru_cache
def get_settings() -> Settings:
    """
    Get configuration instance.

    Use lru_cache to ensure there is only one Settings instance globally
    avoiding repeated configuration loading.
    """
    return Settings()


@lru_cache
def get_cuda_manager() -> CudaManager:
    """Get CUDA manager instance (singleton)"""
    return CudaManager(get_settings())


class ModelValidator:
    """Utility class for model validation"""

    @staticmethod
    def validate_gguf_model(model_path: Path) -> tuple[bool, list[str]]:
        """
        Validate GGUF model file

        Returns:
            tuple: (is_valid, issues)
        """
        issues = []

        if not model_path.exists():
            issues.append(f"Model file does not exist: {model_path}")
            return False, issues

        if not model_path.is_file():
            issues.append(f"Model path is not a file: {model_path}")
            return False, issues

        if model_path.suffix != ".gguf":
            issues.append(f"Model file must be in GGUF format: {model_path}")
            return False, issues

        # Check file size
        file_size_gb = model_path.stat().st_size / (1024**3)
        if file_size_gb < 1.0:
            issues.append(f"Model file seems too small ({file_size_gb:.1f}GB)")
            return False, issues

        return True, []

    @staticmethod
    def get_model_info(model_path: Path) -> dict:
        """Get model information for logging/debugging"""
        info = {
            "path": str(model_path),
            "exists": model_path.exists(),
            "is_file": model_path.is_file() if model_path.exists() else False,
            "size_gb": 0.0,
            "format": "unknown",
        }

        if model_path.exists() and model_path.is_file():
            info["size_gb"] = model_path.stat().st_size / (1024**3)
            info["format"] = model_path.suffix

        return info


@lru_cache
def get_model_validator() -> ModelValidator:
    """Get model validator instance (singleton)"""
    return ModelValidator()
