"""
API dependencies for Qwen text generation service

Provides dependency injection for the text generation service and other shared resources.
"""

from typing import Optional

from core.text_generation_service import TextGenerationService
from fastapi import HTTPException
from loguru import logger


# Global service instance
_text_generation_service: Optional[TextGenerationService] = None


def set_text_generation_service(service: Optional[TextGenerationService]) -> None:
    """Set the global text generation service instance"""
    global _text_generation_service
    _text_generation_service = service
    if service is not None:
        logger.info("Text generation service instance set")
    else:
        logger.debug("Text generation service instance cleared")


def get_text_generation_service() -> TextGenerationService:
    """Get the text generation service instance"""
    if _text_generation_service is None:
        logger.error("Text generation service not initialized")
        raise HTTPException(
            status_code=500,
            detail="Text generation service not initialized. Service startup may have failed.",
        )
    return _text_generation_service


def ensure_model_loaded() -> TextGenerationService:
    """Ensure model is loaded and return service instance"""
    service = get_text_generation_service()

    if not service.is_model_loaded():
        logger.info("Model not loaded, attempting to load...")
        try:
            service.load_model()
            logger.info("Model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to load model: {e!s}")

    return service
