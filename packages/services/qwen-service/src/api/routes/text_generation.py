"""
Text generation API routes
"""

import time

from api.dependencies import ensure_model_loaded, get_text_generation_service
from core.text_generation_service import TextGenerationService
from fastapi import APIRouter, Depends, HTTPException
from loguru import logger
from models.schemas import (
    GenerateTextRequest,
    GenerateTextResponse,
    ModelActionResponse,
    ModelInfoResponse,
)


router = APIRouter()


@router.post("/generate", response_model=GenerateTextResponse)
async def generate_text(
    request: GenerateTextRequest,
    service: TextGenerationService = Depends(ensure_model_loaded),
) -> GenerateTextResponse:
    """Generate text using the Qwen model"""
    try:
        logger.info(f"Received text generation request: {request.prompt[:100]}...")

        # Generate text
        result = service.generate_text(
            prompt=request.prompt,
            system_prompt=request.system_prompt,
            temperature=request.temperature,
            top_p=request.top_p,
            top_k=request.top_k,
            min_p=request.min_p,
            repeat_penalty=request.repeat_penalty,
            presence_penalty=request.presence_penalty,
            stop=request.stop,
            enable_thinking=request.enable_thinking,
        )

        # Handle memory management options based on request parameters
        # (these now have defaults from config)
        memory_management = None
        if request.clear_cache_after or request.unload_model_after:
            memory_management = {}

            if request.clear_cache_after:
                # For llama-cpp-python, we can trigger garbage collection
                import gc

                gc.collect()
                memory_management["cache_cleared"] = True
                logger.info("GPU cache cleared after generation")

            if request.unload_model_after:
                unload_result = service.unload_model()
                memory_management["model_unloaded"] = unload_result
                logger.info("Model unloaded after generation")

        # Build response
        response_data = {**result, "memory_management": memory_management}

        return GenerateTextResponse(**response_data)

    except ValueError as e:
        logger.error(f"Invalid request: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except RuntimeError as e:
        logger.error(f"Generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error during text generation: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/model/info", response_model=ModelInfoResponse)
async def get_model_info(
    service: TextGenerationService = Depends(get_text_generation_service),
) -> ModelInfoResponse:
    """Get model information"""
    try:
        model_info = service.get_model_info()
        return ModelInfoResponse(**model_info)
    except Exception as e:
        logger.error(f"Failed to get model info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/model/load", response_model=ModelActionResponse)
async def load_model(
    service: TextGenerationService = Depends(get_text_generation_service),
) -> ModelActionResponse:
    """Load the model"""
    try:
        if service.is_model_loaded():
            return ModelActionResponse(
                success=True,
                message="Model already loaded",
                timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            )

        service.load_model()
        return ModelActionResponse(
            success=True,
            message="Model loaded successfully",
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
        )
    except Exception as e:
        logger.error(f"Failed to load model: {e}")
        return ModelActionResponse(
            success=False,
            message="Failed to load model",
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            error=str(e),
        )


@router.post("/model/unload", response_model=ModelActionResponse)
async def unload_model(
    service: TextGenerationService = Depends(get_text_generation_service),
) -> ModelActionResponse:
    """Unload the model to free GPU memory"""
    try:
        result = service.unload_model()
        return ModelActionResponse(**result)
    except Exception as e:
        logger.error(f"Failed to unload model: {e}")
        return ModelActionResponse(
            success=False,
            message="Failed to unload model",
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            error=str(e),
        )


@router.post("/model/reload", response_model=ModelActionResponse)
async def reload_model(
    service: TextGenerationService = Depends(get_text_generation_service),
) -> ModelActionResponse:
    """Reload the model"""
    try:
        result = service.reload_model()
        return ModelActionResponse(**result)
    except Exception as e:
        logger.error(f"Failed to reload model: {e}")
        return ModelActionResponse(
            success=False,
            message="Failed to reload model",
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            error=str(e),
        )
