"""
Health check API routes
"""

import time
from typing import Optional

from api.dependencies import get_text_generation_service
from core.text_generation_service import TextGenerationService
from fastapi import APIRouter, Depends
from loguru import logger
from models.schemas import HealthResponse


router = APIRouter()

# Track service start time for uptime calculation
_service_start_time: Optional[float] = None


def set_service_start_time() -> None:
    """Set the service start time"""
    global _service_start_time
    _service_start_time = time.time()


@router.get("/health", response_model=HealthResponse)
async def health_check(
    service: TextGenerationService = Depends(get_text_generation_service)
) -> HealthResponse:
    """Health check endpoint"""
    try:
        model_loaded = service.is_model_loaded()

        # Calculate uptime
        uptime_seconds = None
        if _service_start_time is not None:
            uptime_seconds = time.time() - _service_start_time

        return HealthResponse(
            status="healthy",
            version="1.0.0",
            model_loaded=model_loaded,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            uptime_seconds=uptime_seconds
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="unhealthy",
            version="1.0.0",
            model_loaded=False,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            uptime_seconds=None
        )


@router.get("/ready", response_model=HealthResponse)
async def readiness_check(
    service: TextGenerationService = Depends(get_text_generation_service)
) -> HealthResponse:
    """Readiness check endpoint - returns healthy only if model is loaded"""
    try:
        model_loaded = service.is_model_loaded()

        # Calculate uptime
        uptime_seconds = None
        if _service_start_time is not None:
            uptime_seconds = time.time() - _service_start_time

        status = "ready" if model_loaded else "not_ready"

        return HealthResponse(
            status=status,
            version="1.0.0",
            model_loaded=model_loaded,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            uptime_seconds=uptime_seconds
        )
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return HealthResponse(
            status="not_ready",
            version="1.0.0",
            model_loaded=False,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            uptime_seconds=None
        )
