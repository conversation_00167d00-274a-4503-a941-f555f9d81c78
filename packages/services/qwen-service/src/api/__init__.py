"""
API module for Qwen text generation service
"""

from fastapi import APIRouter

from .routes.health import router as health_router
from .routes.text_generation import router as text_generation_router


# Main API router
api_router = APIRouter()

# Include sub-routers
api_router.include_router(text_generation_router, prefix="/api/v1", tags=["text-generation"])
api_router.include_router(health_router, prefix="/api/v1", tags=["health"])

__all__ = ["api_router"]
