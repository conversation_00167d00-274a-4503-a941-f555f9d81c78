#!/usr/bin/env python3
"""
Qwen text generation service main entry point

Provides high-quality text generation using Qwen3-32B model with llama-cpp-python backend.
Supports thinking mode, task-specific optimization, and GPU-only operation.
"""

from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager

import uvicorn
from api import api_router
from api.dependencies import set_text_generation_service
from api.routes.health import set_service_start_time
from core.config import get_settings
from core.text_generation_service import TextGenerationService
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger


settings = get_settings()


@asynccontextmanager
async def lifespan(_app: FastAPI) -> AsyncGenerator[None, None]:
    """Lifespan event handler for startup and shutdown events"""
    # --- STARTUP ---
    text_generation_service = None
    try:
        logger.info("Initializing Qwen Text Generation Service...")

        # Set service start time for uptime tracking
        set_service_start_time()

        # Initialize text generation service
        text_generation_service = TextGenerationService()

        # Set global service instance for API routes to use
        set_text_generation_service(text_generation_service)

        # Load model during startup
        logger.info("Loading Qwen model during startup...")
        text_generation_service.load_model()

        logger.info("🚀 Qwen Text Generation Service started successfully")
        logger.info(f"Service running on {settings.HOST}:{settings.PORT}")
        logger.info(f"Model: {settings.MODEL_PATH}")
        logger.info(f"Device: {settings.DEVICE}")
        logger.info("Role: Arabic Language Learning Assistant")
        logger.info(
            f"Default thinking mode: {'Enabled' if settings.DEFAULT_ENABLE_THINKING else 'Disabled'}"
        )
        logger.info(
            f"Default cache clearing: {'Enabled' if settings.DEFAULT_CLEAR_CACHE_AFTER else 'Disabled'}"
        )
        logger.info(
            f"Default model unloading: {'Enabled' if settings.DEFAULT_UNLOAD_MODEL_AFTER else 'Disabled'}"
        )

    except Exception as e:
        logger.error(f"❌ Service startup failed: {e}")
        # In production, you might want to exit the application on startup failure
        # import sys
        # sys.exit(1)

    # Yield control to FastAPI
    yield

    # --- SHUTDOWN ---
    logger.info("Shutting down Qwen Text Generation Service...")

    try:
        # Clear global service instance first
        set_text_generation_service(None)
        logger.debug("Global text generation service instance cleared")

        # Cleanup the text generation service resources if it exists
        if text_generation_service is not None:
            try:
                # Unload model to free GPU memory
                if text_generation_service.is_model_loaded():
                    logger.info("Unloading Qwen model during shutdown...")
                    unload_result = text_generation_service.unload_model()

                    if unload_result.get("success", False):
                        logger.info("Qwen model unloaded successfully")
                    else:
                        logger.warning(
                            f"Model unloading reported issues: {unload_result.get('error', 'Unknown error')}"
                        )
                else:
                    logger.debug("Model was not loaded, skipping unload")

                # Clear CUDA manager resources if available
                if (
                    hasattr(text_generation_service, "cuda_manager")
                    and text_generation_service.cuda_manager
                ):
                    try:
                        # Reset CUDA optimizations if cleanup method exists
                        if hasattr(text_generation_service.cuda_manager, "cleanup"):
                            text_generation_service.cuda_manager.cleanup()
                            logger.debug("CUDA optimizations cleaned up")
                    except Exception as cuda_error:
                        logger.warning(f"Error during CUDA cleanup: {cuda_error}")

                logger.info("Qwen service resources cleared")

            except Exception as cleanup_error:
                logger.warning(
                    f"Error during text generation service cleanup: {cleanup_error}"
                )

            finally:
                # Clear the reference
                text_generation_service = None

        logger.success("🛑 Qwen Text Generation Service shutdown complete")

    except Exception as shutdown_error:
        logger.error(f"Error during service shutdown: {shutdown_error}")
        # Continue with shutdown even if cleanup fails


# FastAPI application
app = FastAPI(
    title="Qwen Text Generation Service",
    description="High-quality text generation service powered by Qwen3-32B model "
    "with thinking capabilities and GPU acceleration",
    version="1.0.0",
    lifespan=lifespan,
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Should restrict to specific domains in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Register API routes
app.include_router(api_router)


def main() -> None:
    """Main entry point for the service"""
    logger.info("Starting Qwen Text Generation Service...")

    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
    )


if __name__ == "__main__":
    main()
