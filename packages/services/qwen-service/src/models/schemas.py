"""
Data models for Qwen text generation service API
"""

from typing import Any, Optional

from core.config import get_settings
from pydantic import BaseModel, Field


# Get global settings
settings = get_settings()


class GenerateTextRequest(BaseModel):
    """Text generation request model"""

    prompt: str = Field(..., description="Input prompt text", min_length=1)
    system_prompt: Optional[str] = Field(
        settings.DEFAULT_SYSTEM_PROMPT, description="System prompt for the model"
    )
    temperature: Optional[float] = Field(
        None, description="Temperature parameter", ge=0.0, le=2.0
    )
    top_p: Optional[float] = Field(None, description="Top-p parameter", ge=0.0, le=1.0)
    top_k: Optional[int] = Field(None, description="Top-k parameter", ge=1, le=100)
    min_p: Optional[float] = Field(None, description="Min-p parameter", ge=0.0, le=1.0)
    repeat_penalty: Optional[float] = Field(
        None, description="Repeat penalty", ge=0.0, le=2.0
    )
    presence_penalty: Optional[float] = Field(
        None, description="Presence penalty", ge=0.0, le=2.0
    )
    stop: Optional[list[str]] = Field(None, description="Stop sequences")
    enable_thinking: Optional[bool] = Field(
        settings.DEFAULT_ENABLE_THINKING, description="Enable thinking mode"
    )

    # Memory management options
    clear_cache_after: bool = Field(
        settings.DEFAULT_CLEAR_CACHE_AFTER,
        description="Clear GPU cache after generation",
    )
    unload_model_after: bool = Field(
        settings.DEFAULT_UNLOAD_MODEL_AFTER, description="Unload model after generation"
    )


class GenerateTextResponse(BaseModel):
    """Text generation response model"""

    generated_text: str = Field(..., description="Generated text")
    prompt: str = Field(..., description="Original prompt")
    system_prompt: Optional[str] = Field(None, description="System prompt used")
    final_prompt: str = Field(..., description="Final prompt sent to model")
    processing_time: float = Field(..., description="Processing time in seconds")
    model_path: str = Field(..., description="Model file path")
    generation_params: dict[str, Any] = Field(
        ..., description="Generation parameters used"
    )
    usage: dict[str, Any] = Field(..., description="Token usage statistics")
    timestamp: str = Field(..., description="Generation timestamp")
    thinking_mode: bool = Field(..., description="Whether thinking mode was used")
    finish_reason: Optional[str] = Field(None, description="Reason generation finished")

    # Memory management results
    memory_management: Optional[dict[str, Any]] = Field(
        None, description="Memory management results"
    )


class ModelInfoResponse(BaseModel):
    """Model information response model"""

    model_path: str = Field(..., description="Model file path")
    model_loaded: bool = Field(..., description="Whether model is loaded")
    file_size_gb: float = Field(..., description="Model file size in GB")
    context_length: int = Field(..., description="Context length")
    gpu_layers: int = Field(..., description="Number of GPU layers")
    device_info: dict[str, Any] = Field(..., description="Device information")
    backend: str = Field(..., description="Backend used (llama-cpp-python)")


class ModelActionResponse(BaseModel):
    """Model action response (load/unload/reload)"""

    success: bool = Field(..., description="Whether action succeeded")
    message: str = Field(..., description="Action result message")
    timestamp: str = Field(..., description="Action timestamp")
    error: Optional[str] = Field(None, description="Error message if failed")


class HealthResponse(BaseModel):
    """Health check response model"""

    status: str = Field(..., description="Service status")
    version: str = Field(..., description="Service version")
    model_loaded: bool = Field(..., description="Whether model is loaded")
    timestamp: str = Field(..., description="Health check timestamp")
    uptime_seconds: Optional[float] = Field(
        None, description="Service uptime in seconds"
    )


class ErrorResponse(BaseModel):
    """Error response model"""

    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
    timestamp: str = Field(..., description="Error timestamp")
    request_id: Optional[str] = Field(None, description="Request ID for tracking")


# Request/Response type aliases for convenience
TextGenerationRequest = GenerateTextRequest
TextGenerationResponse = GenerateTextResponse
