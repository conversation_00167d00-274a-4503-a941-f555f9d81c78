[tool.poetry]
name = "qwen-service"
version = "1.0.0"
description = "Qwen3-32B text generation service with llama-cpp-python backend"
authors = ["Bayan Education Technology Co., Ltd. <<EMAIL>>"]
readme = "README.md"
packages = [
  { include = "api", from = "src" },
  { include = "core", from = "src" },
  { include = "models", from = "src" },
]

[tool.poetry.dependencies]
python = ">=3.9,<3.13"
# Core llama-cpp-python with CUDA support (RTX 5090 optimized)
llama-cpp-python = { version = "^0.3.9", extras = ["server"] }
# FastAPI and web framework
fastapi = "^0.104.0"
uvicorn = { extras = ["standard"], version = "^0.24.0" }
python-multipart = "^0.0.6"
# Data handling and validation
pydantic = "^2.0.0"
pydantic-settings = "^2.0.0"
numpy = "^2.0.2"
# Configuration and environment
python-dotenv = "^1.0.0"
# Logging
loguru = "^0.7.0"
# GPU monitoring and system utilities
psutil = "^5.9.0"
GPUtil = "^1.4.0"
redis = "^6.2.0"

[tool.poetry.group.dev.dependencies]
ruff = "^0.1.0"
mypy = "^1.5.0"
httpx = "^0.25.0"

[tool.poetry.scripts]
qwen-service = "src.main:main"

[[tool.poetry.source]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
priority = "primary"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true


# ===== RUFF CONFIGURATION (Replaces Black + Flake8 + isort + Pylint) =====
[tool.ruff]
line-length = 88
target-version = "py39"

# Exclude directories
exclude = [
  ".eggs",
  ".git",
  ".hg",
  ".mypy_cache",
  ".tox",
  ".venv",
  "build",
  "dist",
  "__pycache__",
]

# Comprehensive linting rules
select = [
  "E",   # pycodestyle errors
  "W",   # pycodestyle warnings
  "F",   # Pyflakes
  "I",   # isort (import sorting)
  "N",   # pep8-naming
  "UP",  # pyupgrade
  "B",   # flake8-bugbear
  "C4",  # flake8-comprehensions
  "PIE", # flake8-pie

  "RET", # flake8-return
  "SIM", # flake8-simplify
  "ARG", # flake8-unused-arguments
  "PTH", # flake8-use-pathlib
  "PL",  # Pylint rules
  "RUF", # Ruff-specific rules
  "A",   # flake8-builtins
  "COM", # flake8-commas
  "C90", # mccabe complexity
  "EM",  # flake8-errmsg
  "G",   # flake8-logging-format
  "ICN", # flake8-import-conventions
  "ISC", # flake8-implicit-str-concat
  "T20", # flake8-print
  "Q",   # flake8-quotes
]

# Ignore specific rules for compatibility and practicality
ignore = [
  "E203",    # Whitespace before ':' (formatter compatibility)
  "PLR0913", # Too many arguments (sometimes necessary)
  "PLR0912", # Too many branches (sometimes necessary)
  "PLR0915", # Too many statements (sometimes necessary)
  "COM812",  # Trailing comma missing (formatter handles this)
  "COM819",  # Trailing comma prohibited (formatter handles this)
  "ISC001",  # Implicitly concatenated string literals (formatter conflict)
  "Q000",    # Single quotes found but double quotes preferred
  "Q001",    # Single quote docstrings
  "Q002",    # Single quote multiline strings
  "Q003",    # Change outer quotes to avoid escaping inner quotes
  "B008",    # Do not perform function calls in argument defaults (FastAPI pattern)
  "PLW0603", # Using the global statement is discouraged (dependency injection pattern)
]

# Per-file ignores for specific patterns
[tool.ruff.lint.per-file-ignores]
"src/infrastructure/factory.py" = [
  "F401", # Allow unused imports for dependency injection factory
]
"src/infrastructure/container.py" = [
  "F401", # Allow unused imports for DI container
]
"src/*/__init__.py" = [
  "F401", # Allow unused imports for module re-exports
]

[tool.ruff.lint.isort]
# isort configuration
known-first-party = ["src"]
lines-after-imports = 2

[tool.ruff.format]
# Black-compatible formatting
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
docstring-code-format = true
