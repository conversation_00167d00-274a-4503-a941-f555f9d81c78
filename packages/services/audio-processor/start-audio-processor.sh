#!/bin/bash

# =============================================================================
# WhisperX Arabic Audio Processing Service - 一键启动脚本
# =============================================================================
# 
# 功能：
# - 自动检测和设置虚拟环境
# - 配置CUDA库路径
# - 启动音频处理微服务
# - 提供健康检查和错误处理
#
# 使用方法：
#   ./start-audio-processor.sh [选项]
#
# 选项：
#   --dev       开发模式（启用热重载）
#   --prod      生产模式（默认）
#   --check     仅检查环境，不启动服务
#   --help      显示帮助信息
#
# =============================================================================

set -euo pipefail  # 严格模式：遇到错误立即退出

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "${DEBUG:-false}" == "true" ]]; then
        echo -e "${PURPLE}[DEBUG]${NC} $1"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
${CYAN}WhisperX Arabic Audio Processing Service - 一键启动脚本${NC}

${YELLOW}使用方法:${NC}
    $0 [选项]

${YELLOW}选项:${NC}
    --dev       开发模式（启用热重载和详细日志）
    --prod      生产模式（默认，优化性能）
    --check     仅检查环境配置，不启动服务
    --debug     启用调试模式
    --help      显示此帮助信息

${YELLOW}示例:${NC}
    $0                  # 生产模式启动
    $0 --dev            # 开发模式启动
    $0 --check          # 检查环境配置
    $0 --debug --dev    # 调试模式下的开发启动

${YELLOW}服务信息:${NC}
    - 服务地址: http://localhost:8001
    - API文档: http://localhost:8001/docs
    - 健康检查: http://localhost:8001/health

EOF
}

# 获取脚本所在目录的绝对路径
get_script_dir() {
    local script_path
    script_path="$(readlink -f "${BASH_SOURCE[0]}")"
    dirname "$script_path"
}

# 检查必要的命令是否存在
check_dependencies() {
    log_info "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        missing_deps+=("python3")
    fi
    
    # 检查curl（用于健康检查）
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    # 检查nvidia-smi（GPU支持）
    if ! command -v nvidia-smi &> /dev/null; then
        log_warning "nvidia-smi 未找到，GPU加速可能不可用"
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少必要的依赖: ${missing_deps[*]}"
        log_error "请安装缺少的依赖后重试"
        exit 1
    fi
    
    log_success "系统依赖检查通过"
}

# 检测和设置虚拟环境
setup_virtual_environment() {
    local script_dir="$1"
    local venv_path="$script_dir/.venv"
    
    log_info "设置虚拟环境..."
    
    # 检查虚拟环境是否存在
    if [[ ! -d "$venv_path" ]]; then
        log_error "虚拟环境不存在: $venv_path"
        log_error "请先运行以下命令创建虚拟环境:"
        log_error "  cd $script_dir"
        log_error "  poetry install"
        exit 1
    fi
    
    # 检查虚拟环境中的Python
    local venv_python="$venv_path/bin/python"
    if [[ ! -x "$venv_python" ]]; then
        log_error "虚拟环境中的Python不可执行: $venv_python"
        exit 1
    fi
    
    # 设置虚拟环境
    export VIRTUAL_ENV="$venv_path"
    export PATH="$venv_path/bin:$PATH"
    
    log_debug "VIRTUAL_ENV: $VIRTUAL_ENV"
    log_debug "Python路径: $(which python)"
    log_success "虚拟环境设置完成"
}

# 配置CUDA库路径
setup_cuda_paths() {
    log_info "配置CUDA库路径..."
    
    local script_dir="$1"
    local venv_path="$script_dir/.venv"
    
    # 查找CUDNN库路径
    local cudnn_paths=()
    
    # 虚拟环境中的CUDNN
    local venv_cudnn="$venv_path/lib/python*/site-packages/nvidia/cudnn/lib"
    if compgen -G "$venv_cudnn" > /dev/null; then
        cudnn_paths+=($(echo $venv_cudnn))
    fi
    
    # 系统CUDA路径
    local system_cuda_paths=(
        "/usr/local/cuda/lib64"
        "/usr/local/cuda-*/lib64"
        "/opt/cuda/lib64"
    )
    
    for cuda_path in "${system_cuda_paths[@]}"; do
        if compgen -G "$cuda_path" > /dev/null; then
            cudnn_paths+=($(echo $cuda_path))
        fi
    done
    
    # 设置LD_LIBRARY_PATH
    if [[ ${#cudnn_paths[@]} -gt 0 ]]; then
        local cuda_lib_path
        cuda_lib_path=$(IFS=:; echo "${cudnn_paths[*]}")
        export LD_LIBRARY_PATH="$cuda_lib_path:${LD_LIBRARY_PATH:-}"
        log_success "CUDA库路径已设置: $cuda_lib_path"
        log_debug "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"
    else
        log_warning "未找到CUDA库路径，GPU加速可能不可用"
    fi
}

# 验证Python环境和依赖
validate_python_environment() {
    log_info "验证Python环境..."
    
    # 检查Python版本
    local python_version
    python_version=$(python --version 2>&1 | cut -d' ' -f2)
    log_debug "Python版本: $python_version"
    
    # 检查关键依赖
    local required_packages=("torch" "whisperx" "fastapi" "uvicorn")
    local missing_packages=()
    
    for package in "${required_packages[@]}"; do
        if ! python -c "import $package" &> /dev/null; then
            missing_packages+=("$package")
        fi
    done
    
    if [[ ${#missing_packages[@]} -gt 0 ]]; then
        log_error "缺少Python包: ${missing_packages[*]}"
        log_error "请运行 'poetry install' 安装依赖"
        exit 1
    fi
    
    # 检查CUDA支持
    local cuda_available
    cuda_available=$(python -c "import torch; print(torch.cuda.is_available())" 2>/dev/null || echo "False")
    
    if [[ "$cuda_available" == "True" ]]; then
        local gpu_name
        gpu_name=$(python -c "import torch; print(torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'N/A')" 2>/dev/null || echo "Unknown")
        log_success "CUDA支持已启用，GPU: $gpu_name"
    else
        log_warning "CUDA不可用，将使用CPU模式"
    fi
    
    log_success "Python环境验证通过"
}

# 检查端口是否被占用
check_port() {
    local port="$1"
    
    if command -v netstat &> /dev/null; then
        if netstat -tuln | grep -q ":$port "; then
            return 0  # 端口被占用
        fi
    elif command -v ss &> /dev/null; then
        if ss -tuln | grep -q ":$port "; then
            return 0  # 端口被占用
        fi
    else
        # 尝试连接端口
        if timeout 1 bash -c "</dev/tcp/localhost/$port" &> /dev/null; then
            return 0  # 端口被占用
        fi
    fi
    
    return 1  # 端口未被占用
}

# 等待服务启动
wait_for_service() {
    local port="$1"
    local max_attempts="${2:-30}"
    local attempt=1
    
    log_info "等待服务启动..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -s "http://localhost:$port/health" &> /dev/null; then
            log_success "服务已启动并响应健康检查"
            return 0
        fi
        
        log_debug "尝试 $attempt/$max_attempts: 等待服务响应..."
        sleep 2
        ((attempt++))
    done
    
    log_error "服务启动超时"
    return 1
}

# 启动服务
start_service() {
    local mode="$1"
    local script_dir="$2"
    local port="${3:-8001}"

    log_info "启动WhisperX音频处理服务..."

    # 检查端口
    if check_port "$port"; then
        log_warning "端口 $port 已被占用"
        log_info "尝试停止现有服务..."

        # 尝试优雅停止
        if curl -s -X POST "http://localhost:$port/shutdown" &> /dev/null; then
            log_info "已发送停止信号，等待服务关闭..."
            sleep 3
        fi

        # 如果仍然占用，提示用户
        if check_port "$port"; then
            log_error "端口 $port 仍被占用，请手动停止相关进程"
            log_error "可以使用以下命令查找进程: lsof -i :$port"
            exit 1
        fi
    fi

    # 切换到服务目录
    cd "$script_dir"

    # 根据模式设置参数
    local uvicorn_args=()

    if [[ "$mode" == "dev" ]]; then
        uvicorn_args+=(
            "--reload"
            "--log-level" "debug"
        )
        log_info "开发模式启动（热重载已启用）"
    else
        uvicorn_args+=(
            "--log-level" "info"
        )
        log_info "生产模式启动"
    fi

    # 启动服务
    log_info "执行命令: python src/main.py"
    log_info "服务将在 http://localhost:$port 启动"
    log_info "API文档: http://localhost:$port/docs"
    log_info "按 Ctrl+C 停止服务"

    # 启动服务并处理信号
    trap 'log_info "正在停止服务..."; exit 0' SIGINT SIGTERM

    python src/main.py
}

# 主函数
main() {
    local mode="prod"
    local check_only=false
    local script_dir

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dev)
                mode="dev"
                shift
                ;;
            --prod)
                mode="prod"
                shift
                ;;
            --check)
                check_only=true
                shift
                ;;
            --debug)
                export DEBUG=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 获取脚本目录
    script_dir=$(get_script_dir)
    log_debug "脚本目录: $script_dir"

    # 显示启动信息
    echo -e "${CYAN}"
    echo "=============================================="
    echo "  WhisperX Arabic Audio Processing Service"
    echo "=============================================="
    echo -e "${NC}"

    # 执行检查
    check_dependencies
    setup_virtual_environment "$script_dir"
    setup_cuda_paths "$script_dir"
    validate_python_environment

    if [[ "$check_only" == true ]]; then
        log_success "环境检查完成，所有配置正常"
        exit 0
    fi

    # 启动服务
    start_service "$mode" "$script_dir"
}

# 执行主函数
main "$@"

