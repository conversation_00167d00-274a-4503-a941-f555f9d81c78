# WhisperX Arabic Audio Processing Service ✨ Clean Architecture

High-performance Arabic audio transcription microservice based on WhisperX, featuring **advanced chapter detection** and **clean architecture design**. Specifically optimized for Arabic transcription, VAD detection, speaker diarization, and intelligent chapter segmentation.

## 🎯 Features

### Core Audio Processing

- 🎵 **Arabic Specialized**: Specifically optimized for Arabic transcription
- ⚡ **High Performance**: Uses faster-whisper-large-v3-turbo model, 8x faster than standard models
- 🎯 **Precise Timestamps**: Sentence-level and word-level timestamp generation
- 👥 **Speaker Diarization**: Optional multi-speaker identification
- 🔧 **Flexible Configuration**: Adjustable VAD parameters and processing options

### Advanced Chapter Detection ✨ NEW

- 📖 **Intelligent Chapter Detection**: Advanced Arabic text pattern matching with three detection modes
- 🔍 **Cross-Segment Matching**: Sophisticated algorithm for chapter titles spanning multiple segments
- ⏰ **Word-Level Timestamps**: Precise chapter boundary detection using word-level timing data
- 📄 **Segment Reorganization**: Automatic reorganization of transcription segments for chapter markers
- 🧠 **Semantic Validation**: AI-powered validation using Qwen language model
- 🔧 **Configurable Processing**: Extensive configuration options for Arabic text processing

### Clean Architecture Design ✨ NEW

- 🏗️ **Modular Services**: Specialized services with single responsibility principle
- 💉 **Dependency Injection**: Complete DI container and service factory implementation
- 🎯 **Interface-Driven**: Protocol-based design for maximum testability and flexibility
- 🔄 **Adapter Pattern**: Clean separation between API layer and business logic
- ⚙️ **Service Orchestration**: Sophisticated workflow management and error handling

## 📁 Directory Structure ✨ Clean Architecture

```
audio-processor/
├── src/                       # Source code (Clean Architecture)
│   ├── main.py               # FastAPI application entry point
│   ├── interfaces/           # 🎯 Protocol definitions (Dependency Inversion)
│   │   ├── audio_processing_service.py      # Audio processing service interface
│   │   ├── audio_processing_service_factory.py # Service factory interface
│   │   ├── audio_transcription_service.py   # Audio transcription service interface
│   │   ├── camel_tools_client.py           # Camel-tools client interface
│   │   ├── chapter_detection_service.py    # Chapter detection service interface
│   │   ├── chapter_splitting_service.py    # Chapter splitting service interface
│   │   ├── cross_segment_matching_service.py # Cross-segment matching interface
│   │   ├── external_service_clients.py     # External service clients interface
│   │   ├── qwen_service_client.py          # Qwen service client interface
│   │   ├── segment_reorganization_service.py # Segment reorganization interface
│   │   ├── text_preprocessing_service.py   # Text preprocessing interface
│   │   └── word_level_timestamp_service.py # Word-level timestamp interface
│   ├── services/             # 🔧 Business logic layer (Functional Modules)
│   │   ├── transcription/           # Audio transcription module
│   │   │   └── audio_transcription_service_impl.py
│   │   ├── chapter_detection/       # Chapter detection module
│   │   │   ├── chapter_detection_orchestrator_impl.py # Chapter detection orchestrator
│   │   │   ├── text_preprocessing_service_impl.py     # Arabic text preprocessing
│   │   │   ├── cross_segment_matching_service_impl.py # Cross-segment matching algorithms
│   │   │   ├── word_level_timestamp_service_impl.py   # Word-level timestamp calculations
│   │   │   └── segment_reorganization_service_impl.py # Segment reorganization service
│   │   ├── chapter_processing/      # Chapter processing module
│   │   │   └── chapter_splitting_service_impl.py # Chapter splitting service
│   │   └── orchestration/           # Main orchestration module
│   │       └── audio_processing_service_impl.py  # Main orchestration service
│   ├── infrastructure/       # 🏗️ Infrastructure layer (Dependency Injection)
│   │   ├── dicontainer.py           # DI container implementation
│   │   ├── audio_processing_service_factory_impl.py # Service factory implementation
│   │   ├── camel_tools_client_impl.py      # Camel-tools HTTP client
│   │   ├── qwen_service_client_impl.py     # Qwen service HTTP client
│   │   └── external_service_clients_impl.py # External service clients implementation
│   ├── adapters/            # 🔄 Adapter layer (Interface Adaptation)
│   │   ├── audio_processing_request_adapter.py # Audio processing request adapter
│   │   ├── audio_processing_response_adapter.py # Audio processing response adapter
│   │   └── file_upload_adapter.py          # File upload handling adapter
│   ├── core/                # ⚙️ Core configuration and utilities
│   │   ├── config.py                # Configuration factory functions
│   │   ├── settings.py              # Application settings and validation
│   │   ├── cuda_manager.py          # CUDA optimization manager
│   │   ├── model_validator.py       # Model validation utilities
│   │   └── transcription_service.py # Legacy transcription service
│   ├── models/              # 📋 Data models and schemas
│   │   ├── api/                     # API request/response models
│   │   ├── config/                  # Configuration models
│   │   ├── domain/                  # Domain models
│   │   └── services/                # Service-specific models
│   └── api/                 # 🌐 API layer (Presentation)
│       ├── dependencies.py         # FastAPI dependency injection
│       └── routes/                  # API route handlers
│           ├── health.py            # Health check endpoint
│           ├── models.py            # Model information endpoint
│           └── transcribe.py        # Transcription endpoint
├── assets/                   # 🎵 Model assets (Local storage)
│   ├── transcription/              # Transcription models
│   │   └── whisper/faster-whisper-large-v3-turbo-ct2/
│   ├── alignment/                  # Audio alignment models
│   │   └── arabic/jonatasgrosman-wav2vec2-large-xlsr-53-arabic/
│   └── diarization/                # Speaker diarization models
├── .vscode/                 # 🛠️ VSCode configuration
│   └── settings.json               # Development environment settings
├── .env.example             # Environment variables template
├── pyproject.toml           # Poetry configuration and dependency management
├── poetry.lock              # Locked dependency versions
├── start-audio-processor.sh # Service startup script
└── README.md                # This documentation
```

### 🏗️ Architecture Layers

**🎯 Interfaces Layer**: Protocol definitions implementing Dependency Inversion Principle

- Abstract interfaces for all services
- External service client protocols
- Clear contracts between layers

**🔧 Services Layer**: Business logic organized by functional modules

- **Transcription Module**: WhisperX integration and audio processing
- **Chapter Detection Module**: Advanced Arabic text pattern matching with specialized services
  - **Orchestrator**: Coordinates the complete chapter detection workflow
  - **Text Preprocessing**: Arabic language processing with camel-tools integration
  - **Cross-Segment Matching**: Complex algorithms for chapter titles spanning segments
  - **Word-Level Timestamps**: Precise timing calculations using WhisperX data
  - **Segment Reorganization**: Intelligent restructuring of transcription segments
- **Chapter Processing Module**: Chapter splitting and post-processing
- **Orchestration Module**: Main workflow coordination and service orchestration

**🏗️ Infrastructure Layer**: Dependency Injection and external integrations

- DI container for service lifecycle management
- Service factory with clean instantiation
- HTTP clients for camel-tools-api and qwen-service

**🔄 Adapters Layer**: Interface adaptation and data transformation

- Audio processing request/response model conversion
- File upload handling and validation
- External service data mapping

**📋 Models Layer**: Organized data models and schemas

- **API Models**: Request/response models for API endpoints
- **Config Models**: Configuration and settings models
- **Domain Models**: Core business domain models
- **Service Models**: Service-specific data transfer objects

## 🚀 Quick Start

### Requirements

- Python >= 3.9, < 3.13
- Poetry (Python dependency management)
- CUDA (recommended for GPU acceleration)
- FFmpeg

### Development Environment Setup

```bash
# 1. Run automatic setup script from project root
./scripts/setup-poetry-envs.sh

# 2. Or manually setup individual service
cd packages/services/audio-processor

# Configure Poetry to create virtual environment in project directory
poetry config virtualenvs.in-project true

# Install all dependencies (including development dependencies)
poetry install

# Activate virtual environment
source .venv/bin/activate
```

### Start Development Server

```bash
# Method 1: Use pnpm script from project root
pnpm audio-processor:dev

# Method 2: Run directly in service directory
cd packages/services/audio-processor
poetry run python src/main.py

# Method 3: Run in activated virtual environment
source .venv/bin/activate
python src/main.py
```

The service will start at `http://localhost:8001` with hot reload support.

### Testing

```bash
# Run unit tests
poetry run pytest

# Start the service for development
poetry run python src/main.py
```

## 📡 API Endpoints

### Health Check

```bash
GET /health
```

### Audio Transcription with Chapter Detection ✨ Enhanced

```bash
POST /transcribe
Content-Type: multipart/form-data

Parameters:
# Core Audio Processing
- file: Audio file (required)
- language: Transcription language (fixed: "ar" - Arabic)
- vad_onset: Voice activity detection onset threshold (default: 0.5)
- vad_offset: Voice activity detection offset threshold (default: 0.363)
- chunk_size: Chunk size in seconds (default: 30)
- enable_diarization: Enable speaker diarization (default: false)
- min_speakers: Minimum number of speakers (optional)
- max_speakers: Maximum number of speakers (optional)

# Chapter Detection ✨ NEW
- enable_chapter_detection: Enable intelligent chapter detection (default: false)
- chapter_markers: JSON array of Arabic chapter titles to search for (optional)
- chapter_timestamps: JSON array of timestamp-based chapter markers (optional)
- enable_chapter_splitting: Enable automatic chapter file splitting (default: false)
```

**Enhanced Response Format:**

```json
{
  "success": true,
  "message": "Audio processing completed successfully",
  "segments": [...],           // Transcription segments (reorganized if chapters detected)
  "chapters": [                // ✨ NEW: Detected chapters
    {
      "title": "الفصل الأول",
      "start_time": 45.2,
      "end_time": 120.8,
      "audio_file": "chapter_1.wav"  // If splitting enabled
    }
  ],
  "report": [...],            // ✨ NEW: Detailed processing report
  "language": "ar",
  "duration": 180.5,
  "processing_time": 12.3,
  "word_count": 450
}
```

### Model Information

```bash
GET /models/info
```

**Response includes service architecture info:**

```json
{
  "transcription_model": {
    "name": "faster-whisper-large-v3-turbo-ct2",
    "language": "ar",
    "device": "cuda"
  },
  "chapter_detection": {
    "enabled": true,
    "detection_modes": ["single-segment", "multi-segment", "cross-segment"],
    "semantic_validation": true,
    "external_services": {
      "camel_tools_api": "available",
      "qwen_service": "available"
    }
  },
  "architecture": {
    "design": "clean_architecture",
    "services": ["transcription", "chapter_detection", "chapter_splitting"],
    "dependency_injection": true
  }
}
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file for sensitive configuration:

```env
# Required: HuggingFace Token for speaker diarization
HF_TOKEN=your_huggingface_token_here

# External Service URLs (for chapter detection)
CAMEL_TOOLS_API_URL=http://localhost:8002
QWEN_SERVICE_API_URL=http://localhost:8000/api/v1/qwen

# Optional: Override Default Configuration
# Most users can leave these unset to use the sensible defaults

# Model Paths (uncomment to override defaults)
# MODEL_PATH=./assets/transcription/whisper/faster-whisper-large-v3-turbo-ct2
# ALIGN_MODEL_PATH=./assets/alignment/arabic/jonatasgrosman-wav2vec2-large-xlsr-53-arabic

# Device Configuration (uncomment to override defaults)
# DEVICE=cuda                    # cuda (recommended for performance) or cpu (fallback)
# COMPUTE_TYPE=float16           # float16 (GPU optimal), int8 (CPU/GPU balanced), float32 (CPU precise)

# CUDA Optimization Settings (uncomment to override defaults)
# ENABLE_TF32=true
# ENABLE_CUDNN_BENCHMARK=true
# ENABLE_CUDNN_DETERMINISTIC=false

# Service Configuration (uncomment to override defaults)
# HOST=0.0.0.0
# PORT=8001
# DEBUG=true

# Audio Processing Configuration (uncomment to override defaults)
# DEFAULT_LANGUAGE=ar
# DEFAULT_VAD_ONSET=0.5
# DEFAULT_VAD_OFFSET=0.363
# DEFAULT_CHUNK_SIZE=30

# Speaker Diarization Configuration (uncomment to override defaults)
# DEFAULT_ENABLE_DIARIZATION=false
# DEFAULT_MIN_SPEAKERS=
# DEFAULT_MAX_SPEAKERS=

# Logging Configuration (uncomment to override defaults)
# LOG_LEVEL=INFO

# Note: Enhanced text normalization options (remove_punctuation, normalize_digits,
# normalize_whitespace, normalize_english_case) are now mandatory in camel-tools-api
```

**Note**: The `.env` file should only contain sensitive information like API tokens and service URLs. All other configuration uses sensible defaults from `config.py`.

For a complete list of available environment variables, see `.env.example`.

### Model Configuration

- **Model Type**: faster-whisper-large-v3-turbo-ct2
- **Supported Language**: Arabic (ar) specialized
- **Performance**: 8x faster than standard large-v3
- **Accuracy**: Comparable to large-v3

## 🛠️ Development Workflow

### Dependency Management

```bash
# Add new production dependency
poetry add package-name

# Add development dependency
poetry add --group dev package-name

# Update dependencies
poetry update

# View dependency tree
poetry show --tree

# Export requirements.txt (if needed)
poetry export -f requirements.txt --output requirements.txt
```

### Code Quality Tools

```bash
# Format and fix all issues (main command)
poetry run ruff check src/ --fix && poetry run ruff format src/

# Type checking
poetry run mypy src/

# Complete quality check (CI/CD pipeline)
poetry run ruff check src/ && poetry run ruff format src/ --check && poetry run mypy src/
```

### VSCode Development Environment

This project has complete VSCode development environment support configured:

#### Automatic Configuration Features

- **Python Interpreter**: Automatically recognizes Poetry virtual environment
- **Code Completion**: Complete IntelliSense and type hints
- **Code Formatting**: Ruff automatic formatting
- **Import Sorting**: Ruff automatic import sorting
- **Syntax Checking**: Ruff real-time syntax checking
- **Type Checking**: MyPy static type checking
- **Test Support**: pytest integrated testing

#### Recommended VSCode Extensions

- `ms-python.python` - Python language support
- `charliermarsh.ruff` - Ruff code formatting and checking
- `ms-python.mypy-type-checker` - MyPy type checking
- `tamasfe.even-better-toml` - TOML file support

#### Using Workspace File

It is recommended to use the `arabic-learning-platform.code-workspace` file in the project root to open the project, which allows:

- Managing multiple microservices simultaneously
- Each service uses an independent Python interpreter
- Unified development environment configuration

### Troubleshooting

#### Common Development Issues and Solutions

1. **Poetry Virtual Environment Issues**

   ```bash
   # Recreate virtual environment
   poetry env remove python
   poetry install

   # Check virtual environment location
   poetry env info --path
   ```

2. **GPU Not Available**

   ```bash
   # Check NVIDIA driver
   nvidia-smi

   # Check PyTorch CUDA support
   poetry run python -c "import torch; print(f'CUDA: {torch.cuda.is_available()}')"
   ```

3. **Model Loading Failed**

   ```bash
   # Check transcription model files
   ls -la assets/transcription/whisper/faster-whisper-large-v3-turbo-ct2/

   # Check alignment model files
   ls -la assets/alignment/arabic/

   # Check environment variables
   poetry run python -c "import os; print(os.environ.get('MODEL_PATH'))"
   ```

4. **Dependency Conflicts**

   ```bash
   # Update lock file
   poetry lock --no-update

   # Reinstall dependencies
   poetry install --sync
   ```

5. **VSCode Python Interpreter Issues**
   - Press `Ctrl+Shift+P` to open command palette
   - Search for "Python: Select Interpreter"
   - Select `./packages/services/audio-processor/.venv/bin/python`

## 📊 Performance Optimization

### GPU Acceleration

Ensure CUDA and cuDNN are properly installed on your system:

```bash
# Check NVIDIA driver
nvidia-smi

# Check CUDA version
nvcc --version

# Verify PyTorch CUDA support
poetry run python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}, Device: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else \"CPU\"}')"
```

### Configuration Management

The service uses a layered configuration approach:

1. **Default Configuration** (`settings.py`): Sensible defaults for all settings
2. **Environment Variables** (`.env`): Override defaults when needed (mainly for secrets)

**Default Configuration** includes:

- GPU acceleration with CUDA
- Optimized CUDA settings (TF32, cuDNN benchmark)
- Arabic language specialization
- Performance-tuned VAD parameters

**Environment Variables** (optional overrides):

```env
# Only add to .env if you need to override defaults
DEVICE=cuda                    # Default: cuda
COMPUTE_TYPE=float16          # Default: float16
ENABLE_TF32=true             # Default: true
ENABLE_CUDNN_BENCHMARK=true  # Default: true
```

See `.env.example` for all available configuration options.

### Memory Optimization

- For memory-constrained environments, use `COMPUTE_TYPE=int8`
- Adjust `chunk_size` parameter to balance memory usage and processing speed
- Consider using model quantization techniques

### Batch Processing Optimization

- For large numbers of files, consider implementing batch processing API
- Use asynchronous processing to improve concurrent performance

## 🔧 Service Verification

### Manual Service Verification

The audio-processor service can be verified through manual testing and API calls.

### Service Functionality Verification

```bash
# Verify service initialization and dependency injection
poetry run python -c "
from src.infrastructure.audio_processing_service_factory_impl import AudioProcessingServiceFactoryImpl
from src.core.config import get_settings
import asyncio

async def verify():
    settings = get_settings()
    factory = AudioProcessingServiceFactoryImpl(settings)
    await factory.initialize_services()
    print('✅ All services initialized successfully!')
    await factory.cleanup_services()

asyncio.run(verify())
"
```

### API Verification

```bash
# Basic API verification
curl -X POST "http://localhost:8001/transcribe" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test_audio.wav" \
  -F "language=ar"
```

### Chapter Detection Verification ✨ NEW

```bash
# Verify with chapter markers
curl -X POST "http://localhost:8001/transcribe" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@arabic_audiobook.wav" \
  -F "language=ar" \
  -F "enable_chapter_detection=true" \
  -F 'chapter_markers=["الفصل الأول", "الفصل الثاني", "الخاتمة"]'

# Verify with timestamp-based chapters
curl -X POST "http://localhost:8001/transcribe" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@arabic_audiobook.wav" \
  -F "language=ar" \
  -F "enable_chapter_detection=true" \
  -F 'chapter_timestamps=[120.5, 300.2, 450.8]'

# Verify with chapter splitting
curl -X POST "http://localhost:8001/transcribe" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@arabic_audiobook.wav" \
  -F "language=ar" \
  -F "enable_chapter_detection=true" \
  -F "enable_chapter_splitting=true" \
  -F 'chapter_markers=["الفصل الأول", "الفصل الثاني"]'
```

### Performance Benchmarks (Target Goals)

- Health check: < 1 second
- Model info: < 2 seconds
- 5s audio transcription: < 10 seconds
- Chapter detection (5 markers): < 30 seconds

### Architecture Validation

You can validate the clean architecture implementation by testing service creation:

```bash
# Test service factory and dependency injection
poetry run python -c "
from src.infrastructure.audio_processing_service_factory_impl import AudioProcessingServiceFactoryImpl
from src.core.config import get_settings
import asyncio

async def test():
    settings = get_settings()
    factory = AudioProcessingServiceFactoryImpl(settings)
    await factory.initialize_services()

    # Test all services can be created
    transcription = factory.create_transcription_service()
    chapter_detection = factory.create_chapter_detection_service()
    chapter_splitting = factory.create_chapter_splitting_service()
    audio_processing = factory.create_audio_processing_service()

    print('✅ All services created successfully!')
    print(f'Transcription: {type(transcription).__name__}')
    print(f'Chapter Detection: {type(chapter_detection).__name__}')
    print(f'Chapter Splitting: {type(chapter_splitting).__name__}')
    print(f'Audio Processing: {type(audio_processing).__name__}')

    await factory.cleanup_services()

asyncio.run(test())
"
```

## 🏆 Architecture Achievements ✨ Latest Updates

### Clean Architecture Implementation with Functional Modules

This service represents a **complete clean architecture refactoring** that successfully transformed a monolithic 1,826-line chapter detection service into a modular, testable, and maintainable system organized by functional modules.

#### Key Achievements

- **✅ Zero Functionality Loss**: All original complex algorithms preserved
- **✅ Three Detection Modes**: Single-segment, multi-segment, cross-segment matching
- **✅ Word-Level Precision**: Exact timestamp calculations using WhisperX data
- **✅ Segment Reorganization**: Intelligent restructuring of transcription segments
- **✅ Arabic Language Support**: Full camel-tools integration with batch processing
- **✅ Semantic Validation**: AI-powered validation using Qwen language model
- **✅ Dependency Injection**: Complete DI container and service factory
- **✅ Interface-Driven Design**: Protocol-based architecture for maximum flexibility
- **✅ Backward Compatibility**: API endpoints remain unchanged
- **✅ Functional Module Organization**: Services organized by domain for better maintainability

### Service Specialization ✨ Updated Architecture

The services are now organized into **functional modules** with clear separation of concerns:

#### Transcription Module

- **AudioTranscriptionServiceImpl**: WhisperX integration and audio processing

#### Chapter Detection Module

- **ChapterDetectionOrchestratorImpl**: Workflow coordination and validation
- **TextPreprocessingServiceImpl**: Arabic text processing and stem extraction
- **CrossSegmentMatchingServiceImpl**: Complex pattern matching algorithms
- **WordLevelTimestampServiceImpl**: Precise timing calculations
- **SegmentReorganizationServiceImpl**: Intelligent segment restructuring

#### Chapter Processing Module

- **ChapterSplittingServiceImpl**: Chapter splitting and audio file generation

#### Orchestration Module

- **AudioProcessingServiceImpl**: Main workflow coordination and service orchestration

### Latest Architecture Improvements ✨ Recent Updates

#### Functional Module Organization

The latest architecture update introduces **functional module organization** that provides:

1. **Domain-Driven Structure**: Services are grouped by their functional domain

   - `transcription/`: Audio transcription functionality
   - `chapter_detection/`: All chapter detection related services
   - `chapter_processing/`: Chapter splitting and post-processing
   - `orchestration/`: Main workflow coordination

2. **Improved Maintainability**: Related services are co-located, making it easier to:

   - Understand service relationships
   - Modify domain-specific functionality
   - Add new features within existing domains

3. **Clear Separation of Concerns**: Each module has a specific responsibility:

   - **Transcription**: Handles audio-to-text conversion
   - **Chapter Detection**: Manages complex chapter identification algorithms
   - **Chapter Processing**: Handles chapter-based audio manipulation
   - **Orchestration**: Coordinates the entire workflow

4. **Enhanced Developer Experience**:
   - Easier navigation through the codebase
   - Clearer understanding of service dependencies
   - Better IDE support for code completion and refactoring

## 📚 Related Documentation

- [Project Architecture](../../../docs/architecture.md) - Complete system architecture
- [Clean Architecture Principles](../../../docs/architecture.md#audio-processor-清洁架构重构) - Implementation details
- [Service Configuration](./src/core/settings.py) - Detailed configuration options
- [Service Interfaces](./src/interfaces/) - Protocol definitions and contracts

## 🤝 Contributing

### Development Guidelines

1. **Follow Clean Architecture**: Maintain separation of concerns
2. **Use Dependency Injection**: All services should be injected, not instantiated
3. **Write Tests**: Each service should have comprehensive unit tests
4. **Maintain Interfaces**: Changes should be backward compatible
5. **Document Changes**: Update both code and documentation

### Contribution Process

1. Fork the project
2. Create a feature branch
3. Follow the clean architecture patterns
4. Add tests for new functionality
5. Update documentation
6. Create a Pull Request

## 📄 License

MIT License
