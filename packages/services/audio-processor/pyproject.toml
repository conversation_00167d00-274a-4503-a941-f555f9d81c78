[tool.poetry]
name = "audio-processor"
version = "1.0.0"
description = "WhisperX 音频处理服务"
authors = ["Bayan Education Technology Co., Ltd. <<EMAIL>>"]
readme = "README.md"
packages = [
  { include = "api", from = "src" },
  { include = "core", from = "src" },
  # Removed "models" package - it's a data models module, not a distributable package
  # The models are imported directly via src.models in the application
]

[tool.poetry.dependencies]
python = ">=3.9,<3.13"
whisperx = "^3.3.4"
torch = { version = "^2.7.0", source = "pytorch" }
torchaudio = { version = "^2.7.0", source = "pytorch" }
transformers = "^4.30.0"
faster-whisper = "^1.1.1"
librosa = "^0.10.0"
soundfile = "^0.12.0"
pydub = "^0.25.0"
fastapi = "^0.104.0"
uvicorn = { extras = ["standard"], version = "^0.24.0" }
python-multipart = "^0.0.6"
numpy = "^2.0.2"
pandas = "^2.0.0"
pydantic = "^2.0.0"
pydantic-settings = "^2.0.0"
python-dotenv = "^1.0.0"
loguru = "^0.7.0"
httpx = "^0.25.0"
redis = "^6.2.0"
psutil = "^7.0.0"

[tool.poetry.group.dev.dependencies]
ruff = "^0.5.3"
mypy = "^1.5.0"

[tool.poetry.scripts]
audio-processor = "src.main:main"

[[tool.poetry.source]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
priority = "primary"

[[tool.poetry.source]]
name = "pytorch"
url = "https://download.pytorch.org/whl/cu128"
priority = "explicit"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# Removed [tool.black] and [tool.isort] - replaced by Ruff

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true


# ===== RUFF CONFIGURATION (Replaces Black + Flake8 + isort + Pylint) =====
[tool.ruff]
line-length = 88
target-version = "py39"

# Exclude directories (equivalent to Black's extend-exclude)
exclude = [
  ".eggs",
  ".git",
  ".hg",
  ".mypy_cache",
  ".tox",
  ".venv",
  "build",
  "dist",
  "__pycache__",
]

[tool.ruff.lint]
# Comprehensive linting rules (replaces Pylint + Flake8)
select = [
  "E",   # pycodestyle errors
  "W",   # pycodestyle warnings
  "F",   # Pyflakes
  "I",   # isort (import sorting)
  "N",   # pep8-naming
  "UP",  # pyupgrade
  "B",   # flake8-bugbear
  "C4",  # flake8-comprehensions
  "PIE", # flake8-pie

  "RET", # flake8-return
  "SIM", # flake8-simplify
  "ARG", # flake8-unused-arguments
  "PTH", # flake8-use-pathlib
  "PL",  # Pylint rules (comprehensive coverage)
  "RUF", # Ruff-specific rules
  "A",   # flake8-builtins
  "COM", # flake8-commas
  "C90", # mccabe complexity
  "DJ",  # flake8-django
  "EM",  # flake8-errmsg
  "G",   # flake8-logging-format
  "ICN", # flake8-import-conventions
  "ISC", # flake8-implicit-str-concat
  "T20", # flake8-print
  "Q",   # flake8-quotes
]

# Ignore specific rules for compatibility and practicality
ignore = [
  "E203",    # Whitespace before ':' (formatter compatibility)
  "PLR0913", # Too many arguments (sometimes necessary)
  "PLR0912", # Too many branches (sometimes necessary)
  "PLR0915", # Too many statements (sometimes necessary)
  "COM812",  # Trailing comma missing (formatter handles this)
  "COM819",  # Trailing comma prohibited (formatter handles this)
  "ISC001",  # Implicitly concatenated string literals (formatter conflict)
  "Q000",    # Single quotes found but double quotes preferred (formatter handles this)
  "Q001",    # Single quote docstrings (formatter handles this)
  "Q002",    # Single quote multiline strings (formatter handles this)
  "Q003",    # Change outer quotes to avoid escaping inner quotes (formatter handles this)
  "B008",    # Do not perform function calls in argument defaults (FastAPI pattern)
  "PLW0603", # Using the global statement is discouraged (dependency injection pattern)
  "E402",    # Module level import not at top of file (factory pattern with circular dependency prevention)
  "EM101",   # Exception must not use a string literal (factory pattern error messages)
]

[tool.ruff.lint.isort]
# isort configuration (replaces [tool.isort])
known-first-party = ["src"]
lines-after-imports = 2

# Per-file ignores for specific patterns
[tool.ruff.lint.per-file-ignores]
"src/infrastructure/factory.py" = [
  "E402", # Allow delayed imports in factory pattern
  "F401", # Allow unused imports for dependency injection factory
]
"src/infrastructure/container.py" = [
  "E402", # Allow delayed imports in DI container
]

[tool.ruff.format]
# Black-compatible formatting
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
docstring-code-format = true
