# Environment Variables Example for Audio Processor Service
# Copy this file to .env and fill in your actual values

# =============================================================================
# REQUIRED: HuggingFace Token for Speaker Diarization
# =============================================================================
# Get your token from: https://huggingface.co/settings/tokens
# Required for speaker diarization functionality
HF_TOKEN=your_huggingface_token_here

# =============================================================================
# OPTIONAL: Override Default Configuration
# =============================================================================
# The following variables can be set to override defaults in config.py
# Most users can leave these unset to use the sensible defaults

# Model Paths (uncomment to override defaults)
# MODEL_PATH=./models/whisper/faster-whisper-large-v3-turbo-ct2
# ALIGN_MODEL_PATH=./models/align_model

# Device Configuration (uncomment to override defaults)
# DEVICE=cuda                    # cuda (recommended for performance) or cpu (fallback)
# COMPUTE_TYPE=float16           # float16 (GPU optimal), int8 (CPU/GPU balanced), float32 (CPU precise)

# CUDA Optimization Settings (uncomment to override defaults)
# ENABLE_TF32=true
# ENABLE_CUDNN_BENCHMARK=true
# ENABLE_CUDNN_DETERMINISTIC=false

# Service Configuration (uncomment to override defaults)
# HOST=0.0.0.0
# PORT=8001
# DEBUG=true

# Audio Processing Configuration (uncomment to override defaults)
# DEFAULT_LANGUAGE=ar
# DEFAULT_VAD_ONSET=0.5
# DEFAULT_VAD_OFFSET=0.363
# DEFAULT_CHUNK_SIZE=30

# Speaker Diarization Configuration (uncomment to override defaults)
# DEFAULT_ENABLE_DIARIZATION=false
# DEFAULT_MIN_SPEAKERS=
# DEFAULT_MAX_SPEAKERS=

# Logging Configuration (uncomment to override defaults)
# LOG_LEVEL=INFO
