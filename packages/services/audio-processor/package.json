{"name": "audio-processor", "version": "1.0.0", "description": "WhisperX Audio Processing Service", "main": "src/main.py", "scripts": {"dev": "poetry run python src/main.py", "install-deps": "poetry install", "setup": "poetry install"}, "keywords": ["whisperx", "audio", "transcription", "speech-to-text", "arabic"], "author": "Arabic Learning Platform Team", "license": "MIT", "dependencies": {}, "devDependencies": {}}