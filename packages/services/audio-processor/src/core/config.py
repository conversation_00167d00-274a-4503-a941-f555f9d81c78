"""
Configuration factory functions

Provides factory functions for creating configuration instances,
implementing singleton pattern for efficient resource usage.
"""

from functools import lru_cache

from .cuda_manager import <PERSON>udaManager
from .model_validator import ModelValidator
from .settings import Settings


# Advantages of not directly instantiating:
# 1. Can avoid repeated instantiation of configuration in different places
# 2. Can add configuration preprocessing, such as loading additional configs,
# validating key parameters, setting default values, etc.
# 3. Lazy loading, only create configuration objects when really needed.
#    Improving performance by only creating configuration objects when really needed.
# 4. Convenient for unit testing, as configuration can be injected into tests
# 5. Easy to share configuration between different components
@lru_cache
def get_settings() -> Settings:
    """
    Get configuration instance.

    Use lru_cache to ensure there is only one Settings instance globally
    avoiding repeated configuration loading.
    """
    return Settings()


@lru_cache
def get_model_validator() -> ModelValidator:
    """Get model validator instance (singleton)"""
    return ModelValidator()


@lru_cache
def get_cuda_manager() -> CudaManager:
    """Get CUDA manager instance (singleton)"""
    return CudaManager(get_settings())
