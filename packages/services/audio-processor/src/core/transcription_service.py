"""
WhisperX transcription service core logic
"""

import time
from pathlib import Path
from typing import Any

import whisperx  # type: ignore
from loguru import logger

from models.api.model_info import ModelInfo
from models.config.compute_config import ComputeConfig
from models.config.model_state import ModelState
from models.config.transcription_config import TranscriptionConfig

from .config import get_cuda_manager, get_settings


# 这里增加一条注释，看看是否能自动同步到远程服务器

# Import diarization functionality
try:
    from whisperx.diarize import DiarizationPipeline  # type: ignore
except ImportError:
    DiarizationPipeline = None


class TranscriptionService:
    """WhisperX transcription service"""

    def __init__(self) -> None:
        self.settings = get_settings()
        self.cuda_manager = get_cuda_manager()
        self.models = ModelState()
        self.compute = ComputeConfig()

        # Configure CUDA optimization settings
        self.cuda_manager.configure_cuda_optimizations()

        self._load_model()

    def load_model(self) -> None:
        """Public method: Load model"""
        self._load_model()

    def _load_model(self) -> None:
        """Load WhisperX model"""
        try:
            logger.info(f"Loading model: {self.settings.MODEL_PATH}")

            # Validate device configuration using CUDA manager
            self.cuda_manager.validate_device_config()
            device = self.settings.DEVICE
            compute_type = self.cuda_manager.get_optimal_compute_type()
            logger.info(
                f"Using {device.upper()} mode"
                f"Device: {device}, Compute type: {compute_type}"
            )

            # Load transcription model
            model_path = self.settings.MODEL_PATH

            # Configure VAD options
            vad_options = {
                "vad_onset": self.settings.DEFAULT_VAD_ONSET,
                "vad_offset": self.settings.DEFAULT_VAD_OFFSET,
                "chunk_size": self.settings.DEFAULT_CHUNK_SIZE,
            }

            logger.debug(f"VAD options: {vad_options}")

            # Model loading optimized specifically for Arabic
            self.models.model = whisperx.load_model(
                model_path,
                device=device,
                compute_type=compute_type,
                vad_options=vad_options,
                language="ar",  # Specify Arabic, avoid language detection
            )

            self.compute.device = device
            self.compute.compute_type = compute_type

            # Ensure CUDA optimizations remain enabled after model loading
            self.cuda_manager.ensure_optimizations_enabled()

            logger.success(
                f"Model loaded successfully - Device: {device}, "
                f"Compute type: {compute_type}"
            )

        except Exception as e:
            logger.error(f"Model loading failed: {e}")
            raise

    def _load_align_model(self) -> None:
        """Load Arabic alignment model"""
        if self.models.align_model is None or self.models.current_align_lang != "ar":
            try:
                logger.info("Loading Arabic alignment model")

                # Use configured alignment model path (already validated in config.py)
                local_model_path = Path(self.settings.ALIGN_MODEL_PATH)

                # Try to use local model first, fallback to download if needed
                if local_model_path.exists() and any(local_model_path.iterdir()):
                    logger.info(
                        f"Using local Arabic alignment model: {local_model_path}"
                    )
                    try:
                        (
                            self.models.align_model,
                            self.models.align_metadata,
                        ) = whisperx.load_align_model(
                            language_code="ar",
                            device=self.compute.device,
                            model_name=str(local_model_path),
                        )
                    except Exception as e:
                        logger.warning(
                            f"Failed to load local model: {e}. "
                            f"Downloading model automatically..."
                        )
                        # Fallback to online download
                        (
                            self.models.align_model,
                            self.models.align_metadata,
                        ) = whisperx.load_align_model(
                            language_code="ar", device=self.compute.device
                        )
                else:
                    logger.info("Downloading Arabic alignment model automatically...")
                    (
                        self.models.align_model,
                        self.models.align_metadata,
                    ) = whisperx.load_align_model(
                        language_code="ar", device=self.compute.device
                    )

                self.models.current_align_lang = "ar"
                logger.success("Arabic alignment model loaded successfully")
            except (ImportError, AttributeError, RuntimeError, OSError) as e:
                logger.warning(f"Alignment model loading failed: {e}")
                self.models.align_model = None
                self.models.align_metadata = None

    def _load_diarization_model(self) -> None:
        """Load speaker diarization model"""
        if self.models.diarize_model is None:
            try:
                if DiarizationPipeline is None:
                    logger.warning(
                        "DiarizationPipeline not available, skipping speaker "
                        "diarization model loading"
                    )
                    return

                logger.info("Loading speaker diarization model")
                hf_token = self.settings.HF_TOKEN
                device = self.compute.device
                self.models.diarize_model = DiarizationPipeline(
                    use_auth_token=hf_token, device=device
                )
                logger.success("Speaker diarization model loaded successfully")
            except (ImportError, AttributeError, RuntimeError, OSError) as e:
                logger.warning(f"Speaker diarization model loading failed: {e}")
                self.models.diarize_model = None

    async def transcribe(self, config: TranscriptionConfig) -> dict[str, Any]:
        """
        Execute audio transcription

        Args:
            config: Transcription configuration object containing all necessary
                parameters

        Returns:
            Transcription result dictionary
        """
        # Extract configuration parameters with defaults
        language = "ar"  # Force Arabic language, specialized for Arabic
        vad_onset = config.vad_onset or self.settings.DEFAULT_VAD_ONSET
        vad_offset = config.vad_offset or self.settings.DEFAULT_VAD_OFFSET
        chunk_size = config.chunk_size or self.settings.DEFAULT_CHUNK_SIZE
        enable_diarization = (
            config.enable_diarization
            if config.enable_diarization is not None
            else self.settings.DEFAULT_ENABLE_DIARIZATION
        )
        min_speakers = config.min_speakers or self.settings.DEFAULT_MIN_SPEAKERS
        max_speakers = config.max_speakers or self.settings.DEFAULT_MAX_SPEAKERS

        start_time = time.time()

        try:
            # 1. Load audio
            logger.info(f"Loading audio file: {config.audio_file}")
            audio = whisperx.load_audio(config.audio_file)
            audio_duration = len(audio) / 16000  # Assume 16kHz sampling rate

            # 2. Execute transcription
            if self.models.model is None:
                error_msg = "Model not loaded, cannot execute transcription"
                raise RuntimeError(error_msg)

            logger.info("Starting transcription...")
            logger.debug(
                f"VAD config: onset={vad_onset}, "
                f"offset={vad_offset}, chunk_size={chunk_size}"
            )

            result = self.models.model.transcribe(
                audio, language=language, chunk_size=chunk_size
            )

            # 3. Timestamp alignment (Arabic specialized)
            if self.models.model:
                try:
                    self._load_align_model()
                    if self.models.align_model:
                        logger.info("Executing timestamp alignment...")
                        result = whisperx.align(
                            result["segments"],
                            self.models.align_model,
                            self.models.align_metadata,
                            audio,
                            self.compute.device,
                            return_char_alignments=False,
                        )
                except Exception as e:
                    logger.warning(f"Alignment step failed, skipping: {e}")
                    # Continue processing, do not interrupt transcription

            # 4. Speaker diarization (optional)
            if enable_diarization:
                self._load_diarization_model()
                if self.models.diarize_model:
                    logger.info("Executing speaker diarization...")
                    diarize_model = self.models.diarize_model
                    diarize_segments = diarize_model(
                        audio,
                        min_speakers=min_speakers,
                        max_speakers=max_speakers,
                    )
                    # Assign speakers to words based on diarization
                    result = whisperx.assign_word_speakers(diarize_segments, result)

            processing_time = time.time() - start_time

            logger.success(
                f"Transcription completed - Time taken: {processing_time:.2f}s"
            )

            return {
                "segments": result.get("segments", []),
                "language": result.get("language", language),
                "duration": audio_duration,
                "processing_time": processing_time,
                "word_segments": result.get("word_segments", []),
            }

        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            raise

    def get_model_info(self) -> ModelInfo:
        """Get model information"""
        return ModelInfo(
            model_path=self.settings.MODEL_PATH,
            device=self.compute.device or "unknown",
            compute_type=self.compute.compute_type or "unknown",
            model_loaded=self.models.model is not None,
            align_model_loaded=self.models.align_model is not None,
            diarization_model_loaded=self.models.diarize_model is not None,
            supported_languages=[
                "ar",  # Specialized support for Arabic transcription
            ],
        )
