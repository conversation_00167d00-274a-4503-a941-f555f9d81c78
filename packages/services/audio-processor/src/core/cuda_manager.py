"""
CUDA management utilities

Manages CUDA availability detection and optimization settings,
providing centralized CUDA configuration for the audio processing service.
"""

from typing import Optional

from loguru import logger

from .settings import Settings


class CudaManager:
    """Manages CUDA availability detection and optimization settings"""

    def __init__(self, settings: Settings):
        """
        Initialize CUDA manager

        Args:
            settings: Application settings containing CUDA configuration
        """
        self.settings = settings
        self._cuda_available: Optional[bool] = None
        self._torch_imported = False

    @property
    def cuda_available(self) -> bool:
        """Check CUDA availability (cached)"""
        if self._cuda_available is None:
            self._cuda_available = self._check_cuda_availability()
        return self._cuda_available

    def _check_cuda_availability(self) -> bool:
        """Check if CUDA is available"""
        try:
            import torch

            self._torch_imported = True
            return torch.cuda.is_available()
        except ImportError:
            logger.warning("PyTorch not available, CUDA check skipped")
            return False
        except Exception as e:
            logger.warning(f"CUDA availability check failed: {e}")
            return False

    def validate_device_config(self) -> None:
        """Validate device configuration against hardware availability"""
        if self.settings.DEVICE == "cuda":
            if not self.cuda_available:
                error_msg = (
                    "CUDA not available, but configuration requires GPU. "
                    "Please check CUDA installation or modify configuration "
                    "to CPU mode (set DEVICE=cpu in config or environment)."
                )
                raise RuntimeError(error_msg)
            logger.info(
                "CUDA validation passed - GPU mode enabled for optimal performance"
            )
        elif self.settings.DEVICE == "cpu":
            logger.info(
                "CPU mode enabled - processing will be slower but functional. "
                "For better performance, consider using GPU with CUDA support."
            )
            if self.cuda_available:
                logger.warning(
                    "CUDA is available but CPU mode is configured. "
                    "Set DEVICE=cuda for significantly better performance."
                )
        else:
            error_msg = (
                f"Unsupported device type: '{self.settings.DEVICE}'. "
                f"Supported devices: 'cuda' (recommended), 'cpu' (fallback)"
            )
            raise ValueError(error_msg)

    def configure_cuda_optimizations(self) -> None:
        """Configure CUDA optimization settings based on configuration"""
        if not self._torch_imported:
            try:
                import torch

                self._torch_imported = True
            except ImportError:
                logger.warning("PyTorch not available, CUDA optimizations skipped")
                return

        if not self.cuda_available:
            logger.warning("CUDA not available, skipping CUDA optimization settings")
            return

        try:
            import torch

            # Configure TF32 settings
            if self.settings.ENABLE_TF32:
                torch.backends.cuda.matmul.allow_tf32 = True
                torch.backends.cudnn.allow_tf32 = True
            else:
                torch.backends.cuda.matmul.allow_tf32 = False
                torch.backends.cudnn.allow_tf32 = False

            # Configure cuDNN benchmark mode
            torch.backends.cudnn.benchmark = self.settings.ENABLE_CUDNN_BENCHMARK

            # Configure cuDNN deterministic mode
            torch.backends.cudnn.deterministic = (
                self.settings.ENABLE_CUDNN_DETERMINISTIC
            )

            logger.info(
                f"CUDA optimization settings configured: "
                f"TF32={self.settings.ENABLE_TF32}, "
                f"cuDNN benchmark={self.settings.ENABLE_CUDNN_BENCHMARK}, "
                f"cuDNN deterministic={self.settings.ENABLE_CUDNN_DETERMINISTIC}"
            )

        except Exception as e:
            logger.warning(f"CUDA optimization setup failed: {e}")

    def ensure_optimizations_enabled(self) -> None:
        """Ensure CUDA optimizations remain enabled (call after model loading)"""
        if not self._torch_imported or not self.cuda_available:
            return

        try:
            import torch

            if self.settings.ENABLE_TF32:
                torch.backends.cuda.matmul.allow_tf32 = True
                torch.backends.cudnn.allow_tf32 = True

        except Exception:
            # Silent fail for this optimization reset
            pass

    def get_optimal_compute_type(self) -> str:
        """Get optimal compute type based on device configuration"""
        if self.settings.DEVICE == "cpu":
            # For CPU, int8 provides good balance of speed and accuracy
            if self.settings.COMPUTE_TYPE == "float16":
                logger.info(
                    "Adjusting compute_type from float16 to int8 for CPU compatibility"
                )
                return "int8"
            return self.settings.COMPUTE_TYPE
        # For CUDA, use configured compute_type (float16 is optimal for most GPUs)
        return self.settings.COMPUTE_TYPE
