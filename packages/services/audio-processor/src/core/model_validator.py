"""
Model validation utilities

Provides utilities for validating model directories and files,
ensuring that required model files are present and accessible.
"""

from pathlib import Path


class ModelValidator:
    """Utility class for model validation"""

    @staticmethod
    def validate_whisper_model(model_path: Path) -> tuple[bool, list[str]]:
        """
        Validate WhisperX model directory

        Args:
            model_path: Path to the model directory

        Returns:
            tuple: (is_valid, missing_files)
        """
        required_files = ["config.json", "model.bin", "tokenizer.json"]
        missing_files = [
            file for file in required_files if not (model_path / file).exists()
        ]
        return len(missing_files) == 0, missing_files

    @staticmethod
    def validate_alignment_model(model_path: Path) -> tuple[bool, list[str]]:
        """
        Validate alignment model directory

        Args:
            model_path: Path to the alignment model directory

        Returns:
            tuple: (is_valid, missing_files)
        """
        expected_files = ["config.json", "pytorch_model.bin"]
        existing_files = [
            file for file in expected_files if (model_path / file).exists()
        ]
        missing_files = [file for file in expected_files if file not in existing_files]
        return len(existing_files) > 0, missing_files

    @staticmethod
    def get_model_info(model_path: Path, model_type: str = "unknown") -> dict:
        """
        Get model information for logging/debugging

        Args:
            model_path: Path to the model directory
            model_type: Type of model for identification

        Returns:
            Dictionary containing model information
        """
        info = {
            "path": str(model_path),
            "exists": model_path.exists(),
            "is_directory": model_path.is_dir() if model_path.exists() else False,
            "type": model_type,
            "files": [],
        }

        if model_path.exists() and model_path.is_dir():
            info["files"] = [f.name for f in model_path.iterdir() if f.is_file()]

        return info
