"""
External service clients manager implementation

Provides centralized management of all external service clients,
implementing proper initialization, health checking, and cleanup.
"""

import asyncio

from core.config import Settings
from interfaces import (
    CamelToolsClient,
    ExternalServiceClients,
    QwenServiceClient,
)
from loguru import logger

from .camel_tools_client_impl import CamelToolsClientImpl
from .qwen_service_client_impl import QwenServiceClientImpl


class ExternalServiceClientsImpl(ExternalServiceClients):
    """Implementation of external service clients manager"""

    def __init__(self, settings: Settings):
        """
        Initialize external service clients

        Args:
            settings: Application settings containing API configuration
        """
        self.settings = settings
        self._camel_tools = CamelToolsClientImpl(settings)
        self._qwen_service = QwenServiceClientImpl(settings)
        self._initialized = False

    @property
    def camel_tools(self) -> CamelToolsClient:
        """Get camel-tools-api client instance"""
        return self._camel_tools

    @property
    def qwen_service(self) -> QwenServiceClient:
        """Get qwen-service client instance"""
        return self._qwen_service

    async def health_check_all(self) -> dict[str, bool]:
        """
        Check health of all external services

        Returns:
            Dictionary mapping service names to health status
        """
        results = {}

        # Run health checks concurrently
        camel_tools_task = asyncio.create_task(self._camel_tools.health_check())
        qwen_service_task = asyncio.create_task(self._qwen_service.health_check())

        results["camel_tools"] = await camel_tools_task
        results["qwen_service"] = await qwen_service_task

        return results

    async def initialize(self) -> None:
        """
        Initialize all external service clients

        Raises:
            RuntimeError: If initialization fails
        """
        if self._initialized:
            return

        try:
            logger.info("Initializing external service clients...")

            # Perform health checks to ensure services are available
            health_status = await self.health_check_all()

            for service_name, is_healthy in health_status.items():
                if not is_healthy:
                    logger.warning(f"External service {service_name} is not available")
                else:
                    logger.info(f"External service {service_name} is healthy")

            self._initialized = True
            logger.success("External service clients initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize external service clients: {e}")
            raise RuntimeError("External service client initialization failed") from e

    async def cleanup(self) -> None:
        """
        Cleanup all external service clients
        """
        if not self._initialized:
            return

        try:
            logger.info("Cleaning up external service clients...")

            # No specific cleanup needed for HTTP clients
            # They are automatically cleaned up when the async context ends

            self._initialized = False
            logger.success("External service clients cleanup completed")

        except Exception as e:
            logger.error(f"Error during external service clients cleanup: {e}")
            # Continue with cleanup even if some operations fail
