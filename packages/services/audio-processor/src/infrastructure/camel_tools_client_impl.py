"""
CamelTools client implementation

Provides HTTP client for integrating with camel-tools-api service,
implementing proper error handling, retries, and timeout management.
"""

import httpx
from core.config import Settings
from loguru import logger

from models.services.batch_text_processing_request import BatchTextProcessingRequest
from models.services.batch_text_processing_response import BatchTextProcessingResponse
from models.services.text_processing_request import TextProcessingRequest
from models.services.text_processing_response import TextProcessingResponse


class CamelToolsClientImpl:
    """Implementation of camel-tools-api service client"""

    # HTTP status codes
    HTTP_OK = 200

    def __init__(self, settings: Settings):
        """
        Initialize camel-tools client

        Args:
            settings: Application settings containing API configuration
        """
        self.settings = settings
        self.base_url = settings.CAMEL_TOOLS_API_URL
        self.timeout = settings.HTTP_CLIENT_TIMEOUT
        self.max_retries = settings.HTTP_CLIENT_MAX_RETRIES
        self.retry_delay = settings.HTTP_CLIENT_RETRY_DELAY

    async def process_text(
        self, request: TextProcessingRequest
    ) -> TextProcessingResponse:
        """
        Process single text through camel-tools-api

        Args:
            request: Text processing request with configuration

        Returns:
            TextProcessingResponse containing processed text and analysis

        Raises:
            RuntimeError: If API call fails
        """
        url = f"{self.base_url}/process"
        payload = {
            "text": request.text,
            "enable_normalization": request.enable_normalization,
            "enable_stemming": request.enable_stemming,
            "enable_root_extraction": request.enable_root_extraction,
            # Note: Enhanced normalization parameters removed - now mandatory
            # Note: Diacritics removal from stems/roots is now mandatory
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url,
                    json=payload,
                    timeout=self.timeout,
                )
                response.raise_for_status()
                result = response.json()

                return TextProcessingResponse(
                    success=result.get("success", False),
                    message=result.get("message", ""),
                    original_text=request.text,
                    normalized_text=result.get("normalized_text"),
                    words=result.get("words", []),
                )
        except Exception as e:
            logger.error(f"Error calling camel-tools-api process endpoint: {e}")
            error_msg = f"Camel-tools API call failed: {e}"
            raise RuntimeError(error_msg) from e

    async def process_batch(
        self, request: BatchTextProcessingRequest
    ) -> BatchTextProcessingResponse:
        """
        Process multiple texts through camel-tools-api in batch

        Args:
            request: Batch text processing request with configuration

        Returns:
            BatchTextProcessingResponse containing all processing results

        Raises:
            RuntimeError: If API call fails
        """
        url = f"{self.base_url}/process/batch"
        payload = {
            "texts": request.texts,
            "enable_normalization": request.enable_normalization,
            "enable_stemming": request.enable_stemming,
            "enable_root_extraction": request.enable_root_extraction,
            # Note: Enhanced normalization parameters removed - now mandatory
            # Note: Diacritics removal from stems/roots is now mandatory
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url,
                    json=payload,
                    timeout=self.settings.BATCH_PROCESSING_TIMEOUT,
                )
                response.raise_for_status()
                result = response.json()

                return BatchTextProcessingResponse(
                    success=result.get("success", False),
                    message=result.get("message", ""),
                    results=result.get("results", []),
                )
        except Exception as e:
            logger.error(f"Error calling camel-tools-api batch process endpoint: {e}")
            error_msg = f"Camel-tools batch API call failed: {e}"
            raise RuntimeError(error_msg) from e

    async def health_check(self) -> bool:
        """
        Check if camel-tools-api service is available

        Returns:
            True if service is healthy, False otherwise
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/health",
                    timeout=5.0,  # Short timeout for health checks
                )
                return response.status_code == self.HTTP_OK
        except Exception as e:
            logger.warning(f"Camel-tools-api health check failed: {e}")
            return False
