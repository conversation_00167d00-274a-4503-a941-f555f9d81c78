"""
Translation service implementation

Implements translation functionality using qwen-service through
the backend service's optimized integration with context management.
"""

import asyncio
import time
from typing import List

from core.config import Settings
from interfaces import QwenServiceClient, TranslationService
from loguru import logger

from models.domain.transcription_segment import TranscriptionSegment
from models.services.semantic_validation_request import SemanticValidationRequest
from models.services.translation_request import TranslationRequest
from models.services.translation_response import TranslationResponse


class TranslationServiceImpl(TranslationService):
    """
    Implementation of translation service using qwen-service
    
    Provides translation functionality with context management
    following the same patterns as chapter marker validation.
    """

    def __init__(
        self,
        settings: Settings,
        qwen_client: QwenServiceClient,
    ):
        """
        Initialize translation service
        
        Args:
            settings: Application settings
            qwen_client: Qwen service client for API calls
        """
        self.settings = settings
        self.qwen_client = qwen_client
        self.max_concurrent_requests = 3  # Limit concurrent requests to avoid overload

    async def translate_segments(
        self, request: TranslationRequest
    ) -> TranslationResponse:
        """
        Translate transcribed segments with contextual information
        
        Args:
            request: Translation request containing segments and configuration
            
        Returns:
            TranslationResponse with translated segments and metadata
        """
        start_time = time.time()
        
        try:
            logger.info(
                f"Starting translation for {len(request.segments)} segments "
                f"to {request.target_language}"
            )
            
            # Create translated segments list
            translated_segments = []
            successful_count = 0
            failed_count = 0
            
            # Process segments in batches to avoid overwhelming the service
            semaphore = asyncio.Semaphore(self.max_concurrent_requests)
            
            async def translate_single_segment(segment_idx: int) -> None:
                """Translate a single segment with context"""
                nonlocal successful_count, failed_count
                
                async with semaphore:
                    try:
                        # Build context for this segment
                        context_text = self._build_context_text(
                            request.segments, segment_idx, request.context_segments
                        )
                        
                        # Create translation prompt
                        prompt = self._create_translation_prompt(
                            context_text=context_text,
                            target_segment_text=request.segments[segment_idx].text,
                            target_language=request.target_language,
                            source_language=request.source_language,
                        )
                        
                        # Create validation request for qwen service
                        validation_request = SemanticValidationRequest(
                            prompt=prompt,
                            system_prompt=self._get_translation_system_prompt(),
                            enable_thinking=True,
                        )
                        
                        # Call qwen service
                        validation_response = await self.qwen_client.validate_semantically(
                            validation_request
                        )
                        
                        if validation_response.success:
                            # Extract translated text from response
                            translated_text = self._extract_translation_from_response(
                                validation_response.generated_text
                            )
                            
                            # Create segment with translation
                            segment_copy = request.segments[segment_idx].model_copy()
                            segment_copy.translated_text = translated_text
                            translated_segments.append((segment_idx, segment_copy))
                            successful_count += 1
                            
                            logger.debug(
                                f"Translation successful for segment {segment_idx}: "
                                f"'{request.segments[segment_idx].text[:50]}...' -> "
                                f"'{translated_text[:50]}...'"
                            )
                        else:
                            # Keep original segment without translation
                            segment_copy = request.segments[segment_idx].model_copy()
                            translated_segments.append((segment_idx, segment_copy))
                            failed_count += 1
                            
                            logger.warning(
                                f"Translation failed for segment {segment_idx}: "
                                f"{validation_response.message}"
                            )
                    
                    except Exception as e:
                        # Keep original segment without translation
                        segment_copy = request.segments[segment_idx].model_copy()
                        translated_segments.append((segment_idx, segment_copy))
                        failed_count += 1
                        
                        logger.error(
                            f"Translation error for segment {segment_idx}: {e}"
                        )
            
            # Process all segments concurrently
            tasks = [
                translate_single_segment(i) 
                for i in range(len(request.segments))
            ]
            
            await asyncio.gather(*tasks)
            
            # Sort segments by original index to maintain order
            translated_segments.sort(key=lambda x: x[0])
            final_segments = [segment for _, segment in translated_segments]
            
            processing_time = time.time() - start_time
            
            logger.success(
                f"Translation completed: {successful_count} successful, "
                f"{failed_count} failed, {processing_time:.2f}s"
            )
            
            return TranslationResponse(
                success=True,
                message=f"Translation completed: {successful_count} successful, {failed_count} failed",
                segments=final_segments,
                processing_time=processing_time,
                translated_count=successful_count,
                failed_count=failed_count,
                target_language=request.target_language,
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Translation service error: {e}"
            logger.error(error_msg)
            
            return TranslationResponse(
                success=False,
                message=error_msg,
                segments=request.segments,  # Return original segments
                processing_time=processing_time,
                translated_count=0,
                failed_count=len(request.segments),
                target_language=request.target_language,
            )

    def _build_context_text(
        self, segments: List[TranscriptionSegment], target_idx: int, context_size: int
    ) -> str:
        """
        Build context text around target segment
        
        Args:
            segments: List of all segments
            target_idx: Index of the target segment
            context_size: Number of context segments before and after
            
        Returns:
            Context text with target segment highlighted
        """
        start_idx = max(0, target_idx - context_size)
        end_idx = min(len(segments), target_idx + context_size + 1)
        
        # Build context text
        context_parts = []
        for i in range(start_idx, end_idx):
            if i == target_idx:
                # Mark the target segment
                context_parts.append(f"[TARGET: {segments[i].text}]")
            else:
                context_parts.append(segments[i].text)
        
        return " ".join(context_parts)

    def _create_translation_prompt(
        self, context_text: str, target_segment_text: str, target_language: str, source_language: str
    ) -> str:
        """
        Create translation prompt with context
        
        Args:
            context_text: Context text with target segment marked
            target_segment_text: Text of the target segment to translate
            target_language: Target language for translation
            source_language: Source language of the text
            
        Returns:
            Translation prompt
        """
        return f"""Please translate the following {source_language} text segment to {target_language}.

Context (the text marked with [TARGET: ...] is what needs to be translated):
{context_text}

Target segment to translate:
{target_segment_text}

Please provide a natural and accurate translation that fits the context. Only return the translated text, without any additional explanation or formatting."""

    def _get_translation_system_prompt(self) -> str:
        """
        Get system prompt for translation
        
        Returns:
            System prompt for translation tasks
        """
        return """You are an expert translator specializing in Arabic to Chinese translation. 
You understand the nuances of both languages and can provide accurate, contextual translations.
Your task is to translate the given text segment while considering the surrounding context.
Always provide natural, fluent translations that maintain the meaning and tone of the original text.
Only return the translated text without any additional explanation."""

    def _extract_translation_from_response(self, response_text: str) -> str:
        """
        Extract the actual translation from the model response
        
        Args:
            response_text: Raw response from the model
            
        Returns:
            Extracted translation text
        """
        # Clean up the response text
        translation = response_text.strip()
        
        # Remove any common prefixes or suffixes that might be added by the model
        prefixes_to_remove = [
            "翻译：", "Translation:", "译文：", "中文：", "Chinese:", 
            "翻译结果：", "Translation result:", "答案：", "Answer:"
        ]
        
        for prefix in prefixes_to_remove:
            if translation.startswith(prefix):
                translation = translation[len(prefix):].strip()
                break
        
        # Remove quotes if the entire text is quoted
        if translation.startswith('"') and translation.endswith('"'):
            translation = translation[1:-1]
        elif translation.startswith("'") and translation.endswith("'"):
            translation = translation[1:-1]
        
        return translation

    async def health_check(self) -> bool:
        """
        Check if translation service is available
        
        Returns:
            True if service is healthy, False otherwise
        """
        try:
            return await self.qwen_client.health_check()
        except Exception as e:
            logger.error(f"Translation service health check failed: {e}")
            return False