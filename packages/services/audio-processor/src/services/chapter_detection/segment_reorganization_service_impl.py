"""
Segment reorganization service implementation

Handles reorganization of transcription segments to create consolidated
segments for chapter markers, supporting both single and cross-segment chapters.
"""

from typing import Any

from core.config import Settings
from interfaces import WordLevelTimestampService
from loguru import logger

from models.domain.transcription_segment import TranscriptionSegment


class SegmentReorganizationServiceImpl:
    """
    Implementation of segment reorganization service

    Provides segment reorganization capabilities to create consolidated
    chapter marker segments while preserving word-level timing data.
    """

    def __init__(
        self,
        settings: Settings,
        word_level_timestamp_service: WordLevelTimestampService,
    ):
        """
        Initialize the segment reorganization service

        Args:
            settings: Application settings containing configuration
            word_level_timestamp_service: Service for word-level timestamp operations
        """
        self.settings = settings
        self.word_level_timestamp_service = word_level_timestamp_service

    def reorganize_segments_for_chapter(
        self,
        segments: list[TranscriptionSegment],
        chapter_info: dict[str, Any],
        marker_text: str,
    ) -> list[TranscriptionSegment]:
        """
        Reorganize segments to create a consolidated segment for the chapter marker

        Handles both single-segment and multi-segment chapter marker extraction,
        creating proper before/chapter/after segment structure.

        Args:
            segments: Original list of transcription segments
            chapter_info: Chapter information including match details
            marker_text: The chapter marker text

        Returns:
            Reorganized list of segments with chapter marker in its own segment
        """
        matched_indices = chapter_info.get("matched_segment_indices", [])

        # Handle all single-segment cases (start, middle, etc.)
        if len(matched_indices) == 1:
            return self._reorganize_single_segment(segments, chapter_info, marker_text)

        # Handle multi-segment case (existing functionality)
        if len(matched_indices) <= 1:
            # No segments matched, no reorganization needed
            return segments

        try:
            start_idx = min(matched_indices)
            end_idx = max(matched_indices)

            # Check if this is a cross-segment chapter with specific info
            cross_segment_info = chapter_info.get("cross_segment_info")
            if cross_segment_info:
                # Use precise boundaries for cross-segment chapters
                overlap_words = cross_segment_info["overlap_words"]
                boundary_result = (
                    self.word_level_timestamp_service.find_cross_segment_boundaries(
                        segments, start_idx, end_idx, marker_text, overlap_words
                    )
                )
                start_time = boundary_result.start_time
                end_time = boundary_result.end_time
                chapter_words = boundary_result.chapter_words
            else:
                # Use original method for other multi-segment chapters
                boundary_result = (
                    self.word_level_timestamp_service.find_precise_word_boundaries(
                        segments, start_idx, end_idx, marker_text
                    )
                )
                start_time = boundary_result.start_time
                end_time = boundary_result.end_time
                chapter_words = boundary_result.chapter_words

            # Create new segment for the chapter marker
            chapter_segment = TranscriptionSegment(
                start=start_time,
                end=end_time,
                text=marker_text,
                speaker=segments[start_idx].speaker,
                words=chapter_words,
            )

            # Create reorganized segment list with proper chapter extraction
            reorganized = []

            # Add segments before the chapter
            reorganized.extend(segments[:start_idx])

            # Add any remaining content from the first segment before the chapter
            first_segment_remainder = self._get_cross_segment_remainder_before(
                segments[start_idx], chapter_info, start_time
            )
            if first_segment_remainder:
                reorganized.append(first_segment_remainder)

            # Add the chapter segment (extracted independently)
            reorganized.append(chapter_segment)

            # Add any remaining content from the last segment after the chapter
            last_segment_remainder = self._get_cross_segment_remainder_after(
                segments[end_idx], chapter_info, end_time
            )
            if last_segment_remainder:
                reorganized.append(last_segment_remainder)

            # Add segments after the chapter
            reorganized.extend(segments[end_idx + 1 :])

            logger.info(
                f"Successfully reorganized cross-segment chapter '{marker_text}' "
                f"spanning segments {start_idx}-{end_idx}"
            )

            return reorganized

        except Exception as e:
            logger.error(f"Error reorganizing segments for chapter: {e}")
            return segments  # Return original segments if reorganization fails

    def get_segment_remainder_before_chapter(
        self, segment: TranscriptionSegment, chapter_start_time: float
    ) -> TranscriptionSegment | None:
        """
        Get the portion of a segment that comes before a chapter marker

        Args:
            segment: The original segment
            chapter_start_time: Start time of the chapter marker

        Returns:
            New segment with content before the chapter, or None if no content
        """
        if segment.start >= chapter_start_time:
            return None

        try:
            # Find words that come before the chapter
            before_words = []
            before_text_parts = []

            if segment.words:
                for word in segment.words:
                    if word.end <= chapter_start_time:
                        before_words.append(word)
                        before_text_parts.append(word.word)

                if not before_words:
                    return None

                before_text = " ".join(before_text_parts)
                return TranscriptionSegment(
                    start=segment.start,
                    end=before_words[-1].end,
                    text=before_text,
                    speaker=segment.speaker,
                    words=before_words,
                )

            # No word-level data, estimate based on time
            if chapter_start_time > segment.start:
                return TranscriptionSegment(
                    start=segment.start,
                    end=chapter_start_time,
                    text=segment.text,  # Keep original text as approximation
                    speaker=segment.speaker,
                    words=None,
                )

        except Exception as e:
            logger.warning(f"Error creating segment remainder before chapter: {e}")

        return None

    def get_segment_remainder_after_chapter(
        self, segment: TranscriptionSegment, chapter_end_time: float
    ) -> TranscriptionSegment | None:
        """
        Get the portion of a segment that comes after a chapter marker

        Args:
            segment: The original segment
            chapter_end_time: End time of the chapter marker

        Returns:
            New segment with content after the chapter, or None if no content
        """
        if segment.end <= chapter_end_time:
            return None

        try:
            # Find words that come after the chapter
            after_words = []
            after_text_parts = []

            if segment.words:
                for word in segment.words:
                    if word.start >= chapter_end_time:
                        after_words.append(word)
                        after_text_parts.append(word.word)

                if not after_words:
                    return None

                after_text = " ".join(after_text_parts)
                return TranscriptionSegment(
                    start=after_words[0].start,
                    end=segment.end,
                    text=after_text,
                    speaker=segment.speaker,
                    words=after_words,
                )

            # No word-level data, estimate based on time
            if chapter_end_time < segment.end:
                return TranscriptionSegment(
                    start=chapter_end_time,
                    end=segment.end,
                    text=segment.text,  # Keep original text as approximation
                    speaker=segment.speaker,
                    words=None,
                )

        except Exception as e:
            logger.warning(f"Error creating segment remainder after chapter: {e}")

        return None

    def _reorganize_single_segment(
        self,
        segments: list[TranscriptionSegment],
        chapter_info: dict[str, Any],
        marker_text: str,
    ) -> list[TranscriptionSegment]:
        """
        Reorganize a single segment where chapter marker is found

        Extracts the chapter marker from within a segment and creates segments:
        1. Before segment (content before the chapter marker, if any)
        2. Chapter segment (only the chapter marker)
        3. After segment (content after the chapter marker, if any)

        Handles all single-segment cases: start, middle, and other positions.

        Args:
            segments: Original list of transcription segments
            chapter_info: Chapter information with word-level boundaries
            marker_text: The chapter marker text

        Returns:
            Reorganized list of segments with extracted chapter marker
        """
        try:
            segment_idx = chapter_info.get("segment_index", 0)
            start_word_idx = chapter_info.get("start_word_idx")
            end_word_idx = chapter_info.get("end_word_idx")

            if (
                segment_idx >= len(segments)
                or start_word_idx is None
                or end_word_idx is None
            ):
                logger.warning(
                    "Missing word-level boundary information for single-segment "
                    "reorganization"
                )
                return segments

            original_segment = segments[segment_idx]

            # Calculate precise timestamps using word-level data
            chapter_start_time, chapter_end_time, chapter_words = (
                self._extract_chapter_boundaries_from_words(
                    original_segment, start_word_idx, end_word_idx, marker_text
                )
            )

            # Create the three segments: before, chapter, after
            reorganized = []

            # Add segments before the target segment
            reorganized.extend(segments[:segment_idx])

            # Create before-chapter segment if there's content before the marker
            before_segment = self._create_before_segment(
                original_segment, chapter_start_time, start_word_idx
            )
            if before_segment:
                reorganized.append(before_segment)

            # Create chapter segment
            chapter_segment = TranscriptionSegment(
                start=chapter_start_time,
                end=chapter_end_time,
                text=marker_text,
                speaker=original_segment.speaker,
                words=chapter_words,
            )
            reorganized.append(chapter_segment)

            # Create after-chapter segment if there's content after the marker
            after_segment = self._create_after_segment(
                original_segment, chapter_end_time, end_word_idx
            )
            if after_segment:
                reorganized.append(after_segment)

            # Add segments after the target segment
            reorganized.extend(segments[segment_idx + 1 :])

            segment_count = len(reorganized) - len(segments) + 1
            logger.info(
                f"Successfully reorganized single segment for chapter '{marker_text}' "
                f"at segment {segment_idx}, created {segment_count} segments"
            )
            return reorganized

        except Exception as e:
            logger.error(f"Error in single-segment reorganization: {e}")
            return segments

    def _extract_chapter_boundaries_from_words(
        self,
        segment: TranscriptionSegment,
        start_word_idx: int,
        end_word_idx: int,
        marker_text: str,  # noqa: ARG002
    ) -> tuple[float, float, list[Any]]:
        """
        Extract precise chapter boundaries using word-level timestamps

        Args:
            segment: The segment containing the chapter marker
            start_word_idx: Index of first word in chapter marker
            end_word_idx: Index of last word in chapter marker
            marker_text: The chapter marker text

        Returns:
            Tuple of (start_time, end_time, chapter_words)
        """
        if not segment.words or len(segment.words) <= end_word_idx:
            # Fallback to segment-level timestamps if word data unavailable
            logger.warning("Word-level data unavailable, using segment timestamps")
            return segment.start, segment.end, []

        try:
            # Extract chapter words and calculate boundaries
            chapter_words = segment.words[start_word_idx : end_word_idx + 1]
            start_time = chapter_words[0].start
            end_time = chapter_words[-1].end

            return start_time, end_time, chapter_words

        except (IndexError, AttributeError) as e:
            logger.warning(f"Error extracting word boundaries: {e}")
            # Fallback to segment timestamps
            return segment.start, segment.end, []

    def _create_before_segment(
        self,
        original_segment: TranscriptionSegment,
        chapter_start_time: float,
        start_word_idx: int,
    ) -> TranscriptionSegment | None:
        """
        Create segment containing content before the chapter marker

        Args:
            original_segment: Original segment containing the chapter
            chapter_start_time: Start time of the chapter marker
            start_word_idx: Index where chapter marker starts

        Returns:
            Segment with content before chapter, or None if no content
        """
        if start_word_idx == 0:
            return None  # No content before the chapter

        try:
            if original_segment.words and len(original_segment.words) > start_word_idx:
                # Use word-level data for precise extraction
                before_words = original_segment.words[:start_word_idx]
                before_text = " ".join(word.word for word in before_words)

                return TranscriptionSegment(
                    start=original_segment.start,
                    end=before_words[-1].end,
                    text=before_text,
                    speaker=original_segment.speaker,
                    words=before_words,
                )
            # Fallback to time-based estimation
            if chapter_start_time > original_segment.start:
                # Estimate text content (simplified approach)
                words = original_segment.text.strip().split()
                if start_word_idx < len(words):
                    before_text = " ".join(words[:start_word_idx])
                    return TranscriptionSegment(
                        start=original_segment.start,
                        end=chapter_start_time,
                        text=before_text,
                        speaker=original_segment.speaker,
                        words=None,
                    )

        except Exception as e:
            logger.warning(f"Error creating before segment: {e}")

        return None

    def _create_after_segment(
        self,
        original_segment: TranscriptionSegment,
        chapter_end_time: float,
        end_word_idx: int,
    ) -> TranscriptionSegment | None:
        """
        Create segment containing content after the chapter marker

        Args:
            original_segment: Original segment containing the chapter
            chapter_end_time: End time of the chapter marker
            end_word_idx: Index where chapter marker ends

        Returns:
            Segment with content after chapter, or None if no content
        """
        try:
            if (
                original_segment.words
                and len(original_segment.words) > end_word_idx + 1
            ):
                # Use word-level data for precise extraction
                after_words = original_segment.words[end_word_idx + 1 :]
                after_text = " ".join(word.word for word in after_words)

                return TranscriptionSegment(
                    start=after_words[0].start,
                    end=original_segment.end,
                    text=after_text,
                    speaker=original_segment.speaker,
                    words=after_words,
                )
            # Fallback to time-based estimation
            if chapter_end_time < original_segment.end:
                # Estimate text content (simplified approach)
                words = original_segment.text.strip().split()
                if end_word_idx + 1 < len(words):
                    after_text = " ".join(words[end_word_idx + 1 :])
                    return TranscriptionSegment(
                        start=chapter_end_time,
                        end=original_segment.end,
                        text=after_text,
                        speaker=original_segment.speaker,
                        words=None,
                    )

        except Exception as e:
            logger.warning(f"Error creating after segment: {e}")

        return None

    def _get_cross_segment_remainder_before(
        self,
        segment: TranscriptionSegment,
        chapter_info: dict[str, Any],
        chapter_start_time: float,
    ) -> TranscriptionSegment | None:
        """
        Get the portion of a segment that comes before a cross-segment chapter marker

        This method handles the case where a chapter marker spans multiple segments,
        and we need to extract the content before the chapter marker starts.

        Args:
            segment: The segment containing the start of the chapter marker
            chapter_info: Chapter information with word-level boundaries
            chapter_start_time: Start time of the chapter marker

        Returns:
            New segment with content before the chapter, or None if no content
        """
        start_word_idx = chapter_info.get("start_word_idx", 0)

        # If chapter starts at word 0, no content before
        if start_word_idx == 0:
            return None

        try:
            if segment.words and len(segment.words) > start_word_idx:
                # Use word-level data for precise extraction
                before_words = segment.words[:start_word_idx]
                before_text = " ".join(word.word for word in before_words)

                return TranscriptionSegment(
                    start=segment.start,
                    end=before_words[-1].end,
                    text=before_text,
                    speaker=segment.speaker,
                    words=before_words,
                )
            # Fallback to time-based approach
            return self.get_segment_remainder_before_chapter(
                segment, chapter_start_time
            )

        except Exception as e:
            logger.warning(f"Error creating cross-segment before remainder: {e}")
            # Fallback to original method
            return self.get_segment_remainder_before_chapter(
                segment, chapter_start_time
            )

    def _get_cross_segment_remainder_after(
        self,
        segment: TranscriptionSegment,
        chapter_info: dict[str, Any],
        chapter_end_time: float,
    ) -> TranscriptionSegment | None:
        """
        Get the portion of a segment that comes after a cross-segment chapter marker

        This method handles the case where a chapter marker spans multiple segments,
        and we need to extract the content after the chapter marker ends.

        Args:
            segment: The segment containing the end of the chapter marker
            chapter_info: Chapter information with word-level boundaries
            chapter_end_time: End time of the chapter marker

        Returns:
            New segment with content after the chapter, or None if no content
        """
        end_word_idx = chapter_info.get("end_word_idx")

        if end_word_idx is None:
            # Fallback to time-based approach
            return self.get_segment_remainder_after_chapter(segment, chapter_end_time)

        try:
            if segment.words and len(segment.words) > end_word_idx + 1:
                # Use word-level data for precise extraction
                after_words = segment.words[end_word_idx + 1 :]
                after_text = " ".join(word.word for word in after_words)

                return TranscriptionSegment(
                    start=after_words[0].start,
                    end=segment.end,
                    text=after_text,
                    speaker=segment.speaker,
                    words=after_words,
                )
            # Fallback to time-based approach
            return self.get_segment_remainder_after_chapter(segment, chapter_end_time)

        except Exception as e:
            logger.warning(f"Error creating cross-segment after remainder: {e}")
            # Fallback to original method
            return self.get_segment_remainder_after_chapter(segment, chapter_end_time)
