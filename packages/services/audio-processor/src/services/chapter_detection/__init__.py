"""
Chapter detection service module

This module contains all chapter detection related services including:
- Chapter detection orchestrator for coordinating the detection workflow
- Cross-segment matching for complex pattern detection
- Text preprocessing using camel-tools-api
- Word-level timestamp calculations
- Segment reorganization for chapter markers
"""

from .chapter_detection_orchestrator_impl import ChapterDetectionOrchestratorImpl
from .cross_segment_matching_service_impl import CrossSegmentMatchingServiceImpl
from .segment_reorganization_service_impl import SegmentReorganizationServiceImpl
from .text_preprocessing_service_impl import TextPreprocessingServiceImpl
from .word_level_timestamp_service_impl import WordLevelTimestampServiceImpl


__all__ = [
    "ChapterDetectionOrchestratorImpl",
    "CrossSegmentMatchingServiceImpl",
    "SegmentReorganizationServiceImpl",
    "TextPreprocessingServiceImpl",
    "WordLevelTimestampServiceImpl",
]
