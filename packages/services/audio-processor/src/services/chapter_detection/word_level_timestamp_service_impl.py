"""
Word-level timestamp service implementation

Handles precise word-level timestamp calculations for chapter markers,
supporting both regular and cross-segment chapter boundaries.
"""

from core.config import Settings
from loguru import logger

from models.domain.transcription_segment import TranscriptionSegment
from models.domain.word_segment import WordSegment
from models.services.word_boundary_result import WordBoundaryResult


class WordLevelTimestampServiceImpl:
    """
    Implementation of word-level timestamp service

    Provides precise timestamp calculations using word-level data from WhisperX,
    supporting both single-segment and cross-segment chapter markers.
    """

    def __init__(self, settings: Settings):
        """
        Initialize the word-level timestamp service

        Args:
            settings: Application settings containing configuration
        """
        self.settings = settings

    def find_precise_word_boundaries(
        self,
        segments: list[TranscriptionSegment],
        start_segment_idx: int,
        end_segment_idx: int,
        marker_text: str,
        start_word_offset: int = 0,
    ) -> WordBoundaryResult:
        """
        Find precise start and end timestamps for a marker using word-level data

        Args:
            segments: List of transcription segments
            start_segment_idx: Index of the first segment containing the marker
            end_segment_idx: Index of the last segment containing the marker
            marker_text: The chapter marker text
            start_word_offset: Number of words to skip
            from the beginning of start segment

        Returns:
            WordBoundaryResult with precise timestamps and word data
        """
        start_time = segments[start_segment_idx].start
        end_time = segments[end_segment_idx].end
        chapter_words: list[WordSegment] = []

        # Try to use word-level timestamps for more precision
        try:
            # Find start time using word-level data
            start_segment = segments[start_segment_idx]
            if start_segment.words and len(start_segment.words) > start_word_offset:
                start_time = start_segment.words[start_word_offset].start

            # Find end time by counting words in the marker
            marker_words = marker_text.strip().split()
            words_found = 0
            target_word_count = len(marker_words)

            for seg_idx in range(start_segment_idx, end_segment_idx + 1):
                segment = segments[seg_idx]
                if not segment.words:
                    continue

                start_word_idx = (
                    start_word_offset if seg_idx == start_segment_idx else 0
                )
                for word_idx in range(start_word_idx, len(segment.words)):
                    word = segment.words[word_idx]
                    chapter_words.append(word)
                    words_found += 1

                    if words_found >= target_word_count:
                        end_time = word.end
                        return WordBoundaryResult(
                            start_time=start_time,
                            end_time=end_time,
                            chapter_words=chapter_words,
                        )

        except Exception as e:
            logger.warning(f"Could not use word-level timestamps: {e}")

        return WordBoundaryResult(
            start_time=start_time,
            end_time=end_time,
            chapter_words=chapter_words,
        )

    def find_cross_segment_boundaries(
        self,
        segments: list[TranscriptionSegment],
        start_segment_idx: int,
        end_segment_idx: int,
        marker_text: str,
        overlap_words: int,
    ) -> WordBoundaryResult:
        """
        Find precise word-level boundaries for a cross-segment chapter marker

        Args:
            segments: List of transcription segments
            start_segment_idx: Index of the segment where marker starts
            end_segment_idx: Index of the segment where marker ends
            marker_text: The complete chapter marker text
            overlap_words: Number of overlapping words at the end of start segment

        Returns:
            WordBoundaryResult with precise timestamps and word data
        """
        start_time = segments[start_segment_idx].start
        end_time = segments[end_segment_idx].end
        chapter_words: list[WordSegment] = []

        try:
            # Find start time using word-level timestamps from the start segment
            start_segment = segments[start_segment_idx]
            if start_segment.words and len(start_segment.words) >= overlap_words:
                # Find the word where the marker starts (last overlap_words words)
                marker_start_word_idx = len(start_segment.words) - overlap_words
                start_time = start_segment.words[marker_start_word_idx].start

            # Extract chapter words with precise boundaries
            chapter_words = self._extract_cross_segment_chapter_words(
                segments,
                start_segment_idx,
                end_segment_idx,
                marker_text,
                start_time,
                end_time,
                overlap_words,
            )

            # Find end time by counting words in the marker
            marker_words = marker_text.strip().split()
            words_found = 0
            target_word_count = len(marker_words)

            for seg_idx in range(start_segment_idx, end_segment_idx + 1):
                segment = segments[seg_idx]
                if not segment.words:
                    continue

                if seg_idx == start_segment_idx:
                    # Start from the marker start position in first segment
                    start_word_idx = len(segment.words) - overlap_words
                else:
                    start_word_idx = 0

                for word_idx in range(start_word_idx, len(segment.words)):
                    words_found += 1
                    if words_found >= target_word_count:
                        end_time = segment.words[word_idx].end
                        return WordBoundaryResult(
                            start_time=start_time,
                            end_time=end_time,
                            chapter_words=chapter_words,
                        )

        except Exception as e:
            logger.warning(
                f"Could not use word-level timestamps for cross-segment marker: {e}"
            )

        return WordBoundaryResult(
            start_time=start_time,
            end_time=end_time,
            chapter_words=chapter_words,
        )

    def _extract_cross_segment_chapter_words(
        self,
        segments: list[TranscriptionSegment],
        start_idx: int,
        end_idx: int,
        marker_text: str,
        start_time: float,
        end_time: float,
        overlap_words: int,
    ) -> list[WordSegment]:
        """
        Extract word-level data for a cross-segment chapter marker

        Args:
            segments: List of transcription segments
            start_idx: Index of first segment
            end_idx: Index of last segment
            marker_text: Chapter marker text
            start_time: Start time of the chapter marker
            end_time: End time of the chapter marker
            overlap_words: Number of overlapping words at the end of start segment

        Returns:
            List of WordSegment objects for the chapter marker
        """
        chapter_words = []
        marker_word_list = marker_text.strip().split()

        try:
            words_found = 0
            target_word_count = len(marker_word_list)

            for seg_idx in range(start_idx, end_idx + 1):
                segment = segments[seg_idx]
                if not segment.words:
                    continue

                if seg_idx == start_idx:
                    # Start from the marker start position in first segment
                    start_word_idx = len(segment.words) - overlap_words
                else:
                    start_word_idx = 0

                for word_idx in range(start_word_idx, len(segment.words)):
                    word = segment.words[word_idx]
                    if word.start >= start_time and word.end <= end_time:
                        chapter_words.append(word)
                        words_found += 1

                    if words_found >= target_word_count:
                        break

                if words_found >= target_word_count:
                    break

        except Exception as e:
            logger.warning(f"Error extracting cross-segment chapter words: {e}")

        return chapter_words

    def extract_chapter_words(
        self,
        segments: list[TranscriptionSegment],
        start_idx: int,
        end_idx: int,
        marker_text: str,
        start_time: float,
        end_time: float,
    ) -> list[WordSegment]:
        """
        Extract word-level data for a chapter marker from multiple segments

        Args:
            segments: List of transcription segments
            start_idx: Index of first segment
            end_idx: Index of last segment
            marker_text: Chapter marker text
            start_time: Start time of the chapter marker
            end_time: End time of the chapter marker

        Returns:
            List of WordSegment objects for the chapter marker
        """
        chapter_words = []
        marker_word_list = marker_text.strip().split()
        word_index = 0

        try:
            for seg_idx in range(start_idx, end_idx + 1):
                segment = segments[seg_idx]
                if not segment.words:
                    continue

                for word in segment.words:
                    if (
                        word_index < len(marker_word_list)
                        and word.start >= start_time
                        and word.end <= end_time
                    ):
                        chapter_words.append(word)
                        word_index += 1

                    if word_index >= len(marker_word_list):
                        break

                if word_index >= len(marker_word_list):
                    break

        except Exception as e:
            logger.warning(f"Error extracting chapter words: {e}")

        return chapter_words
