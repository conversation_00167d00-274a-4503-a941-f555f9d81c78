"""
Text preprocessing service implementation

Handles all camel-tools-api integration for Arabic text processing,
including batch processing, stem extraction, and caching management.
"""

from datetime import datetime, timezone
from typing import Any

from core.config import Settings
from interfaces import ExternalServiceClients

from models.api.process_report_entry import ProcessReportEntry
from models.domain.transcription_segment import TranscriptionSegment
from models.services.batch_text_processing_request import BatchTextProcessingRequest


class TextPreprocessingServiceImpl:
    """
    Implementation of text preprocessing service

    Provides Arabic text preprocessing using camel-tools-api with batch processing,
    caching, and fallback mechanisms for robust operation.
    """

    def __init__(self, settings: Settings, external_clients: ExternalServiceClients):
        """
        Initialize the text preprocessing service

        Args:
            settings: Application settings containing configuration
            external_clients: External service clients for API integration
        """
        self.settings = settings
        self.external_clients = external_clients

    async def batch_extract_stems(
        self, texts: list[str], report: list[ProcessReportEntry]
    ) -> dict[str, list[str]]:
        """
        Batch extract word stems from multiple texts using camel-tools-api
        Handles large text lists by chunking them into smaller batches

        Args:
            texts: List of texts to process
            report: Report list to append logs to

        Returns:
            Dictionary mapping text to list of word stems
        """
        if not texts:
            return {}

        # Use configured batch size limit
        batch_size = self.settings.CHAPTER_BATCH_SIZE

        try:
            report.append(
                self._create_report_entry(
                    "INFO", f"Batch processing {len(texts)} texts for stem extraction."
                )
            )

            text_to_stems: dict[str, list[str]] = {}
            total_successful = 0
            total_failed = 0

            # Process texts in chunks
            for chunk_start in range(0, len(texts), batch_size):
                chunk_end = min(chunk_start + batch_size, len(texts))
                chunk_texts = texts[chunk_start:chunk_end]
                chunk_number = chunk_start // batch_size + 1

                report.append(
                    self._create_report_entry(
                        "INFO",
                        f"Processing chunk {chunk_number}: "
                        f"texts {chunk_start + 1}-{chunk_end} of {len(texts)}",
                    )
                )

                (
                    chunk_results,
                    chunk_successful,
                    chunk_failed,
                ) = await self._process_chunk_texts(chunk_texts, chunk_number, report)

                text_to_stems.update(chunk_results)
                total_successful += chunk_successful
                total_failed += chunk_failed

            report.append(
                self._create_report_entry(
                    "SUCCESS",
                    f"Batch processing completed: "
                    f"{total_successful}/{len(texts)} texts processed successfully.",
                )
            )

            return text_to_stems

        except Exception as e:
            report.append(
                self._create_report_entry("ERROR", f"Batch stem extraction failed: {e}")
            )
            return {}

    async def extract_stems(
        self, text: str, report: list[ProcessReportEntry]
    ) -> list[str]:
        """
        Extract word stems from a single text using camel-tools-api

        Args:
            text: Text to process
            report: Report list to append logs to

        Returns:
            List of word stems
        """
        try:
            # Create single text processing request
            request = BatchTextProcessingRequest(
                texts=[text],
                enable_normalization=True,
                enable_stemming=True,
                enable_root_extraction=False,
                # Note: Enhanced normalization parameters now mandatory
                # Note: Diacritics removal from stems/roots is now mandatory
            )

            response = await self.external_clients.camel_tools.process_batch(request)

            if response.success and response.results and len(response.results) > 0:
                result = response.results[0]
                # Handle result as dictionary (from API response)
                result_success = (
                    result.get("success", False)
                    if isinstance(result, dict)
                    else result.success
                )
                result_words = (
                    result.get("words", [])
                    if isinstance(result, dict)
                    else result.words
                )

                if result_success and result_words:
                    word_stems: list[str] = []
                    for word in result_words:
                        stem = word.get("stem", word.get("word", ""))
                        if isinstance(stem, str) and stem.strip():
                            word_stems.append(stem)
                    return word_stems

            report.append(
                self._create_report_entry(
                    "ERROR", f"Failed to extract stems for text '{text[:30]}...'"
                )
            )
            return []

        except Exception as e:
            report.append(
                self._create_report_entry(
                    "ERROR", f"Failed to extract stems for text '{text[:30]}...': {e}"
                )
            )
            return []

    async def collect_and_process_texts(
        self,
        segments: list[TranscriptionSegment],
        chapter_markers: list[str],
        report: list[ProcessReportEntry],
    ) -> dict[str, list[str]]:
        """
        Collect all texts and process them to extract stems

        Args:
            segments: List of transcription segments
            chapter_markers: List of chapter markers
            report: Report list to append logs to

        Returns:
            Dictionary mapping text to list of word stems
        """
        report.append(
            self._create_report_entry(
                "INFO", "Phase 1: Collecting all texts for batch processing."
            )
        )

        all_texts = set()
        all_texts.update(chapter_markers)

        # Add segment texts
        for segment in segments:
            all_texts.add(segment.text)

        # Add cross-segment combinations (2-3 consecutive segments)
        for i in range(len(segments) - 1):
            for span in range(2, min(4, len(segments) - i)):
                combined_text = " ".join(segments[i + j].text for j in range(span))
                all_texts.add(combined_text)

        all_texts_list = list(all_texts)
        report.append(
            self._create_report_entry(
                "INFO",
                f"Collected {len(all_texts_list)} unique texts for batch processing.",
            )
        )

        # Batch process all texts to extract stems
        text_to_stems = await self.batch_extract_stems(all_texts_list, report)

        if not text_to_stems:
            text_to_stems = await self._fallback_process_markers(
                chapter_markers, report
            )

        return text_to_stems

    async def _process_chunk_texts(
        self,
        chunk_texts: list[str],
        chunk_number: int,
        report: list[ProcessReportEntry],
    ) -> tuple[dict[str, list[str]], int, int]:
        """
        Process a single chunk of texts for stem extraction

        Args:
            chunk_texts: List of texts in this chunk
            chunk_number: Chunk number for logging
            report: Report list to append logs to

        Returns:
            Tuple of (text_to_stems_dict, successful_count, failed_count)
        """
        try:
            # Create batch processing request
            batch_request = BatchTextProcessingRequest(
                texts=chunk_texts,
                enable_normalization=True,
                enable_stemming=True,
                enable_root_extraction=False,
                # Note: Enhanced normalization parameters now mandatory
                # Note: Diacritics removal from stems/roots is now mandatory
            )

            batch_result = await self.external_clients.camel_tools.process_batch(
                batch_request
            )

            if not batch_result.success:
                return self._handle_chunk_failure(
                    chunk_texts,
                    chunk_number,
                    batch_result.message,
                    report,
                )

            results = batch_result.results or []
            text_to_stems: dict[str, list[str]] = {}
            successful_count = 0
            failed_count = 0

            for i, text in enumerate(chunk_texts):
                if i < len(results):
                    success = self._process_individual_text_result(
                        text, results[i], text_to_stems, report
                    )
                    if success:
                        successful_count += 1
                    else:
                        failed_count += 1
                else:
                    report.append(
                        self._create_report_entry(
                            "WARNING", f"No result for text '{text[:30]}...'"
                        )
                    )
                    text_to_stems[text] = []
                    failed_count += 1

            return text_to_stems, successful_count, failed_count

        except Exception as chunk_error:
            return self._handle_chunk_failure(
                chunk_texts, chunk_number, str(chunk_error), report
            )

    def _process_individual_text_result(
        self,
        text: str,
        result: Any,
        text_to_stems: dict[str, list[str]],
        report: list[ProcessReportEntry],
    ) -> bool:
        """
        Process the result for a single text

        Args:
            text: The original text
            result: The processing result from camel-tools-api
            text_to_stems: Dictionary to store the results
            report: Report list to append logs to

        Returns:
            True if processing was successful, False otherwise
        """
        # Handle result as dictionary (from API response) or object
        result_success = (
            result.get("success", False) if isinstance(result, dict) else result.success
        )
        result_words = (
            result.get("words", []) if isinstance(result, dict) else result.words
        )
        result_message = (
            result.get("message", "Unknown error")
            if isinstance(result, dict)
            else result.message
        )

        if result_success:
            words_info = result_words or []
            word_stems: list[str] = []
            for word in words_info:
                stem = word.get("stem", word.get("word", ""))
                if isinstance(stem, str) and stem.strip():
                    word_stems.append(stem)
            text_to_stems[text] = word_stems
            return True
        report.append(
            self._create_report_entry(
                "WARNING",
                f"Failed to process text '{text[:30]}...': {result_message}",
            )
        )
        text_to_stems[text] = []
        return False

    def _handle_chunk_failure(
        self,
        chunk_texts: list[str],
        chunk_number: int,
        error_message: str,
        report: list[ProcessReportEntry],
    ) -> tuple[dict[str, list[str]], int, int]:
        """
        Handle failure of an entire chunk

        Args:
            chunk_texts: List of texts in the failed chunk
            chunk_number: Chunk number for logging
            error_message: Error message to log
            report: Report list to append logs to

        Returns:
            Tuple of (text_to_stems_dict, successful_count, failed_count)
        """
        report.append(
            self._create_report_entry(
                "ERROR", f"Error processing chunk {chunk_number}: {error_message}"
            )
        )

        # Mark all texts in this chunk as failed
        text_to_stems: dict[str, list[str]] = {}
        for text in chunk_texts:
            text_to_stems[text] = []

        return text_to_stems, 0, len(chunk_texts)

    async def _fallback_process_markers(
        self, chapter_markers: list[str], report: list[ProcessReportEntry]
    ) -> dict[str, list[str]]:
        """
        Fallback processing for chapter markers when batch processing fails

        Args:
            chapter_markers: List of chapter markers to process
            report: Report list to append logs to

        Returns:
            Dictionary mapping markers to their stems
        """
        report.append(
            self._create_report_entry(
                "WARNING",
                "Batch processing failed. Falling back to individual processing.",
            )
        )

        text_to_stems = {}
        for marker in chapter_markers:
            try:
                stems = await self.extract_stems(marker, report)
                text_to_stems[marker] = stems
                report.append(
                    self._create_report_entry(
                        "INFO",
                        f"Fallback processing successful for marker: '{marker}'",
                    )
                )
            except Exception as e:
                report.append(
                    self._create_report_entry(
                        "WARNING",
                        f"Fallback processing failed for marker '{marker}': {e}",
                    )
                )
                text_to_stems[marker] = []

        if not any(stems for stems in text_to_stems.values()):
            report.append(
                self._create_report_entry(
                    "ERROR",
                    "Both batch and fallback processing failed."
                    "Aborting chapter detection.",
                )
            )
            return {}

        return text_to_stems

    def _create_report_entry(self, level: str, message: str) -> ProcessReportEntry:
        """
        Helper to create a ProcessReportEntry

        Args:
            level: Log level (INFO, WARNING, ERROR, SUCCESS)
            message: Log message

        Returns:
            ProcessReportEntry object
        """
        return ProcessReportEntry(
            timestamp=datetime.now(timezone.utc).isoformat(),
            source=self.__class__.__name__,
            level=level,
            message=message,
        )
