"""
Service layer for audio processor

This module contains the business logic implementations for audio processing,
chapter detection, and related services following clean architecture principles.

The services are organized into functional modules:
- transcription: Audio transcription using WhisperX
- chapter_detection: Chapter detection with advanced pattern matching
- chapter_processing: Chapter splitting and post-processing
- orchestration: Main workflow coordination
"""

# Import from functional modules
from .chapter_detection import (
    ChapterDetectionOrchestratorImpl,
    CrossSegmentMatchingServiceImpl,
    SegmentReorganizationServiceImpl,
    TextPreprocessingServiceImpl,
    WordLevelTimestampServiceImpl,
)
from .chapter_processing import ChapterSplittingServiceImpl
from .orchestration import AudioProcessingServiceImpl
from .transcription import AudioTranscriptionServiceImpl


__all__ = [
    # Orchestration services
    "AudioProcessingServiceImpl",
    # Transcription services
    "AudioTranscriptionServiceImpl",
    # Chapter detection services
    "ChapterDetectionOrchestratorImpl",
    # Chapter processing services
    "ChapterSplittingServiceImpl",
    "CrossSegmentMatchingServiceImpl",
    "SegmentReorganizationServiceImpl",
    "TextPreprocessingServiceImpl",
    "WordLevelTimestampServiceImpl",
]
