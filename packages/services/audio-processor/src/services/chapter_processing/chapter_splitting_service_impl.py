"""
Chapter splitting service implementation

Provides functionality to split audio files into individual chapters based on
detected chapter boundaries, with proper audio processing and metadata handling.
"""

import base64
from datetime import datetime, timezone
from pathlib import Path
from typing import Any

from core.config import Settings
from loguru import logger
from pydub import AudioSegment

from models.api.process_report_entry import ProcessReportEntry
from models.domain.chapter import Chapter
from models.domain.transcription_segment import TranscriptionSegment
from models.services.chapter_split_result import ChapterSplitResult


class ChapterSplittingServiceImpl:
    """
    Implementation of chapter splitting service

    Handles splitting audio files into individual chapters based on detected
    chapter boundaries, providing base64-encoded audio data and metadata.
    """

    def __init__(self, settings: Settings):
        """
        Initialize the chapter splitting service

        Args:
            settings: Application settings containing configuration
        """
        self.settings = settings

    def split_chapters(
        self,
        audio_file_path: str,
        segments: list[TranscriptionSegment],
        raw_chapters: list[dict[str, Any]],
        audio_duration: float,
    ) -> ChapterSplitResult:
        """
        Split audio file into individual chapter files

        Args:
            audio_file_path: Path to the original audio file
            segments: List of transcription segments
            raw_chapters: List of detected chapter information
            audio_duration: Total duration of the audio file

        Returns:
            ChapterSplitResult containing chapter data and split report
        """
        report: list[ProcessReportEntry] = []
        chapters: list[Chapter] = []

        try:
            report.append(
                self._create_report_entry(
                    "INFO",
                    f"Starting chapter splitting for {len(raw_chapters)} chapters",
                )
            )

            # Load the audio file
            audio_file = Path(audio_file_path)
            if not audio_file.exists():
                error_msg = f"Audio file not found: {audio_file_path}"
                report.append(self._create_report_entry("ERROR", error_msg))
                return ChapterSplitResult(
                    success=False,
                    message=error_msg,
                    chapters=[],
                    split_report=report,
                )

            # Load audio using pydub
            try:
                audio = AudioSegment.from_file(str(audio_file))
                report.append(
                    self._create_report_entry(
                        "INFO",
                        f"Loaded audio file: {audio_file.name} "
                        f"(duration: {len(audio) / 1000:.2f}s)",
                    )
                )
            except Exception as e:
                error_msg = f"Failed to load audio file: {e}"
                report.append(self._create_report_entry("ERROR", error_msg))
                return ChapterSplitResult(
                    success=False,
                    message=error_msg,
                    chapters=[],
                    split_report=report,
                )

            # Process each chapter
            for i, raw_chapter in enumerate(raw_chapters):
                try:
                    chapter = self._process_single_chapter(
                        audio=audio,
                        raw_chapter=raw_chapter,
                        chapter_index=i,
                        segments=segments,
                        audio_duration=audio_duration,
                        total_chapters=len(raw_chapters),
                        report=report,
                    )
                    if chapter:
                        chapters.append(chapter)
                except Exception as e:
                    error_msg = f"Failed to process chapter {i + 1}: {e}"
                    report.append(self._create_report_entry("ERROR", error_msg))
                    logger.error(error_msg)

            success_count = len(chapters)
            total_count = len(raw_chapters)

            if success_count == total_count:
                message = f"Successfully split audio into {success_count} chapters"
                report.append(self._create_report_entry("SUCCESS", message))
                return ChapterSplitResult(
                    success=True,
                    message=message,
                    chapters=chapters,
                    split_report=report,
                )
            message = (
                f"Partially successful: {success_count}/{total_count} "
                f"chapters processed"
            )
            report.append(self._create_report_entry("WARNING", message))
            return ChapterSplitResult(
                success=False,
                message=message,
                chapters=chapters,
                split_report=report,
            )

        except Exception as e:
            error_msg = f"Chapter splitting failed: {e}"
            report.append(self._create_report_entry("ERROR", error_msg))
            logger.error(error_msg)
            return ChapterSplitResult(
                success=False,
                message=error_msg,
                chapters=[],
                split_report=report,
            )

    def _process_single_chapter(
        self,
        audio: AudioSegment,
        raw_chapter: dict[str, Any],
        chapter_index: int,
        segments: list[TranscriptionSegment],
        audio_duration: float,
        total_chapters: int,
        report: list[ProcessReportEntry],
    ) -> Chapter | None:
        """
        Process a single chapter: extract audio, encode to base64, and gather text

        Args:
            audio: Loaded audio segment
            raw_chapter: Raw chapter information
            chapter_index: Index of the chapter (0-based)
            segments: List of transcription segments
            audio_duration: Total audio duration
            total_chapters: Total number of chapters
            report: Report list to append logs to

        Returns:
            Chapter object with audio data and text, or None if processing fails
        """
        try:
            title = raw_chapter.get("title", f"Chapter {chapter_index + 1}")
            start_time = raw_chapter.get("start_time", 0.0)

            # Calculate end time
            if chapter_index < total_chapters - 1:
                # Use next chapter's start time as end time
                end_time = raw_chapter.get("end_time", audio_duration)
            else:
                # Last chapter goes to the end of audio
                end_time = audio_duration

            report.append(
                self._create_report_entry(
                    "INFO",
                    f"Processing chapter '{title}' "
                    f"({start_time:.2f}s - {end_time:.2f}s)",
                )
            )

            # Extract audio segment (convert seconds to milliseconds for pydub)
            start_ms = int(start_time * 1000)
            end_ms = int(end_time * 1000)

            # Ensure we don't exceed audio bounds
            audio_length_ms = len(audio)
            start_ms = max(0, min(start_ms, audio_length_ms))
            end_ms = max(start_ms, min(end_ms, audio_length_ms))

            chapter_audio = audio[start_ms:end_ms]

            # Export to bytes and encode as base64
            audio_bytes = chapter_audio.export(format="mp3").read()
            audio_base64 = base64.b64encode(audio_bytes).decode("utf-8")

            # Gather text content for this chapter
            chapter_text = self._extract_chapter_text(
                segments=segments,
                start_time=start_time,
                end_time=end_time,
            )

            report.append(
                self._create_report_entry(
                    "SUCCESS",
                    "Chapter '"
                    + title
                    + "' processed successfully "
                    + (
                        f"(audio: {len(audio_bytes)} bytes, "
                        f"text: {len(chapter_text)} chars)"
                    ),
                )
            )

            return Chapter(
                title=title,
                start_time=start_time,
                end_time=end_time,
                audio_data=audio_base64,
                text=chapter_text,
            )

        except Exception as e:
            error_msg = f"Failed to process chapter {chapter_index + 1}: {e}"
            report.append(self._create_report_entry("ERROR", error_msg))
            return None

    def _extract_chapter_text(
        self,
        segments: list[TranscriptionSegment],
        start_time: float,
        end_time: float,
    ) -> str:
        """
        Extract text content for a chapter based on time boundaries

        Args:
            segments: List of transcription segments
            start_time: Chapter start time in seconds
            end_time: Chapter end time in seconds

        Returns:
            Combined text content for the chapter
        """
        chapter_texts = []

        for segment in segments:
            # Check if segment overlaps with chapter time range
            if segment.start < end_time and segment.end > start_time:
                chapter_texts.append(segment.text.strip())

        return " ".join(chapter_texts)

    def _create_report_entry(self, level: str, message: str) -> ProcessReportEntry:
        """
        Helper to create a ProcessReportEntry

        Args:
            level: Log level (INFO, WARNING, ERROR, SUCCESS)
            message: Log message

        Returns:
            ProcessReportEntry object
        """
        return ProcessReportEntry(
            timestamp=datetime.now(timezone.utc).isoformat(),
            source=self.__class__.__name__,
            level=level,
            message=message,
        )
