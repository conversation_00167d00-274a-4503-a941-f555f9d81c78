#!/usr/bin/env python3
"""
WhisperX audio processing service main entry point
Provides Arabic audio transcription, VAD detection, and timestamp alignment
"""

import os
import site
import sys
from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager
from pathlib import Path

import uvicorn
from api import api_router
from api.dependencies import set_di_container
from core.config import get_settings
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from infrastructure.audio_processing_service_factory_impl import (
    AudioProcessingServiceFactoryImpl,
)
from infrastructure.dicontainer import DIContainer
from loguru import logger


# 自动库路径检测和设置
def setup_library_paths() -> None:
    """
    检测并设置CUDA库路径
    确保动态链接器能找到必要的CUDA库文件
    """
    # 获取当前Python环境的site-packages目录
    site_packages = site.getsitepackages()

    # 尝试查找NVIDIA CUDA库路径
    cudnn_paths = []
    for site_dir in site_packages:
        cuda_lib_path = Path(site_dir) / "nvidia" / "cudnn" / "lib"
        if cuda_lib_path.exists():
            cudnn_paths.append(str(cuda_lib_path))

    # 尝试查找项目虚拟环境中的CUDA库
    venv_path = Path(__file__).resolve().parent.parent / ".venv"
    if venv_path.exists():
        venv_lib_path = (
            venv_path
            / "lib"
            / f"python{sys.version_info.major}.{sys.version_info.minor}"
            / "site-packages"
            / "nvidia"
            / "cudnn"
            / "lib"
        )
        if venv_lib_path.exists():
            cudnn_paths.append(str(venv_lib_path))

    # 设置LD_LIBRARY_PATH环境变量
    if cudnn_paths:
        current_ld_path = os.environ.get("LD_LIBRARY_PATH", "")
        new_ld_path = ":".join(cudnn_paths)
        if current_ld_path:
            new_ld_path = f"{new_ld_path}:{current_ld_path}"
        os.environ["LD_LIBRARY_PATH"] = new_ld_path
        logger.info(f"CUDA库路径已设置: {new_ld_path}")
    else:
        logger.warning("未找到CUDA库路径，GPU加速可能不可用")


# 在应用启动前设置库路径
setup_library_paths()

settings = get_settings()


@asynccontextmanager
async def lifespan(_app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Manage application lifespan events using clean architecture with DI container

    Handles startup and shutdown of the audio processing service with proper
    resource management and dependency injection.
    """
    # Startup
    di_container = None
    try:
        logger.info("Initializing Audio Processing Service with Clean Architecture...")

        # Create and configure DI container, now it is empty but will be populated later
        di_container = DIContainer()

        # Create service factory with settings
        service_factory = AudioProcessingServiceFactoryImpl(settings)

        # Register factory in DI container,
        # now the factory has been registered in the container
        di_container.register_factory(service_factory)

        # Set global DI container for API routes to use
        set_di_container(di_container)

        # Initialize all services asynchronously
        await di_container.initialize_services()

        logger.success(
            "🚀 Audio Processing Service with Clean Architecture started successfully"
        )
        logger.info(f"Service running on http://{settings.HOST}:{settings.PORT}")
        logger.info(f"Model: {settings.MODEL_PATH}")
        logger.info(f"Device: {settings.DEVICE}")
        logger.info(f"Registered services: {di_container.get_registered_services()}")

    except Exception as e:
        logger.error(f"❌ Service startup failed: {e}")
        # Clean up partially initialized container
        if di_container:
            try:
                await di_container.cleanup_services()
            except Exception as cleanup_error:
                logger.error(f"Error during startup cleanup: {cleanup_error}")

        # Re-raise to prevent application from starting with broken state
        raise

    yield

    # Shutdown
    logger.info("Shutting down Audio Processing Service...")

    try:
        # Clear global DI container first
        set_di_container(None)
        logger.debug("Global DI container cleared")

        # Cleanup all services through DI container
        if di_container is not None:
            try:
                await di_container.cleanup_services()
                logger.info("All services cleaned up successfully")
            except Exception as cleanup_error:
                logger.warning(f"Error during service cleanup: {cleanup_error}")
            finally:
                # Clear the reference
                di_container = None

        logger.success("🛑 Audio Processing Service shutdown complete")

    except Exception as shutdown_error:
        logger.error(f"Error during service shutdown: {shutdown_error}")
        # Continue with shutdown even if cleanup fails


# FastAPI application
app = FastAPI(
    title="WhisperX Arabic Audio Processing Service",
    description="Arabic-specialized audio transcription and processing service "
    "based on WhisperX with chapter detection and splitting capabilities",
    version="1.0.0",
    lifespan=lifespan,
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Should restrict to specific domains in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Register API routes
app.include_router(api_router)


if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8001, reload=True, log_level="info")
