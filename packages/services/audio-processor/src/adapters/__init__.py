"""
Adapter layer for audio processor service

This module provides adapters for converting between API models and internal models,
maintaining clean separation between the API layer and business logic.
"""

# Import from individual files following one-class-per-file rule
from .audio_processing_request_adapter import AudioProcessingRequestAdapter
from .audio_processing_response_adapter import AudioProcessingResponseAdapter
from .file_upload_adapter import FileUploadAdapter


__all__ = [
    "AudioProcessingRequestAdapter",
    "AudioProcessingResponseAdapter",
    "FileUploadAdapter",
]
