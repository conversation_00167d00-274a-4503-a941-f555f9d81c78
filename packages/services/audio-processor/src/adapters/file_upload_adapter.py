"""
File upload adapter for the audio processor service

Provides clean separation between the API layer and business logic by handling
file upload operations, validation, and temporary file management.
"""

from pathlib import Path
from typing import Optional

from fastapi import HTTPException, UploadFile
from loguru import logger


class FileUploadAdapter:
    """
    Adapter for handling file upload operations

    Manages temporary file creation, validation, and cleanup for uploaded audio files.
    """

    @staticmethod
    async def save_uploaded_file(file: UploadFile) -> Path:
        """
        Save uploaded file to temporary location

        Args:
            file: FastAPI uploaded file object

        Returns:
            Path to the saved temporary file

        Raises:
            HTTPException: If file saving fails
        """
        try:
            temp_file_path = Path(f"/tmp/{file.filename}")
            with temp_file_path.open("wb") as buffer:
                content = await file.read()
                buffer.write(content)

            logger.debug(f"Saved uploaded file to: {temp_file_path}")
            return temp_file_path
        except Exception as e:
            error_msg = f"Failed to save uploaded file: {e}"
            logger.error(error_msg)
            raise HTTPException(status_code=500, detail=error_msg) from e

    @staticmethod
    def cleanup_temp_file(file_path: Optional[Path]) -> None:
        """
        Clean up temporary file

        Args:
            file_path: File path to delete, can be None
        """
        if file_path and file_path.exists():
            try:
                file_path.unlink()
                logger.debug(f"Cleaned up temporary file: {file_path}")
            except Exception as e:
                logger.warning(f"Failed to cleanup temporary file {file_path}: {e}")
