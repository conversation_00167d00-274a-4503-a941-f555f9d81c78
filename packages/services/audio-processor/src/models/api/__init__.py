"""
API models package

Contains external API contract models for the audio processor service.
These models define the stable external interface and should maintain
backward compatibility.
"""

# Import from individual files following one-class-per-file rule
from .health_status import HealthStatus
from .model_info import ModelInfo
from .process_report_entry import ProcessReportEntry
from .supported_language import SupportedLanguage
from .transcription_request import TranscriptionRequest
from .transcription_response import TranscriptionResponse


__all__ = [
    "HealthStatus",
    "ModelInfo",
    "ProcessReportEntry",
    "SupportedLanguage",
    "TranscriptionRequest",
    "TranscriptionResponse",
]
