"""
Model information model for the audio processor service

Contains the ModelInfo Pydantic model that provides
information about loaded models and service capabilities.
"""

from pydantic import BaseModel, Field


class ModelInfo(BaseModel):
    """Model information"""

    model_path: str = Field(description="Model file path")
    device: str = Field(description="Runtime device (cuda/cpu)")
    compute_type: str = Field(description="Computation precision type")
    model_loaded: bool = Field(description="Whether main model is loaded")
    align_model_loaded: bool = Field(description="Whether alignment model is loaded")
    diarization_model_loaded: bool = Field(
        description="Whether speaker diarization model is loaded"
    )
    supported_languages: list[str] = Field(description="List of supported languages")
