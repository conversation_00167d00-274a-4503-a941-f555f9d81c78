"""
Health status model for the audio processor service

Contains the HealthStatus Pydantic model that provides
service health status information for monitoring.
"""

from typing import Any, Optional

from pydantic import BaseModel, Field


class HealthStatus(BaseModel):
    """Service health status information"""

    status: str = Field(description="Service status")
    model_loaded: bool = Field(description="Whether model is loaded")
    model_path: str = Field(description="Model path")
    uptime: Optional[float] = Field(
        default=None, description="Service uptime (seconds)"
    )
    memory_usage: Optional[dict[str, Any]] = Field(
        default=None, description="Memory usage information"
    )
    version: Optional[str] = Field(default=None, description="Service version")
