"""
Transcription request model for the audio processor service

Contains the TranscriptionRequest Pydantic model that defines
the external API interface for transcription requests.
"""

from typing import TYPE_CHECKING, Optional

from pydantic import BaseModel, Field, ValidationInfo, field_validator

from .supported_language import SupportedLanguage


if TYPE_CHECKING:
    from models.config.transcription import TranscriptionConfig


class TranscriptionRequest(BaseModel):
    """
    Transcription request model

    Provides detailed field descriptions and validation rules, which will be
    reflected in the automatically generated API documentation.
    """

    language: Optional[SupportedLanguage] = Field(
        default=SupportedLanguage.ARABIC,
        description=(
            "Transcription language code. This service is specialized for "
            "Arabic (ar) transcription."
        ),
    )

    vad_onset: float = Field(
        default=0.5,
        ge=0.0,
        le=1.0,
        description=(
            "Voice activity detection onset threshold. Lower values are more "
            "sensitive, range 0.0-1.0."
        ),
    )

    vad_offset: float = Field(
        default=0.363,
        ge=0.0,
        le=1.0,
        description=(
            "Voice activity detection offset threshold. Lower values are more "
            "sensitive, range 0.0-1.0."
        ),
    )

    chunk_size: int = Field(
        default=30,
        gt=0,
        le=300,
        description=(
            "Audio chunk size for processing (seconds). Larger values may "
            "improve accuracy but increase memory usage."
        ),
    )

    enable_diarization: bool = Field(
        default=False,
        description=(
            "Whether to enable speaker diarization. When enabled, can identify "
            "different speakers."
        ),
    )

    min_speakers: Optional[int] = Field(
        default=None,
        ge=1,
        le=20,
        description=(
            "Minimum number of speakers for diarization. Only effective when "
            "speaker diarization is enabled."
        ),
    )

    max_speakers: Optional[int] = Field(
        default=None,
        ge=1,
        le=20,
        description=(
            "Maximum number of speakers for diarization. Only effective when "
            "speaker diarization is enabled."
        ),
    )

    # Chapter detection parameters
    chapter_timestamps: Optional[list[float]] = Field(
        default=None,
        description=(
            "List of chapter start times in seconds. When provided, audio will be "
            "divided into chapters at these timestamps. Takes precedence over chapter_markers."
        ),
    )

    chapter_markers: Optional[list[str]] = Field(
        default=None,
        description=(
            "List of chapter title markers to detect in the transcribed text. "
            "The service will use intelligent detection to find these markers in the text."
        ),
    )

    split_chapters: bool = Field(
        default=False,
        description=(
            "Whether to split audio and text by chapters. When enabled, the response "
            "will include audio data and text content for each chapter. Requires "
            "either chapter_timestamps or chapter_markers to be provided."
        ),
    )

    # Translation parameters
    enable_translation: bool = Field(
        default=False,
        description=(
            "Whether to enable translation of transcribed text. When enabled, "
            "transcribed segments will be translated using the qwen-service."
        ),
    )

    target_language: str = Field(
        default="中文",
        description=(
            "Target language for translation. Default is Chinese (中文). "
            "Only effective when enable_translation is True."
        ),
    )

    translation_context_segments: int = Field(
        default=4,
        ge=1,
        le=8,
        description=(
            "Number of context segments to include before and after each target "
            "segment for translation. Default is 4 (total 9 segments per request: "
            "4 preceding + 1 current + 4 following). Only effective when "
            "enable_translation is True."
        ),
    )

    # Tell pydantic this field needs validation
    @field_validator("max_speakers")
    @classmethod
    def validate_speaker_range(
        cls, v: Optional[int], info: ValidationInfo
    ) -> Optional[int]:
        """Validate speaker count range"""
        if (
            v is not None
            and "min_speakers" in info.data
            and info.data["min_speakers"] is not None
            and v < info.data["min_speakers"]
        ):
            error_msg = "max_speakers cannot be less than min_speakers"
            raise ValueError(error_msg)
        return v

    @field_validator("split_chapters")
    @classmethod
    def validate_split_chapters(cls, v: bool, info: ValidationInfo) -> bool:
        """Validate split_chapters parameter requires chapter detection"""
        if v and not (
            info.data.get("chapter_timestamps") or info.data.get("chapter_markers")
        ):
            error_msg = "split_chapters requires either chapter_timestamps or chapter_markers to be provided"
            raise ValueError(error_msg)
        return v

    def to_config(self, audio_file: str) -> "TranscriptionConfig":
        """Convert to TranscriptionConfig object"""
        # Import here to avoid circular imports
        from models.config.transcription import TranscriptionConfig

        return TranscriptionConfig(
            audio_file=audio_file,
            language=self.language.value if self.language else None,
            vad_onset=self.vad_onset,
            vad_offset=self.vad_offset,
            chunk_size=self.chunk_size,
            enable_diarization=self.enable_diarization,
            min_speakers=self.min_speakers,
            max_speakers=self.max_speakers,
            chapter_timestamps=self.chapter_timestamps,
            chapter_markers=self.chapter_markers,
            split_chapters=self.split_chapters,
            enable_translation=self.enable_translation,
            target_language=self.target_language,
            translation_context_segments=self.translation_context_segments,
        )
