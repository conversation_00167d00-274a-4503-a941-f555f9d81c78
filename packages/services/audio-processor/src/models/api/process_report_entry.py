"""
Process report entry model for the audio processor service

Contains the ProcessReportEntry Pydantic model that represents
a single entry in a process report for detailed service insights.
"""

from pydantic import BaseModel, Field


class ProcessReportEntry(BaseModel):
    """
    A single entry in a process report, providing detailed insight into
    the internal operations of a service.
    """

    timestamp: str = Field(description="Timestamp of the event (ISO 8601 format)")
    source: str = Field(
        description="The service or component that generated the entry (e.g., 'ChapterDetectionService')"
    )
    level: str = Field(
        description="Log level (e.g., 'INFO', 'DEBUG', 'SUCCESS', 'ERROR')"
    )
    message: str = Field(description="Detailed log message")
