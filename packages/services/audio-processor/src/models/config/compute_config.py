"""
Compute configuration for the audio processor service

Contains the ComputeConfig dataclass that manages computation-related
configuration for the transcription service.
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class ComputeConfig:
    """Manages computation-related configuration for the transcription service"""

    device: Optional[str] = None
    compute_type: Optional[str] = None
