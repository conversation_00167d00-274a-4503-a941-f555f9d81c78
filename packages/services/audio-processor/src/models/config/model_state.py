"""
Model state configuration for the audio processor service

Contains the ModelState dataclass that manages all model-related
state for the transcription service.
"""

from dataclasses import dataclass
from typing import Any, Optional


@dataclass
class ModelState:
    """Manages all model-related state for the transcription service"""

    model: Optional[Any] = None
    align_model: Optional[Any] = None
    align_metadata: Optional[Any] = None
    diarize_model: Optional[Any] = None
    current_align_lang: Optional[str] = None
