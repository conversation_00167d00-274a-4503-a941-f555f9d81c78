"""
Data models package

Provides centralized access to all data models organized by clean architecture principles.
"""

# API models - updated for one-class-per-file structure
from .api.health_status import HealthStatus
from .api.model_info import ModelInfo
from .api.process_report_entry import ProcessReportEntry
from .api.supported_language import SupportedLanguage
from .api.transcription_request import TranscriptionRequest
from .api.transcription_response import TranscriptionResponse

# Configuration models - updated for one-class-per-file structure
from .config.compute_config import ComputeConfig
from .config.model_state import ModelState
from .config.transcription_config import TranscriptionConfig

# Domain models - updated for one-class-per-file structure
from .domain.chapter import Chapter
from .domain.transcription_segment import TranscriptionSegment
from .domain.word_segment import WordSegment

# Service models - updated for one-class-per-file structure
from .services.audio_processing_request import AudioProcessingRequest
from .services.audio_processing_response import AudioProcessingResponse
from .services.chapter_detection_result import ChapterDetectionResult
from .services.chapter_split_result import ChapterSplitResult
from .services.transcription_result import TranscriptionResult


__all__ = [
    # API models
    "SupportedLanguage",
    "TranscriptionRequest",
    "HealthStatus",
    "ModelInfo",
    "ProcessReportEntry",
    "TranscriptionResponse",
    # Domain models
    "Chapter",
    "TranscriptionSegment",
    "WordSegment",
    # Configuration models
    "ComputeConfig",
    "ModelState",
    "TranscriptionConfig",
    # Service models
    "AudioProcessingRequest",
    "AudioProcessingResponse",
    "ChapterDetectionResult",
    "ChapterSplitResult",
    "TranscriptionResult",
]
