"""
Segment match result model for chapter detection service

Contains the SegmentMatchResult dataclass that represents
the result of matching within a single segment.
"""

from dataclasses import dataclass
from typing import List, Optional


@dataclass
class SegmentMatchResult:
    """Result of matching within a single segment"""

    complete_match: bool
    failed_match: bool
    current_stem_index: int
    matched_words: List[str]
    complete_text: Optional[str]
