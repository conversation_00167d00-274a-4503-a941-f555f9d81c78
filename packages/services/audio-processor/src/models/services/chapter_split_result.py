"""
Chapter split result model for the audio processor service

Contains the ChapterSplitResult dataclass that represents
the result of a chapter splitting operation.
"""

from dataclasses import dataclass
from typing import Optional

from models.api.process_report_entry import ProcessReportEntry
from models.domain.chapter import Chapter


@dataclass
class ChapterSplitResult:
    """Result of chapter splitting operation"""

    success: bool
    message: str
    chapters: list[Chapter]
    split_report: Optional[list[ProcessReportEntry]] = None
