"""
Chapter detection result model for the audio processor service

Contains the ChapterDetectionResult dataclass that represents
the result of a chapter detection operation.
"""

from dataclasses import dataclass
from typing import Any, Optional

from models.api.process_report_entry import ProcessReportEntry
from models.domain.transcription_segment import TranscriptionSegment


@dataclass
class ChapterDetectionResult:
    """Result of chapter detection operation"""

    success: bool
    message: str
    chapters: list[dict[str, Any]]
    reorganized_segments: Optional[list[TranscriptionSegment]] = None
    detection_report: Optional[list[ProcessReportEntry]] = None
