"""
Transcription result model for the audio processor service

Contains the TranscriptionResult dataclass that represents
the result of an audio transcription operation.
"""

from dataclasses import dataclass
from typing import Optional

from models.domain.transcription_segment import TranscriptionSegment


@dataclass
class TranscriptionResult:
    """Result of audio transcription operation"""

    success: bool
    message: str
    segments: list[TranscriptionSegment]
    language: Optional[str] = None
    duration: Optional[float] = None
    processing_time: Optional[float] = None
    word_count: Optional[int] = None
