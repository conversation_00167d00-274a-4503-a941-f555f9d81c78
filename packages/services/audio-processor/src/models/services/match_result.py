"""
Match result model for chapter detection service

Contains the MatchResult dataclass that represents
the result of a chapter marker matching operation.
"""

from dataclasses import dataclass
from typing import Any, List, Optional


@dataclass
class MatchResult:
    """Result of a chapter marker matching operation"""

    found: bool
    segment_index: int
    start_time: float
    confidence: float
    match_type: str  # "single_segment_start", "single_segment_middle", "cross_segment"
    matched_text: Optional[str] = None
    matched_segment_indices: Optional[List[int]] = None
    cross_segment_info: Optional[dict[str, Any]] = None
    # Enhanced word-level boundary information for precise extraction
    start_word_idx: Optional[int] = (
        None  # Word index where chapter marker starts within start segment
    )
    end_word_idx: Optional[int] = (
        None  # Word index where chapter marker ends within end segment
    )
    end_segment_index: Optional[int] = (
        None  # Index of segment where marker ends (for cross-segment)
    )
