"""
Audio processing response model for the audio processor service

Contains the AudioProcessingResponse dataclass that represents
the response from a complete audio processing operation.
"""

from dataclasses import dataclass
from typing import Optional

from models.api.process_report_entry import ProcessReportEntry
from models.domain.chapter import Chapter
from models.domain.transcription_segment import TranscriptionSegment


@dataclass
class AudioProcessingResponse:
    """Response from complete audio processing operation"""

    success: bool
    message: str
    segments: list[TranscriptionSegment]
    chapters: Optional[list[Chapter]] = None
    report: Optional[list[ProcessReportEntry]] = None
    language: Optional[str] = None
    duration: Optional[float] = None
    processing_time: Optional[float] = None
    word_count: Optional[int] = None
    translation_enabled: Optional[bool] = None
    target_language: Optional[str] = None
    translation_processing_time: Optional[float] = None
