"""
Translation response model

Contains the response model for translation operations
with translated segments and processing metadata.
"""

from typing import List, Optional

from pydantic import BaseModel, Field

from models.domain.transcription_segment import TranscriptionSegment


class TranslationResponse(BaseModel):
    """
    Translation response model
    
    Contains the results of translation operation including
    translated segments and processing metadata.
    """

    success: bool = Field(
        description="Whether the translation was successful"
    )
    
    message: str = Field(
        description="Response message"
    )
    
    segments: List[TranscriptionSegment] = Field(
        default_factory=list,
        description="List of segments with translation results"
    )
    
    processing_time: Optional[float] = Field(
        default=None,
        description="Time spent processing translation (seconds)"
    )
    
    translated_count: int = Field(
        default=0,
        description="Number of successfully translated segments"
    )
    
    failed_count: int = Field(
        default=0,
        description="Number of segments that failed translation"
    )
    
    target_language: str = Field(
        description="Target language used for translation"
    )