"""
Text processing response model for external services

Contains the TextProcessingResponse dataclass for
responses from camel-tools-api text processing.
"""

from dataclasses import dataclass
from typing import Any, Optional


@dataclass
class TextProcessingResponse:
    """Response from camel-tools-api text processing"""

    success: bool
    message: str
    original_text: str
    normalized_text: Optional[str] = None
    words: list[dict[str, Any]] = None
