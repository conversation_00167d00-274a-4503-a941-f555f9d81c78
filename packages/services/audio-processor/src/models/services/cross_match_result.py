"""
Cross match result model for chapter detection service

Contains the CrossMatchResult dataclass that represents
the result of a cross-segment matching operation.
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class CrossMatchResult:
    """Result of cross-segment matching operation"""

    success: bool
    end_segment_index: int
    complete_text: Optional[str]
    matched_words_count: int
    full_matched_text: Optional[str] = None
