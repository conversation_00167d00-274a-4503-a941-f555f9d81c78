"""
Batch text processing request model for external services

Contains the BatchTextProcessingRequest dataclass for
batch text processing via camel-tools-api.
"""

from dataclasses import dataclass


@dataclass
class BatchTextProcessingRequest:
    """Request for batch text processing via camel-tools-api"""

    texts: list[str]
    enable_normalization: bool = True
    enable_stemming: bool = True
    enable_root_extraction: bool = False
    # Note: Enhanced normalization parameters (remove_punctuation, normalize_digits,
    # normalize_whitespace, normalize_english_case) are now mandatory in camel-tools-api
    # Note: Diacritics removal from stems and roots is now mandatory and always enabled
