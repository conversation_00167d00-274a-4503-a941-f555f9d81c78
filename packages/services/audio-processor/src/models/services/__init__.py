"""
Service models package

Contains service-specific data structures, DTOs, and result objects
used for internal processing and communication between services.
These models can evolve to optimize service performance.
"""

# Import from individual files following one-class-per-file rule
from .audio_processing_request import AudioProcessingRequest
from .audio_processing_response import AudioProcessingResponse
from .batch_text_processing_request import BatchTextProcessingRequest
from .batch_text_processing_response import BatchTextProcessingResponse
from .chapter_detection_result import ChapterDetectionResult
from .chapter_split_result import ChapterSplitResult
from .cross_match_result import CrossMatchResult
from .match_result import MatchResult
from .segment_match_result import SegmentMatchResult
from .semantic_validation_request import SemanticValidationRequest
from .semantic_validation_response import SemanticValidationResponse
from .text_processing_request import TextProcessingRequest
from .text_processing_response import TextProcessingResponse
from .transcription_result import TranscriptionResult
from .word_boundary_result import WordBoundaryResult
from .translation_request import TranslationRequest
from .translation_response import TranslationResponse


__all__ = [
    "AudioProcessingRequest",
    "AudioProcessingResponse",
    "BatchTextProcessingRequest",
    "BatchTextProcessingResponse",
    "ChapterDetectionResult",
    "ChapterSplitResult",
    "CrossMatchResult",
    "MatchResult",
    "SegmentMatchResult",
    "SemanticValidationRequest",
    "SemanticValidationResponse",
    "TextProcessingRequest",
    "TextProcessingResponse",
    "TranscriptionResult",
    "WordBoundaryResult",
    "TranslationRequest",
    "TranslationResponse",
]
