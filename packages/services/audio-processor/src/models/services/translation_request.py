"""
Translation request model

Contains the request model for translation operations
with contextual information and configuration.
"""

from typing import List

from pydantic import BaseModel, Field

from models.domain.transcription_segment import TranscriptionSegment


class TranslationRequest(BaseModel):
    """
    Translation request model
    
    Contains segments to translate along with context configuration
    and target language specification.
    """

    segments: List[TranscriptionSegment] = Field(
        description="List of transcription segments to translate"
    )
    
    target_language: str = Field(
        default="中文",
        description="Target language for translation"
    )
    
    context_segments: int = Field(
        default=4,
        ge=1,
        le=8,
        description="Number of context segments before and after target segment"
    )
    
    source_language: str = Field(
        default="Arabic",
        description="Source language of the text"
    )