"""
Word boundary result model for chapter detection service

Contains the WordBoundaryResult dataclass that represents
the result of word-level boundary calculation.
"""

from dataclasses import dataclass
from typing import List, Optional

from models.domain.word_segment import WordSegment


@dataclass
class WordBoundaryResult:
    """Result of word-level boundary calculation"""

    start_time: float
    end_time: float
    chapter_words: Optional[List[WordSegment]] = None
