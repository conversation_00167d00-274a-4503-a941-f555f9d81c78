"""
Audio processing request model for the audio processor service

Contains the AudioProcessingRequest dataclass that represents
a request for complete audio processing operation.
"""

from dataclasses import dataclass
from typing import Any


@dataclass
class AudioProcessingRequest:
    """Request for complete audio processing operation"""

    audio_file_path: str
    config: Any  # Will be TranscriptionConfig, using Any to avoid circular import
