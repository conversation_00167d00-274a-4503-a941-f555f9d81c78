"""
Domain models package

Contains core business domain models that represent the fundamental
concepts of audio processing, transcription, and chapter detection.
These models are independent of external concerns.
"""

# Import from individual files following one-class-per-file rule
from .chapter import Chapter
from .transcription_segment import TranscriptionSegment
from .word_segment import WordSegment


__all__ = [
    "Chapter",
    "TranscriptionSegment",
    "WordSegment",
]
