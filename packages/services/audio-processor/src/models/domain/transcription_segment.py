"""
Transcription segment domain model for the audio processor service

Contains the TranscriptionSegment Pydantic model that represents
transcription segments with timestamps and word-level data.
"""

from typing import Optional

from pydantic import BaseModel, Field

from .word_segment import WordSegment


class TranscriptionSegment(BaseModel):
    """
        Transcription segment

        segments = [
        TranscriptionSegment(
            start=0.0,
            end=15.2,
            text="مرحبا بكم في هذا الكتاب الصوتي",
            words=[
                WordSegment(word="مرحبا", start=0.0, end=1.5),
                WordSegment(word="بكم", start=1.5, end=2.0),
                WordSegment(word="في", start=2.0, end=2.5),
                WordSegment(word="هذا", start=2.5, end=3.0),
                WordSegment(word="الكتاب", start=3.0, end=4.0),
                WordSegment(word="الصوتي", start=4.0, end=5.0)
            ]
        ),
        TranscriptionSegment(
            start=15.2,
            end=28.5,
            text="الفصل الأول الصهيونية تقاتل على جبهة",
            words=[
                WordSegment(word="الفصل", start=15.2, end=16.0),
                WordSegment(word="الأول", start=16.0, end=17.0),
                WordSegment(word="الصهيونية", start=17.0, end=18.5),
                WordSegment(word="تقاتل", start=18.5, end=19.5),
                WordSegment(word="على", start=19.5, end=20.0),
                WordSegment(word="جبهة", start=20.0, end=21.0)
            ]
        ),
        TranscriptionSegment(
            start=28.5,
            end=35.0,
            text="اللغة العربية والثقافة",
            words=[
                WordSegment(word="اللغة", start=28.5, end=29.5),
                WordSegment(word="العربية", start=29.5, end=30.5),
                WordSegment(word="والثقافة", start=30.5, end=32.0)
            ]
        )
    ]
    """

    start: float = Field(description="Segment start time (seconds)")
    end: float = Field(description="Segment end time (seconds)")
    text: str = Field(description="Transcribed text")
    speaker: Optional[str] = Field(
        default=None,
        description="Speaker identifier (if speaker diarization is enabled)",
    )
    words: Optional[list[WordSegment]] = Field(
        default=None,
        description="Word-level timestamps and text (if alignment is enabled)",
    )
    
    # Translation fields
    translated_text: Optional[str] = Field(
        default=None,
        description="Translated text (if translation is enabled)",
    )
