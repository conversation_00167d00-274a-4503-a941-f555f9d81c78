"""
Chapter domain models

Contains core business models related to chapter detection and management,
including chapter information and chapter-related data structures.
"""

from typing import Optional

from pydantic import BaseModel, Field

from models.domain.transcription_segment import TranscriptionSegment


class Chapter(BaseModel):
    """Chapter information"""

    title: str = Field(description="Chapter title")
    start_time: float = Field(description="Chapter start time in seconds")
    end_time: Optional[float] = Field(
        default=None, description="Chapter end time in seconds (if available)"
    )
    audio_data: Optional[str] = Field(
        default=None,
        description=(
            "Base64-encoded audio data for this chapter when split_chapters=True"
        ),
    )
    text: Optional[str] = Field(
        default=None,
        description="Full text of the chapter when split_chapters=True",
    )
    segments: Optional[list[TranscriptionSegment]] = Field(
        default=None,
        description=(
            "List of transcription segments belonging to this chapter "
            "when split_chapters=True"
        ),
    )
