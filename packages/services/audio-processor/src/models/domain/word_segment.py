"""
Word segment domain model for the audio processor service

Contains the WordSegment Pydantic model that represents
word-level transcription data with timestamp information.
"""

from typing import Optional

from pydantic import BaseModel, Field


class WordSegment(BaseModel):
    """Word-level segment with timestamp information"""

    word: str = Field(description="Individual word text")
    start: float = Field(description="Word start time (seconds)")
    end: float = Field(description="Word end time (seconds)")
    confidence: Optional[float] = Field(
        default=None,
        description="Confidence score for the word (0.0-1.0)",
    )
    speaker: Optional[str] = Field(
        default=None,
        description="Speaker identifier (if speaker diarization is enabled)",
    )
