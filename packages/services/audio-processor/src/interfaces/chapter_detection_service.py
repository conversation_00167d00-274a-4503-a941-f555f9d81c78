"""
Chapter detection service interface

Defines the protocol for chapter detection operations in audio transcriptions,
providing standardized chapter detection capabilities.
"""

from typing import Protocol, runtime_checkable

from models.domain.transcription_segment import TranscriptionSegment
from models.services.chapter_detection_result import ChapterDetectionResult


@runtime_checkable
class ChapterDetectionService(Protocol):
    """Protocol for chapter detection operations in audio transcriptions"""

    async def detect_chapters_by_markers(
        self, segments: list[TranscriptionSegment], chapter_markers: list[str]
    ) -> ChapterDetectionResult:
        """
        Detect chapters using predefined text markers

        Args:
            segments: List of transcription segments to analyze
            chapter_markers: List of chapter title markers to search for

        Returns:
            ChapterDetectionResult containing detected chapters and reorganized segments
        """
        ...

    def detect_chapters_by_timestamps(
        self, segments: list[TranscriptionSegment], timestamps: list[float]
    ) -> ChapterDetectionResult:
        """
        Detect chapters using predefined timestamps

        Args:
            segments: List of transcription segments to analyze
            timestamps: List of timestamp markers for chapter boundaries

        Returns:
            ChapterDetectionResult containing detected chapters
        """
        ...
