"""
Word-level timestamp service interface

Defines the protocol for word-level timestamp operations,
providing precise timing information for chapter markers.
"""

from typing import Protocol, runtime_checkable

from models.domain.transcription_segment import TranscriptionSegment
from models.services.word_boundary_result import WordBoundaryResult


@runtime_checkable
class WordLevelTimestampService(Protocol):
    """Protocol for word-level timestamp operations"""

    def find_precise_word_boundaries(
        self,
        segments: list[TranscriptionSegment],
        start_segment_idx: int,
        end_segment_idx: int,
        marker_text: str,
        start_word_offset: int = 0,
    ) -> WordBoundaryResult:
        """
        Find precise start and end timestamps for a marker using word-level data

        Args:
            segments: List of transcription segments
            start_segment_idx: Index of the first segment containing the marker
            end_segment_idx: Index of the last segment containing the marker
            marker_text: The chapter marker text
            start_word_offset: Number of words to skip from the beginning of
                start segment

        Returns:
            WordBoundaryResult with precise timestamps and word data
        """
        ...

    def find_cross_segment_boundaries(
        self,
        segments: list[TranscriptionSegment],
        start_segment_idx: int,
        end_segment_idx: int,
        marker_text: str,
        overlap_words: int,
    ) -> WordBoundaryResult:
        """
        Find precise word-level boundaries for a cross-segment chapter marker

        Args:
            segments: List of transcription segments
            start_segment_idx: Index of the segment where marker starts
            end_segment_idx: Index of the segment where marker ends
            marker_text: The complete chapter marker text
            overlap_words: Number of overlapping words at the end of start segment

        Returns:
            WordBoundaryResult with precise timestamps and word data
        """
        ...
