"""
Translation service interface

Defines the contract for translation services that can translate
transcribed text segments with contextual information.
"""

from abc import ABC, abstractmethod
from typing import List

from models.domain.transcription_segment import TranscriptionSegment
from models.services.translation_request import TranslationRequest
from models.services.translation_response import TranslationResponse


class TranslationService(ABC):
    """
    Abstract base class for translation services
    
    Provides methods for translating transcribed text segments
    with contextual information for improved accuracy.
    """

    @abstractmethod
    async def translate_segments(
        self, request: TranslationRequest
    ) -> TranslationResponse:
        """
        Translate transcribed segments with contextual information
        
        Args:
            request: Translation request containing segments, target language,
                    and context configuration
                    
        Returns:
            TranslationResponse containing translated segments and metadata
            
        Raises:
            RuntimeError: If translation fails
        """
        pass

    @abstractmethod
    async def health_check(self) -> bool:
        """
        Check if the translation service is available
        
        Returns:
            True if service is healthy, False otherwise
        """
        pass