"""
CamelTools client interface

Defines the protocol for camel-tools-api service integration,
providing standardized text processing capabilities.
"""

from typing import Protocol, runtime_checkable

from models.services.batch_text_processing_request import BatchTextProcessingRequest
from models.services.batch_text_processing_response import BatchTextProcessingResponse
from models.services.text_processing_request import TextProcessingRequest
from models.services.text_processing_response import TextProcessingResponse


@runtime_checkable
class CamelToolsClient(Protocol):
    """Protocol for camel-tools-api service integration"""

    async def process_text(
        self, request: TextProcessingRequest
    ) -> TextProcessingResponse:
        """
        Process single text through camel-tools-api

        Args:
            request: Text processing request with configuration

        Returns:
            TextProcessingResponse containing processed text and analysis

        Raises:
            RuntimeError: If API call fails
        """
        ...

    async def process_batch(
        self, request: BatchTextProcessingRequest
    ) -> BatchTextProcessingResponse:
        """
        Process multiple texts through camel-tools-api in batch

        Args:
            request: Batch text processing request with configuration

        Returns:
            BatchTextProcessingResponse containing all processing results

        Raises:
            RuntimeError: If API call fails
        """
        ...

    async def health_check(self) -> bool:
        """
        Check if camel-tools-api service is available

        Returns:
            True if service is healthy, False otherwise
        """
        ...
