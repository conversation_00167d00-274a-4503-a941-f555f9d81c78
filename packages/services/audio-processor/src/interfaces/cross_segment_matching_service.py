"""
Cross-segment matching service interface

Defines the protocol for cross-segment matching operations,
providing standardized chapter marker detection capabilities.
"""

from typing import Any, Optional, Protocol, runtime_checkable

from models.api.process_report_entry import ProcessReportEntry
from models.domain.transcription_segment import TranscriptionSegment
from models.services.match_result import MatchR<PERSON>ult


@runtime_checkable
class CrossSegmentMatchingService(Protocol):
    """
    Protocol for cross-segment matching operations

    Provides public interface for chapter marker detection using unified
    sequential matching algorithm with proper encapsulation.
    """

    async def find_marker_matches(
        self,
        segments: list[TranscriptionSegment],
        marker: str,
        marker_stems: list[str],
        text_to_stems: dict[str, list[str]],
        report: list[ProcessReportEntry],
        existing_chapters: list[dict[str, Any]],
    ) -> Optional[MatchResult]:
        """
        Find chapter marker matches using unified sequential matching algorithm

        This is the public interface for chapter marker detection. It handles
        duplicate checking, logging, and delegates to the internal matching algorithm.

        Args:
            segments: List of transcription segments
            marker: Chapter marker text
            marker_stems: Preprocessed stems for the marker
            text_to_stems: Cache of text to stems mapping
            report: Report list to append logs to
            existing_chapters: List of already found chapters for duplicate checking

        Returns:
            MatchResult if found and not duplicate, None otherwise
        """
        ...
