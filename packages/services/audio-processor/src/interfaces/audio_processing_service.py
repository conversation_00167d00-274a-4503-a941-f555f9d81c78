"""
Audio processing service interface

Defines the protocol for the main orchestration service for complete audio processing workflow,
providing standardized audio processing capabilities.
"""

from typing import Protocol, runtime_checkable

from models.services.audio_processing_request import AudioProcessingRequest
from models.services.audio_processing_response import AudioProcessingResponse


@runtime_checkable
class AudioProcessingService(Protocol):
    """Main orchestration service for complete audio processing workflow"""

    async def process_audio(
        self, request: AudioProcessingRequest
    ) -> AudioProcessingResponse:
        """
        Process audio file through complete workflow: transcription, chapter detection, and splitting

        Args:
            request: Audio processing request containing file path and configuration

        Returns:
            AudioProcessingResponse containing all processing results

        Raises:
            RuntimeError: If processing fails
        """
        ...

    async def initialize(self) -> None:
        """
        Initialize all underlying services

        Raises:
            RuntimeError: If initialization fails
        """
        ...

    async def cleanup(self) -> None:
        """
        Cleanup all underlying services and resources
        """
        ...
