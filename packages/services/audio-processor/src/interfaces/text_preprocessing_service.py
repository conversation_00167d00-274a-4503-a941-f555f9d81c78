"""
Text preprocessing service interface

Defines the protocol for text preprocessing operations using camel-tools-api,
providing standardized text processing capabilities for chapter detection.
"""

from typing import Protocol, runtime_checkable

from models.api.process_report_entry import ProcessReportEntry
from models.domain.transcription_segment import TranscriptionSegment


@runtime_checkable
class TextPreprocessingService(Protocol):
    """Protocol for text preprocessing operations using camel-tools-api"""

    async def batch_extract_stems(
        self, texts: list[str], report: list[ProcessReportEntry]
    ) -> dict[str, list[str]]:
        """
        Batch extract word stems from multiple texts

        Args:
            texts: List of texts to process
            report: Report list to append logs to

        Returns:
            Dictionary mapping text to list of word stems
        """
        ...

    async def extract_stems(
        self, text: str, report: list[ProcessReportEntry]
    ) -> list[str]:
        """
        Extract word stems from a single text

        Args:
            text: Text to process
            report: Report list to append logs to

        Returns:
            List of word stems
        """
        ...

    async def collect_and_process_texts(
        self,
        segments: list[TranscriptionSegment],
        chapter_markers: list[str],
        report: list[ProcessReportEntry],
    ) -> dict[str, list[str]]:
        """
        Collect all texts and process them to extract stems

        Args:
            segments: List of transcription segments
            chapter_markers: List of chapter markers
            report: Report list to append logs to

        Returns:
            Dictionary mapping text to list of word stems
        """
        ...
