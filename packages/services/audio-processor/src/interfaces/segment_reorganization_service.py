"""
Segment reorganization service interface

Defines the protocol for segment reorganization operations,
providing capabilities to restructure transcription segments for chapter markers.
"""

from typing import Any, Optional, Protocol, runtime_checkable

from models.domain.transcription_segment import TranscriptionSegment


@runtime_checkable
class SegmentReorganizationService(Protocol):
    """Protocol for segment reorganization operations"""

    def reorganize_segments_for_chapter(
        self,
        segments: list[TranscriptionSegment],
        chapter_info: dict[str, Any],
        marker_text: str,
    ) -> list[TranscriptionSegment]:
        """
        Reorganize segments to create a consolidated segment for the chapter marker

        Args:
            segments: Original list of transcription segments
            chapter_info: Chapter information including matched_segment_indices
            marker_text: The chapter marker text

        Returns:
            Reorganized list of segments with chapter marker in its own segment
        """
        ...

    def get_segment_remainder_before_chapter(
        self, segment: TranscriptionSegment, chapter_start_time: float
    ) -> Optional[TranscriptionSegment]:
        """
        Get the portion of a segment that comes before a chapter marker

        Args:
            segment: Original transcription segment
            chapter_start_time: Start time of the chapter marker

        Returns:
            Segment containing the portion before the chapter, or None if empty
        """
        ...

    def get_segment_remainder_after_chapter(
        self, segment: TranscriptionSegment, chapter_end_time: float
    ) -> Optional[TranscriptionSegment]:
        """
        Get the portion of a segment that comes after a chapter marker

        Args:
            segment: Original transcription segment
            chapter_end_time: End time of the chapter marker

        Returns:
            Segment containing the portion after the chapter, or None if empty
        """
        ...
