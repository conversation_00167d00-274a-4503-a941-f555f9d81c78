"""
Audio transcription service interface

Defines the protocol for audio transcription operations using WhisperX,
providing standardized transcription capabilities.
"""

from typing import Protocol, runtime_checkable

from models.api.model_info import ModelInfo
from models.config.transcription_config import TranscriptionConfig
from models.services.transcription_result import TranscriptionResult


@runtime_checkable
class AudioTranscriptionService(Protocol):
    """Protocol for audio transcription operations using WhisperX"""

    async def transcribe_audio(
        self, config: TranscriptionConfig
    ) -> TranscriptionResult:
        """
        Execute audio transcription with specified configuration

        Args:
            config: Transcription configuration containing audio file path and parameters

        Returns:
            TranscriptionResult containing segments and metadata

        Raises:
            RuntimeError: If transcription fails
        """
        ...

    def get_model_info(self) -> ModelInfo:
        """
        Get information about loaded models and service capabilities

        Returns:
            ModelInfo containing model status and configuration
        """
        ...

    async def initialize(self) -> None:
        """
        Initialize the transcription service and load models

        Raises:
            RuntimeError: If initialization fails
        """
        ...

    async def cleanup(self) -> None:
        """
        Cleanup resources and unload models
        """
        ...
