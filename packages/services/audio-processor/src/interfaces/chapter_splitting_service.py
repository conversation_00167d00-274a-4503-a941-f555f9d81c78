"""
Chapter splitting service interface

Defines the protocol for splitting audio into chapters,
providing standardized audio splitting capabilities.
"""

from typing import Any, Protocol, runtime_checkable

from models.domain.transcription_segment import TranscriptionSegment
from models.services.chapter_split_result import ChapterSplitResult


@runtime_checkable
class ChapterSplittingService(Protocol):
    """Protocol for splitting audio into chapters"""

    def split_chapters(
        self,
        audio_file_path: str,
        segments: list[TranscriptionSegment],
        raw_chapters: list[dict[str, Any]],
        audio_duration: float,
    ) -> ChapterSplitResult:
        """
        Split audio file into individual chapter files

        Args:
            audio_file_path: Path to the original audio file
            segments: List of transcription segments
            raw_chapters: List of detected chapter information
            audio_duration: Total duration of the audio file

        Returns:
            ChapterSplitResult containing chapter data and split report
        """
        ...
