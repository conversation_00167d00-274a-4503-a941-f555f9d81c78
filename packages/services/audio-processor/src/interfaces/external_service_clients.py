"""
External service clients interface

Defines the protocol for managing all external service clients,
providing centralized access to external service integrations.
"""

from typing import Protocol, runtime_checkable

from .camel_tools_client import CamelToolsClient
from .qwen_service_client import QwenServiceClient


@runtime_checkable
class ExternalServiceClients(Protocol):
    """Protocol for managing all external service clients"""

    @property
    def camel_tools(self) -> CamelToolsClient:
        """Get camel-tools-api client instance"""
        ...

    @property
    def qwen_service(self) -> QwenServiceClient:
        """Get qwen-service client instance"""
        ...

    async def health_check_all(self) -> dict[str, bool]:
        """
        Check health of all external services

        Returns:
            Dictionary mapping service names to health status
        """
        ...

    async def initialize(self) -> None:
        """
        Initialize all external service clients

        Raises:
            RuntimeError: If initialization fails
        """
        ...

    async def cleanup(self) -> None:
        """
        Cleanup all external service clients
        """
        ...
