"""
Health check related API routes
"""

from api.dependencies import get_transcription_service
from core.config import get_settings
from fastapi import APIRouter

from models import HealthStatus


router = APIRouter()


@router.get("/")
async def root():
    """Root path health check endpoint"""
    return {
        "service": "WhisperX Audio Processing Service",
        "status": "running",
        "version": "1.0.0",
    }


@router.get("/health", response_model=HealthStatus)
async def health_check():
    """Detailed health check"""
    try:
        # Check if transcription service is initialized
        get_transcription_service()
        model_loaded = True
    except Exception:
        model_loaded = False

    return HealthStatus(
        status="healthy" if model_loaded else "unhealthy",
        model_loaded=model_loaded,
        model_path=get_settings().MODEL_PATH,
    )
