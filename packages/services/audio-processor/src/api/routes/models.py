"""
Model information related API routes
"""

from api.dependencies import get_transcription_service
from fastapi import APIRouter, Depends
from interfaces import AudioTranscriptionService

from models.api.model_info import ModelInfo


router = APIRouter()


@router.get("/info", response_model=ModelInfo)
async def get_model_info(
    transcription_service: AudioTranscriptionService = Depends(
        get_transcription_service
    ),
):
    """Get model information"""
    return transcription_service.get_model_info()
