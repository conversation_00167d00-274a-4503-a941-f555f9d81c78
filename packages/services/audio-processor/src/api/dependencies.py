"""
API dependencies for audio processing service

Provides dependency injection for API routes using the clean architecture pattern
with proper service resolution through the DI container.
"""

from typing import Optional

from infrastructure import DIContainer
from interfaces import AudioProcessingService


# Global DI container instance
_di_container: Optional[DIContainer] = None


def set_di_container(container: Optional[DIContainer]) -> None:
    """
    Set the global DI container instance

    Args:
        container: DI container instance or None to clear
    """
    global _di_container
    _di_container = container


def get_di_container() -> DIContainer:
    """
    Get the global DI container instance

    Returns:
        DI container instance

    Raises:
        RuntimeError: If DI container is not initialized
    """
    if _di_container is None:
        error_msg = "DI container not initialized"
        raise RuntimeError(error_msg)
    return _di_container


def get_audio_processing_service() -> AudioProcessingService:
    """
    Dependency injection function for audio processing service

    Returns:
        AudioProcessingService instance

    Raises:
        RuntimeError: If service cannot be resolved
    """
    container = get_di_container()
    return container.get_service(AudioProcessingService)


def get_transcription_service():
    """
    Dependency injection function for audio transcription service

    Returns:
        AudioTranscriptionService instance

    Raises:
        RuntimeError: If service cannot be resolved
    """
    from interfaces import AudioTranscriptionService

    container = get_di_container()
    return container.get_service(AudioTranscriptionService)


def get_chapter_detection_service():
    """
    Dependency injection function for chapter detection service

    Returns:
        ChapterDetectionService instance

    Raises:
        RuntimeError: If service cannot be resolved
    """
    from interfaces import ChapterDetectionService

    container = get_di_container()
    return container.get_service(ChapterDetectionService)


def get_chapter_splitting_service():
    """
    Dependency injection function for chapter splitting service

    Returns:
        ChapterSplittingService instance

    Raises:
        RuntimeError: If service cannot be resolved
    """
    from interfaces import ChapterSplittingService

    container = get_di_container()
    return container.get_service(ChapterSplittingService)
