[tool.poetry]
name = "camel-tools-api"
version = "1.0.0"
description = "Camel-Tools 服务接口"
authors = ["Arabic Learning Platform Team <<EMAIL>>"]
readme = "README.md"
packages = [
    { include = "api", from = "src" },
    { include = "core", from = "src" },
    { include = "models", from = "src" },
    { include = "interfaces", from = "src" },
    { include = "services", from = "src" },
    { include = "infrastructure", from = "src" },
    { include = "adapters", from = "src" },
]

[tool.poetry.scripts]
camel-tools-api = "src.main:main"

[tool.poetry.dependencies]
python = ">=3.9,<3.13"
camel-tools = "^1.5.0"
pydantic = "^2.0.0"
pydantic-settings = "^2.0.0"
python-dotenv = "^1.0.0"
loguru = "^0.7.0"
fastapi = "^0.104.0"
uvicorn = { extras = ["standard"], version = "^0.24.0" }
redis = "^5.0.0"
numpy = "^1.24.0"

[tool.poetry.group.dev.dependencies]
ruff = "^0.1.0"
mypy = "^1.5.0"
httpx = "^0.25.0"
# Removed: black, isort, flake8 (replaced by ruff)
# Removed: all test-related dependencies (pytest, pytest-asyncio, pytest-cov, pytest-mock, fakeredis, pytest-xdist)

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# Removed [tool.black] and [tool.isort] - replaced by Ruff

[tool.mypy]
python_version = "3.9"
mypy_path = ["src"]                # Tell MyPy that src is the source root directory
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

# Ignore untyped third-party libraries
[[tool.mypy.overrides]]
module = ["camel_tools.*"]
ignore_missing_imports = true
ignore_errors = true

# Removed: [tool.pytest.ini_options] section - no longer needed as test infrastructure has been removed

# ===== RUFF CONFIGURATION (Replaces Black + Flake8 + isort) =====
[tool.ruff]
line-length = 88
target-version = "py39"

# Exclude directories
exclude = [
    ".eggs",
    ".git",
    ".hg",
    ".mypy_cache",
    ".tox",
    ".venv",
    "build",
    "dist",
    "__pycache__",
]

# Ruff linting configuration
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # Pyflakes
    "I",   # isort (import sorting)
    "N",   # pep8-naming
    "UP",  # pyupgrade
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "PIE", # flake8-pie
    # Removed: "PT" (flake8-pytest-style) - no longer needed as test infrastructure has been removed
    "RET", # flake8-return
    "SIM", # flake8-simplify
    "ARG", # flake8-unused-arguments
    "PTH", # flake8-use-pathlib
    "PL",  # Pylint rules
    "RUF", # Ruff-specific rules
]

# Ignore specific rules for compatibility
ignore = [
    "E203",    # Whitespace before ':' (Black compatibility)
    "PLR0913", # Too many arguments (sometimes necessary)
    "PLR0912", # Too many branches (sometimes necessary)
    "PLR0915", # Too many statements (sometimes necessary)
    "PLW0603", # Global statement usage (used for dependency injection pattern)
    "RUF001",  # String contains ambiguous characters (needed for Arabic text)
    "B008",    # Do not perform function calls in argument defaults (FastAPI Depends pattern)
]

# Per-file ignores for specific patterns
[tool.ruff.lint.per-file-ignores]
"src/infrastructure/factory.py" = [
    "F401", # Allow unused imports for dependency injection factory
]
"src/infrastructure/container.py" = [
    "F401", # Allow unused imports for DI container
]
"src/*/__init__.py" = [
    "F401", # Allow unused imports for module re-exports
]

[tool.ruff.lint.isort]
# isort configuration (replaces [tool.isort])
known-first-party = ["src"]
lines-after-imports = 2

[tool.ruff.format]
# Black-compatible formatting
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
docstring-code-format = true
