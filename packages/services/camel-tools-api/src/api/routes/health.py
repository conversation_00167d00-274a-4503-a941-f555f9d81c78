"""
Health check API routes using clean architecture
"""

import time

from fastapi import APIRouter, Depends

from api.dependencies import get_processing_service
from interfaces.processing import ArabicTextProcessingService
from models.schemas import HealthStatus, ServiceInfo


router = APIRouter()

# Service start time for uptime calculation
_start_time = time.time()


@router.get("/health", response_model=HealthStatus)
async def health_check(
    service: ArabicTextProcessingService = Depends(get_processing_service),
) -> HealthStatus:
    """
    Health check endpoint using clean architecture

    Returns:
        HealthStatus: Current service health status
    """

    uptime = time.time() - _start_time

    try:
        # Get service info to check health
        info = service.get_service_info()
        is_healthy = info.get("camel_tools_initialized", False)

        return HealthStatus(
            status="healthy" if is_healthy else "degraded",
            camel_tools_loaded=is_healthy,
            uptime=uptime,
            memory_usage=None,  # Could add memory usage info here
        )
    except Exception:
        return HealthStatus(
            status="unhealthy",
            camel_tools_loaded=False,
            uptime=uptime,
            memory_usage=None,
        )


@router.get("/info", response_model=ServiceInfo)
async def service_info(
    service: ArabicTextProcessingService = Depends(get_processing_service),
) -> ServiceInfo:
    """
    Get service information using clean architecture

    Returns:
        ServiceInfo: Service information and capabilities
    """

    info = service.get_service_info()

    return ServiceInfo(
        service_name=info["service_name"],
        version=info["version"],
        camel_tools_available=info.get("camel_tools_available", False),
        camel_tools_initialized=info.get("camel_tools_initialized", False),
        initialization_error=info.get("initialization_error"),
        supported_languages=info["supported_languages"],
        supported_features=info["supported_features"],
    )
