"""
Text processing API routes using clean architecture

Refactored to use dependency injection and clean separation of concerns
while maintaining backward compatibility with existing API contracts.
"""

import time

from fastapi import <PERSON><PERSON><PERSON>er, Depends, HTTPException

from adapters.api_adapters import (
    BatchProcessingRequestAdapter,
    BatchProcessingResponseAdapter,
    ProcessingRequestAdapter,
    ProcessingResponseAdapter,
)
from api.dependencies import get_processing_service
from core.config import get_settings
from interfaces.processing import ArabicTextProcessingService
from models.schemas import (
    BatchProcessingRequest,
    BatchProcessingResponse,
    TextProcessingRequest,
    TextProcessingResponse,
)


router = APIRouter()


@router.post("/process", response_model=TextProcessingResponse)
async def process_text(
    request: TextProcessingRequest,
    service: ArabicTextProcessingService = Depends(get_processing_service),
) -> TextProcessingResponse:
    """
    Process Arabic text using the new clean architecture

    Args:
        request: Text processing request
        service: Arabic text processing service

    Returns:
        TextProcessingResponse: Processing results
    """
    start_time = time.time()

    try:
        # Validate text length
        settings = get_settings()
        if len(request.text) > settings.MAX_TEXT_LENGTH:
            raise HTTPException(
                status_code=413,
                detail=f"Text too long. Maximum length: {settings.MAX_TEXT_LENGTH}",
            )

        # Convert API request to internal request
        internal_request = ProcessingRequestAdapter.from_api_request(request)

        # Process text using the service
        result = await service.process_text(internal_request)

        # Convert internal result to API response
        return ProcessingResponseAdapter.to_api_response(
            result, request.enable_normalization
        )

    except HTTPException:
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        return TextProcessingResponse(
            success=False,
            message=f"Processing failed: {e!s}",
            original_text=request.text,
            normalized_text=None,
            word_count=0,
            processing_time=processing_time,
            words=None,
        )


@router.post("/process/batch", response_model=BatchProcessingResponse)
async def process_batch(
    request: BatchProcessingRequest,
    service: ArabicTextProcessingService = Depends(get_processing_service),
) -> BatchProcessingResponse:
    """
    Process multiple Arabic texts in batch using the new clean architecture

    Args:
        request: Batch processing request
        service: Arabic text processing service

    Returns:
        BatchProcessingResponse: Batch processing results
    """
    start_time = time.time()

    try:
        # Validate batch size
        settings = get_settings()
        if len(request.texts) > settings.MAX_BATCH_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"Batch too large. Maximum size: {settings.MAX_BATCH_SIZE}",
            )

        # Convert API request to internal requests
        internal_requests = BatchProcessingRequestAdapter.from_api_request(request)

        # Process all texts using the service's batch processing
        results = await service.process_batch(internal_requests)

        # Calculate total processing time
        total_processing_time = time.time() - start_time

        # Convert internal results to API response
        return BatchProcessingResponseAdapter.to_api_response(
            results, total_processing_time, request.enable_normalization
        )

    except HTTPException:
        raise
    except Exception as e:
        total_processing_time = time.time() - start_time
        return BatchProcessingResponse(
            success=False,
            message=f"Batch processing failed: {e!s}",
            total_texts=len(request.texts),
            successful_texts=0,
            failed_texts=len(request.texts),
            total_processing_time=total_processing_time,
            results=[],
        )
