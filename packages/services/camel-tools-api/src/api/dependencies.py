"""
API dependencies using dependency injection container

Provides clean dependency injection for API routes using the new architecture.
"""

from typing import Optional

from fastapi import HTTPException

from infrastructure.container import DIContainer
from interfaces.processing import ArabicTextProcessingService


# Global DI container instance
_di_container: Optional[DIContainer] = None


def set_di_container(container: Optional[DIContainer]) -> None:
    """Set global DI container instance"""
    global _di_container
    _di_container = container


def get_di_container() -> DIContainer:
    """Get DI container instance dependency"""
    if _di_container is None:
        raise HTTPException(status_code=500, detail="DI container not initialized")
    return _di_container


def get_processing_service() -> ArabicTextProcessingService:
    """
    Get Arabic text processing service dependency

    Returns:
        Arabic text processing service instance
    """
    container = get_di_container()
    try:
        return container.get_service(ArabicTextProcessingService)
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get processing service: {e!s}"
        ) from e
