"""
API module initialization
Contains all API routes registration and configuration
"""

# Filename to Module Name Mapping
# __init__.py -> api
# health.py -> api.health
# processing.py -> api.processing

from fastapi import APIRouter

from .routes import cache, health, processing


# Filename to Module Name Mapping
# `.` is used to import from parent directory
# `.routes` is the directory name(which is a ptyhon package because it has __init__.py)
# `health` is the file name `health.py` which is inside  the `routes` directory
# `processing` is the file name `processing.py` which is inside  the `routes` directory
# `__init__.py` is a special file that makes a directory a python package

# Finally, routes is a package named `routes` which is inside the `api` directory
# health and processing are the modules inside the `routes` package

# In this way, <PERSON> converted the FILENAME BASE NAME to PACKAGE OR MODULE NAME

# Create main API router
api_router = APIRouter()

# Register route modules
# `router` is the variable comes from inside each Python file
# register the router to the main router
api_router.include_router(health.router, tags=["Health"])
api_router.include_router(processing.router, tags=["Text Processing"])
api_router.include_router(cache.router, tags=["Cache Management"])
