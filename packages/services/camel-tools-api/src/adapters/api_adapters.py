"""
API adapter implementations

Converts between API request/response models and internal processing models
to maintain backward compatibility while using clean internal interfaces.
"""

from interfaces.processing import (
    MorphologyOptions,
    NormalizationOptions,
    ProcessingOptions,
    ProcessingRequest,
    ProcessingResult,
)
from models.schemas import (
    BatchProcessingRequest,
    BatchProcessingResponse,
    TextProcessingRequest,
    TextProcessingResponse,
    WordInfo,
)


class ProcessingRequestAdapter:
    """Adapter for converting API requests to internal processing requests"""

    @staticmethod
    def from_api_request(api_request: TextProcessingRequest) -> ProcessingRequest:
        """
        Convert API request to internal processing request

        Args:
            api_request: API request model

        Returns:
            Internal processing request
        """
        # Create normalization options (enhanced operations are now mandatory)
        normalization_options = NormalizationOptions(
            remove_diacritics=True,  # Always enabled for Arabic processing
            # Note: remove_punctuation, normalize_digits, normalize_whitespace,
            # and normalize_english_case are now mandatory and always enabled
        )

        # Create morphology options
        morphology_options = MorphologyOptions(
            enable_stemming=api_request.enable_stemming,
            enable_root_extraction=api_request.enable_root_extraction,
        )

        # Create combined processing options
        processing_options = ProcessingOptions(
            normalization=normalization_options
            if api_request.enable_normalization
            else None,
            morphology=morphology_options,
            enable_caching=True,  # Always enable caching
        )

        return ProcessingRequest(
            text=api_request.text,
            options=processing_options,
        )


class ProcessingResponseAdapter:
    """Adapter for converting internal processing results to API responses"""

    @staticmethod
    def to_api_response(
        result: ProcessingResult, enable_normalization: bool = True
    ) -> TextProcessingResponse:
        """
        Convert internal processing result to API response

        Args:
            result: Internal processing result
            enable_normalization: Whether normalization was enabled

        Returns:
            API response model
        """
        # Convert morphology results to WordInfo objects
        words = []
        if result.morphology_results:
            for morph_result in result.morphology_results:
                words.append(
                    WordInfo(
                        word=morph_result.word,
                        stem=morph_result.stem,
                        root=morph_result.root,
                    )
                )
        elif result.tokens:
            # If no morphology results but we have tokens, create basic WordInfo
            for token in result.tokens:
                words.append(WordInfo(word=token, stem=None, root=None))

        return TextProcessingResponse(
            success=result.success,
            message=result.message,
            original_text=result.original_text,
            normalized_text=result.normalized_text if enable_normalization else None,
            word_count=len(result.tokens) if result.tokens else 0,
            processing_time=result.processing_time,
            words=words if words else None,
        )


class BatchProcessingRequestAdapter:
    """Adapter for converting batch API requests to internal processing requests"""

    @staticmethod
    def from_api_request(
        api_request: BatchProcessingRequest,
    ) -> list[ProcessingRequest]:
        """
        Convert batch API request to list of internal processing requests

        Args:
            api_request: Batch API request model

        Returns:
            List of internal processing requests
        """
        requests = []

        for text in api_request.texts:
            # Create individual TextProcessingRequest for each text
            individual_request = TextProcessingRequest(
                text=text,
                enable_normalization=api_request.enable_normalization,
                enable_stemming=api_request.enable_stemming,
                enable_root_extraction=api_request.enable_root_extraction,
            )

            # Convert to internal request
            internal_request = ProcessingRequestAdapter.from_api_request(
                individual_request
            )
            requests.append(internal_request)

        return requests


class BatchProcessingResponseAdapter:
    """Adapter for converting internal batch results to API batch response"""

    @staticmethod
    def to_api_response(
        results: list[ProcessingResult],
        total_processing_time: float,
        enable_normalization: bool = True,
    ) -> BatchProcessingResponse:
        """
        Convert internal batch results to API batch response

        Args:
            results: List of internal processing results
            total_processing_time: Total time for batch processing
            enable_normalization: Whether normalization was enabled

        Returns:
            API batch response model
        """
        # Convert individual results
        api_results = []
        successful_count = 0
        failed_count = 0

        for result in results:
            api_result = ProcessingResponseAdapter.to_api_response(
                result, enable_normalization
            )
            api_results.append(api_result)

            if result.success:
                successful_count += 1
            else:
                failed_count += 1

        return BatchProcessingResponse(
            success=failed_count == 0,
            message=f"Processed {successful_count}/{len(results)} texts successfully",
            total_texts=len(results),
            successful_texts=successful_count,
            failed_texts=failed_count,
            total_processing_time=total_processing_time,
            results=api_results,
        )
