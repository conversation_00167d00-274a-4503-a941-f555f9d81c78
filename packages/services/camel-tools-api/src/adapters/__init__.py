"""
Adapter classes for converting between API models and internal models

These adapters maintain backward compatibility while allowing the internal
architecture to use clean, well-defined interfaces.
"""

from .api_adapters import (
    ProcessingRequestAdapter,
    ProcessingResponseAdapter,
    BatchProcessingRequestAdapter,
    BatchProcessingResponseAdapter,
)

__all__ = [
    "ProcessingRequestAdapter",
    "ProcessingResponseAdapter",
    "BatchProcessingRequestAdapter",
    "BatchProcessingResponseAdapter",
]
