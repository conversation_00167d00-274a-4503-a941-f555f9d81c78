#!/usr/bin/env python3
"""
Camel-Tools API main entry point
Provides Arabic text processing, morphological analysis, and chapter detection

Code Organization:
1. Import Dependencies
2. Global Configuration
3. Lifecycle Management
4. Application Creation & Configuration
5. Main Entry Point

Execution Timeline:
1. import statements → 2. load settings → 3. define lifespan function
4. create FastAPI app → 5. add middleware → 6. register routes
7. launch uvicorn server → 8. execute lifespan startup process
9. service ready, handle requests
"""

# =============================================================================
# 1. Import Dependencies
# =============================================================================
import sys
from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager
from typing import Optional

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger

from api import (
    api_router,  # Main router containing all API endpoints
)
from api.dependencies import set_di_container
from core.config import get_settings
from infrastructure.container import DIContainer
from infrastructure.factory import ServiceFactoryImpl


# =============================================================================
# 2. Global Configuration
# =============================================================================
settings = get_settings()


# =============================================================================
# 3. Lifecycle Management
# =============================================================================
@asynccontextmanager
async def lifespan(_app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifecycle event handler using clean architecture

    Startup Phase (Before yield):
        - Initialize service factory and DI container
        - Set global DI container for API routes to use
    Runtime Phase (At yield):
        - Handle API requests
        - All endpoints are available
    Shutdown Phase (After yield):
        - Clean up resources and log shutdown status
    """
    # === Startup Phase ===
    container: Optional[DIContainer] = None
    try:
        logger.info("Initializing Camel-Tools Arabic Processing Service...")

        # Create service factory and DI container
        factory = ServiceFactoryImpl(settings)
        container = DIContainer()
        container.register_factory(factory)

        # Initialize services
        await container.initialize_services()

        # Set global DI container for API routes to use
        set_di_container(container)

        # Log initialization status
        if container.is_initialized():
            logger.success(
                "🚀 Camel-Tools Arabic Processing Service started successfully"
            )
            logger.info(f"Service running on http://{settings.HOST}:{settings.PORT}")
            logger.info(f"Registered services: {container.get_registered_services()}")
        else:
            logger.warning("⚠️ Service started in degraded mode")

    except Exception as e:
        logger.error(f"❌ Service startup failed: {e}")
        # In production, you might want to exit here
        # import sys
        # sys.exit(1)

    yield  # === Boundary: Service starts handling requests ===

    # === Shutdown Phase ===
    logger.info("Shutting down Camel-Tools Arabic Processing Service...")

    try:
        # Clear global DI container first
        set_di_container(None)
        logger.debug("Global DI container instance cleared")

        # Cleanup container resources
        if container is not None:
            await container.cleanup_services()
            logger.info("DI container resources cleared")

        logger.success("🛑 Camel-Tools Arabic Service shutdown complete")

    except Exception as shutdown_error:
        logger.error(f"Error during service shutdown: {shutdown_error}")
        # Continue with shutdown even if cleanup fails


# =============================================================================
# 4. Application Creation & Configuration
# =============================================================================

# Create FastAPI application instance
app = FastAPI(
    title="Camel-Tools Arabic Processing API",
    description="Comprehensive Arabic text processing service using Camel-Tools "
    "for morphological analysis, normalization, and chapter detection",
    version="1.0.0",
    lifespan=lifespan,
)

# Configure CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Should restrict to specific domains in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Register API routes
# api_router is defined in api/__init__.py and contains all API routes
app.include_router(api_router)


# =============================================================================
# 5. Main Entry Point
# =============================================================================
if __name__ == "__main__":
    # Configure logging system
    logger.remove()  # Remove default handler
    logger.add(
        sys.stderr,
        level=settings.LOG_LEVEL,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
        "<level>{message}</level>",
    )

    # Print startup information
    logger.info(f"Starting Camel-Tools API on {settings.HOST}:{settings.PORT}")
    logger.info(f"Debug mode: {settings.DEBUG}")
    logger.info(f"Log level: {settings.LOG_LEVEL}")

    # Launch FastAPI application
    # Execution steps:
    # 1. Find the app variable from main.py file
    # 2. Confirm app variable is a FastAPI application instance
    # 3. Run FastAPI application, handle HTTP requests and listen on port
    uvicorn.run(
        "main:app",  # main:app means import app variable from main module
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
    )
