"""
Data models and schemas for the Camel-Tools API
Simplified for core Arabic text processing functionality
"""

from enum import Enum
from typing import Any, Optional

from pydantic import BaseModel, Field


class SupportedLanguage(str, Enum):
    """Supported language enumeration - specifically for Arabic processing"""

    ARABIC = "ar"


# Request Models
class TextProcessingRequest(BaseModel):
    """
    Text processing request model
    """

    text: str = Field(
        description="Arabic text to process", min_length=1, max_length=1000000
    )

    language: SupportedLanguage = Field(
        default=SupportedLanguage.ARABIC,
        description="Text language (currently only Arabic supported)",
    )

    enable_normalization: bool = Field(
        default=True, description="Enable Arabic text normalization"
    )

    enable_stemming: bool = Field(default=True, description="Enable stemming")

    enable_root_extraction: bool = Field(
        default=True, description="Enable root extraction"
    )


class BatchProcessingRequest(BaseModel):
    """
    Batch text processing request model
    """

    texts: list[str] = Field(
        description="List of Arabic texts to process", min_length=1, max_length=100
    )

    enable_normalization: bool = Field(default=True)
    enable_stemming: bool = Field(default=True)
    enable_root_extraction: bool = Field(default=True)


# Response Models
class WordInfo(BaseModel):
    """
    Individual word information
    """

    word: str = Field(description="Original word")
    stem: Optional[str] = Field(None, description="Word stem")
    root: Optional[str] = Field(None, description="Word root")


class TextProcessingResponse(BaseModel):
    """
    Text processing response model
    """

    success: bool = Field(description="Whether processing was successful")
    message: str = Field(description="Response message")
    original_text: str = Field(description="Original input text")
    normalized_text: Optional[str] = Field(None, description="Normalized text")
    word_count: int = Field(description="Number of words processed")
    processing_time: float = Field(0.0, description="Processing time in seconds")
    words: Optional[list[WordInfo]] = Field(
        None, description="Individual word analysis results"
    )


class BatchProcessingResponse(BaseModel):
    """
    Batch processing response model
    """

    success: bool = Field(description="Whether batch processing was successful")
    message: str = Field(description="Response message")
    total_texts: int = Field(description="Total number of texts processed")
    successful_texts: int = Field(description="Number of successfully processed texts")
    failed_texts: int = Field(description="Number of failed texts")
    total_processing_time: float = Field(description="Total processing time in seconds")
    results: list[TextProcessingResponse] = Field(
        description="Individual processing results"
    )


class ServiceInfo(BaseModel):
    """Service information"""

    service_name: str = Field(description="Service name")
    version: str = Field(description="Service version")
    camel_tools_available: bool = Field(description="Whether Camel-Tools is available")
    camel_tools_initialized: bool = Field(
        description="Whether Camel-Tools is initialized"
    )
    initialization_error: Optional[str] = Field(
        None, description="Initialization error message"
    )
    supported_languages: list[str] = Field(description="Supported languages")
    supported_features: dict[str, bool] = Field(description="Supported features")


class HealthStatus(BaseModel):
    """Health status"""

    status: str = Field(description="Service status")
    camel_tools_loaded: bool = Field(description="Whether Camel-Tools is loaded")
    uptime: Optional[float] = Field(None, description="Service uptime in seconds")
    memory_usage: Optional[dict[str, Any]] = Field(
        None, description="Memory usage information"
    )
