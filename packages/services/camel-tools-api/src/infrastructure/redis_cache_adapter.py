"""
Redis cache adapter implementation

Implements both generic cache operations and Arabic text processing specific
cache operations using Redis as the backend storage system.
Follows clean architecture principles with proper dependency injection.
"""

import hashlib
import json
import pickle
from typing import Any, Optional, Union, cast

import redis
from loguru import logger

from core.config import Settings
from interfaces.caching import ArabicTextCacheManager, CacheManager


class RedisCacheAdapter(CacheManager, ArabicTextCacheManager):
    """
    Redis cache adapter for Arabic text processing

    Implements both generic cache operations and Arabic text processing specific
    operations using Redis as the backend. Follows clean architecture principles
    with proper dependency injection and separation of concerns.
    """

    def __init__(self, settings: Settings) -> None:
        """
        Initialize Redis cache adapter with dependency injection

        Args:
            settings: Application configuration settings injected by factory
        """
        self.settings = settings
        self.enabled = self.settings.ENABLE_REDIS_CACHING
        self.namespace = self.settings.REDIS_NAMESPACE
        self.ttl = self.settings.REDIS_TTL
        self.redis_client: Optional[redis.Redis] = None

        if self.enabled:
            self._initialize_redis()

    def _initialize_redis(self) -> None:
        """Initialize Redis connection with error handling"""
        try:
            # Create Redis connection pool
            pool = redis.ConnectionPool(
                host=self.settings.REDIS_HOST,
                port=self.settings.REDIS_PORT,
                db=self.settings.REDIS_DB,
                password=self.settings.REDIS_PASSWORD,
                max_connections=self.settings.REDIS_MAX_CONNECTIONS,
                socket_timeout=self.settings.REDIS_CONNECTION_TIMEOUT,
                socket_connect_timeout=self.settings.REDIS_CONNECTION_TIMEOUT,
                decode_responses=False,  # We'll handle encoding ourselves for pickle
            )

            self.redis_client = redis.Redis(connection_pool=pool)

            # Test connection
            self.redis_client.ping()
            logger.info("Redis cache adapter initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Redis cache adapter: {e}")
            self.enabled = False
            self.redis_client = None

    def _generate_cache_key(
        self, prefix: str, data: Union[str, list[str], dict]
    ) -> str:
        """
        Generate cache key with namespace and hash

        Args:
            prefix: Cache key prefix (e.g., 'word', 'tokens')
            data: Data to generate hash from

        Returns:
            Generated cache key with namespace
        """
        # Convert data to string for hashing
        if isinstance(data, (list, dict)):
            data_str = json.dumps(data, sort_keys=True, ensure_ascii=False)
        else:
            data_str = str(data)

        # Generate hash
        hash_obj = hashlib.md5(data_str.encode("utf-8"))
        data_hash = hash_obj.hexdigest()

        return f"{self.namespace}:{prefix}:{data_hash}"

    # =============================================================================
    # Generic CacheManager Protocol Implementation
    # =============================================================================

    def get(self, key: str) -> Optional[Any]:
        """
        Retrieve a value from cache (CacheManager protocol method)

        Args:
            key: Cache key

        Returns:
            Cached value or None if not found
        """
        return self.get_shared_data(key)

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        Store a value in cache (CacheManager protocol method)

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (optional)
        """
        # Call set_shared_data but ignore the return value to match protocol
        self.set_shared_data(key, value, ttl)

    def delete(self, key: str) -> None:
        """
        Delete a value from cache (CacheManager protocol method)

        Args:
            key: Cache key to delete
        """
        if not self.enabled or not self.redis_client:
            return

        try:
            shared_key = f"shared:{key}"
            self.redis_client.delete(shared_key)
            logger.debug(f"Deleted cache key: {key}")
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")

    def clear(self) -> None:
        """Clear all cached values (CacheManager protocol method)"""
        # Call clear_cache but ignore the return value to match protocol
        self.clear_cache()

    # =============================================================================
    # ArabicTextCacheManager Protocol Implementation
    # =============================================================================

    def get_word_analysis(
        self, word: str, processing_options: dict[str, Any]
    ) -> Optional[dict[str, str]]:
        """
        Get cached word analysis result

        Args:
            word: Arabic word to get analysis for
            processing_options: Processing options that affect the result

        Returns:
            Cached word analysis or None if not found
        """
        if not self.enabled or not self.redis_client:
            return None

        try:
            # Create cache key including processing options
            cache_data = {"word": word, "options": processing_options}
            cache_key = self._generate_cache_key("word", cache_data)

            # Get from cache
            cached_data = cast(Optional[bytes], self.redis_client.get(cache_key))
            if cached_data:
                result = cast(dict[str, str], pickle.loads(cached_data))
                logger.debug(f"Cache hit for word: {word}")
                return result

            logger.debug(f"Cache miss for word: {word}")
            return None

        except Exception as e:
            logger.error(f"Error getting word analysis from cache: {e}")
            return None

    def set_word_analysis(
        self, word: str, processing_options: dict[str, Any], analysis: dict[str, str]
    ) -> bool:
        """
        Cache word analysis result

        Args:
            word: Arabic word
            processing_options: Processing options that affect the result
            analysis: Word analysis result to cache

        Returns:
            True if successfully cached, False otherwise
        """
        if not self.enabled or not self.redis_client:
            return False

        try:
            # Create cache key including processing options
            cache_data = {"word": word, "options": processing_options}
            cache_key = self._generate_cache_key("word", cache_data)

            # Serialize and cache
            serialized_data = pickle.dumps(analysis)
            self.redis_client.setex(cache_key, self.ttl, serialized_data)

            logger.debug(f"Cached word analysis for: {word}")
            return True

        except Exception as e:
            logger.error(f"Error caching word analysis: {e}")
            return False

    def get_tokens_analysis(
        self, tokens: list[str], processing_options: dict[str, Any]
    ) -> Optional[list[dict[str, str]]]:
        """
        Get cached tokens analysis result

        Args:
            tokens: List of Arabic tokens
            processing_options: Processing options that affect the result

        Returns:
            Cached tokens analysis or None if not found
        """
        if not self.enabled or not self.redis_client:
            return None

        try:
            # Create cache key including processing options
            cache_data = {"tokens": tokens, "options": processing_options}
            cache_key = self._generate_cache_key("tokens", cache_data)

            # Get from cache
            cached_data = cast(Optional[bytes], self.redis_client.get(cache_key))
            if cached_data:
                result = cast(list[dict[str, str]], pickle.loads(cached_data))
                logger.debug(f"Cache hit for {len(tokens)} tokens")
                return result

            logger.debug(f"Cache miss for {len(tokens)} tokens")
            return None

        except Exception as e:
            logger.error(f"Error getting tokens analysis from cache: {e}")
            return None

    def set_tokens_analysis(
        self,
        tokens: list[str],
        processing_options: dict[str, Any],
        analysis: list[dict[str, str]],
    ) -> None:
        """
        Cache tokens analysis result

        Args:
            tokens: List of Arabic tokens
            processing_options: Processing options that affect the result
            analysis: Tokens analysis result to cache
        """
        if not self.enabled or not self.redis_client:
            return

        try:
            # Create cache key including processing options
            cache_data = {"tokens": tokens, "options": processing_options}
            cache_key = self._generate_cache_key("tokens", cache_data)

            # Serialize and cache
            serialized_data = pickle.dumps(analysis)
            self.redis_client.setex(cache_key, self.ttl, serialized_data)

            logger.debug(f"Cached tokens analysis for {len(tokens)} tokens")

        except Exception as e:
            logger.error(f"Error caching tokens analysis: {e}")

    # =============================================================================
    # Additional Cache Operations (Cross-service and Shared)
    # =============================================================================

    def clear_cache(self, pattern: Optional[str] = None) -> int:
        """
        Clear cache entries

        Args:
            pattern: Optional pattern to match keys (default: all namespace keys)

        Returns:
            Number of keys deleted
        """
        if not self.enabled or not self.redis_client:
            return 0

        try:
            if pattern is None:
                pattern = f"{self.namespace}:*"

            keys = cast(list[bytes], self.redis_client.keys(pattern))
            if keys:
                deleted = cast(int, self.redis_client.delete(*keys))
                logger.info(f"Cleared {deleted} cache entries")
                return deleted

            return 0

        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return 0

    def get_shared_data(self, key: str) -> Optional[Any]:
        """
        Get data from shared namespace for cross-service access

        Args:
            key: Cache key (without namespace prefix)

        Returns:
            Cached data or None if not found
        """
        if not self.enabled or not self.redis_client:
            return None

        try:
            shared_key = f"shared:{key}"
            cached_data = cast(Optional[bytes], self.redis_client.get(shared_key))
            if cached_data:
                result = pickle.loads(cached_data)
                logger.debug(f"Shared cache hit for key: {key}")
                return result

            logger.debug(f"Shared cache miss for key: {key}")
            return None

        except Exception as e:
            logger.error(f"Error getting shared data from cache: {e}")
            return None

    def set_shared_data(self, key: str, data: Any, ttl: Optional[int] = None) -> bool:
        """
        Set data in shared namespace for cross-service access

        Args:
            key: Cache key (without namespace prefix)
            data: Data to cache
            ttl: Time to live (uses default if None)

        Returns:
            True if successfully cached, False otherwise
        """
        if not self.enabled or not self.redis_client:
            return False

        try:
            shared_key = f"shared:{key}"
            serialized_data = pickle.dumps(data)
            cache_ttl = ttl or self.ttl

            self.redis_client.setex(shared_key, cache_ttl, serialized_data)
            logger.debug(f"Cached shared data for key: {key}")
            return True

        except Exception as e:
            logger.error(f"Error caching shared data: {e}")
            return False

    def get_cross_service_data(self, service: str, key: str) -> Optional[Any]:
        """
        Get data from another service's public cache (read-only access)

        Args:
            service: Service namespace (e.g., 'qwen', 'audio', 'backend')
            key: Cache key (without namespace prefix)

        Returns:
            Cached data or None if not found
        """
        if not self.enabled or not self.redis_client:
            return None

        try:
            cross_service_key = f"{service}:public:{key}"
            cached_data = cast(
                Optional[bytes], self.redis_client.get(cross_service_key)
            )
            if cached_data:
                result = pickle.loads(cached_data)
                logger.debug(f"Cross-service cache hit for {service}:{key}")
                return result

            logger.debug(f"Cross-service cache miss for {service}:{key}")
            return None

        except Exception as e:
            logger.error(f"Error getting cross-service data from cache: {e}")
            return None

    def get_cache_stats(self) -> dict[str, Any]:
        """
        Get cache statistics

        Returns:
            Dictionary with cache statistics
        """
        if not self.enabled or not self.redis_client:
            return {"enabled": False}

        try:
            info = cast(dict[str, Any], self.redis_client.info())
            keys_count = len(
                cast(list[bytes], self.redis_client.keys(f"{self.namespace}:*"))
            )
            shared_keys_count = len(
                cast(list[bytes], self.redis_client.keys("shared:*"))
            )

            return {
                "enabled": True,
                "connected": True,
                "namespace": self.namespace,
                "keys_count": keys_count,
                "shared_keys_count": shared_keys_count,
                "memory_used": info.get("used_memory_human", "N/A"),
                "total_connections": info.get("total_connections_received", 0),
            }

        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {"enabled": True, "connected": False, "error": str(e)}
