"""
Service factory implementation

Creates and configures service instances with proper dependency injection.

1. manages the lifecycle of all services instances.
2. ensures proper configuration and dependency injection.
3. implements singleton pattern for service instances.
4. provides a centralized way to resolve dependencies throughout the application.
"""

from typing import Optional

from loguru import logger

from core.config import Settings
from infrastructure.redis_cache_adapter import RedisCacheAdapter
from interfaces.processing import (
    ArabicTextProcessingService,
    BatchProcessor,
    MorphologicalAnalyzer,
    TextNormalizer,
    TextTokenizer,
)
from interfaces.services import ServiceFactory
from services.batch_processing import ConcurrentBatchProcessor
from services.morphology import CamelToolsMorphologyAnalyzer
from services.normalization import CamelToolsNormalizer
from services.text_processing import ArabicTextProcessingServiceImpl
from services.tokenization import CamelToolsTokenizer


class ServiceFactoryImpl(ServiceFactory):
    """
    Service factory implementation

    Creates and manages service instances with proper dependency injection
    following clean architecture principles. Implements singleton pattern
    for services that should be shared across the application.

    Architectural Decision: Only services that actually use configuration
    receive Settings injection (e.g., ConcurrentBatchProcessor). Services
    that don't need configuration use parameter-less constructors to follow
    the Interface Segregation Principle and avoid unnecessary coupling.
    """

    def __init__(self, config: Settings) -> None:
        """
        Initialize the service factory

        Args:
            config: Application configuration settings
        """
        self.config = config

        # Service instances (singletons)
        self._cache_manager: Optional[RedisCacheAdapter] = None
        self._normalizer: Optional[TextNormalizer] = None
        self._tokenizer: Optional[TextTokenizer] = None
        self._analyzer: Optional[MorphologicalAnalyzer] = None
        self._batch_processor: Optional[BatchProcessor] = None
        self._text_processing_service: Optional[ArabicTextProcessingService] = None

        # Lifecycle management
        self._initialized = False

    def create_text_processing_service(self) -> ArabicTextProcessingService:
        """
        Create the main Arabic text processing service

        Returns:
            Configured text processing service instance
        """
        if self._text_processing_service is None:
            logger.info("Creating Arabic text processing service")

            self._text_processing_service = ArabicTextProcessingServiceImpl(
                normalizer=self.get_normalizer(),
                tokenizer=self.get_tokenizer(),
                analyzer=self.get_analyzer(),
                batch_processor=self.get_batch_processor(),
                cache_manager=self.get_cache_manager(),
            )

            logger.success("Arabic text processing service created successfully")

        return self._text_processing_service

    def get_normalizer(self) -> TextNormalizer:
        """
        Get or create a text normalizer instance

        Returns:
            Text normalizer instance
        """
        if self._normalizer is None:
            logger.debug("Creating text normalizer")
            self._normalizer = CamelToolsNormalizer()

        return self._normalizer

    def get_tokenizer(self) -> TextTokenizer:
        """
        Get or create a text tokenizer instance

        Returns:
            Text tokenizer instance
        """
        if self._tokenizer is None:
            logger.debug("Creating text tokenizer")
            self._tokenizer = CamelToolsTokenizer()

        return self._tokenizer

    def get_analyzer(self) -> MorphologicalAnalyzer:
        """
        Get or create a morphological analyzer instance

        Returns:
            Morphological analyzer instance
        """
        if self._analyzer is None:
            logger.debug("Creating morphological analyzer")
            self._analyzer = CamelToolsMorphologyAnalyzer()

        return self._analyzer

    def get_batch_processor(self) -> BatchProcessor:
        """
        Get or create a batch processor instance

        Returns:
            Batch processor instance
        """
        if self._batch_processor is None:
            logger.debug("Creating batch processor with configuration")
            self._batch_processor = ConcurrentBatchProcessor(self.config)

        return self._batch_processor

    def get_cache_manager(self) -> RedisCacheAdapter:
        """
        Get or create a cache manager instance with proper dependency injection

        Returns:
            Redis cache adapter instance implementing both CacheManager
            and ArabicTextCacheManager protocols
        """
        if self._cache_manager is None:
            logger.debug("Creating Redis cache adapter with dependency injection")
            # Create cache adapter with proper dependency injection following
            # the same pattern as other services in the factory
            self._cache_manager = RedisCacheAdapter(self.config)

        return self._cache_manager

    async def initialize_services(self) -> None:
        """
        Initialize all services that require async initialization

        Raises:
            RuntimeError: If initialization fails
        """
        if self._initialized:
            logger.debug("Services already initialized, skipping")
            return

        try:
            logger.info("Initializing camel-tools-api service factory...")

            # Initialize morphological analyzer
            # The analyzer initializes Camel-Tools models in its constructor,
            # but we verify it's properly initialized here
            analyzer = self.get_analyzer()
            if hasattr(analyzer, "initialized") and not analyzer.initialized:
                logger.warning(
                    f"Morphological analyzer initialization failed: "
                    f"{getattr(analyzer, 'initialization_error', 'Unknown error')}"
                )
                # Continue with degraded functionality rather than failing completely

            # Initialize cache manager to ensure Redis connection is working
            self.get_cache_manager()
            logger.debug("Cache manager initialized")

            # Initialize batch processor with configuration
            self.get_batch_processor()
            logger.debug("Batch processor initialized")

            self._initialized = True
            logger.success("Camel-tools-api service factory initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize service factory: {e}")
            error_msg = "Service factory initialization failed"
            raise RuntimeError(error_msg) from e

    async def cleanup_services(self) -> None:
        """
        Cleanup all services and release resources
        """
        if not self._initialized:
            logger.debug("Services not initialized, skipping cleanup")
            return

        try:
            logger.info("Cleaning up camel-tools-api service factory...")

            # Cleanup morphological analyzer resources
            if self._analyzer and hasattr(self._analyzer, "mle"):
                try:
                    if self._analyzer.mle is not None:
                        self._analyzer.mle = None
                        logger.debug("MLE disambiguator cleared")
                except Exception as analyzer_error:
                    logger.warning(
                        f"Error cleaning up morphological analyzer: {analyzer_error}"
                    )

            # Cleanup cache manager connections
            if self._cache_manager:
                try:
                    # Cache manager cleanup is handled by the Redis connection pool
                    logger.debug("Cache manager cleanup completed")
                except Exception as cache_error:
                    logger.warning(f"Error cleaning up cache manager: {cache_error}")

            # Clear all service references
            self._text_processing_service = None
            self._analyzer = None
            self._normalizer = None
            self._tokenizer = None
            self._batch_processor = None
            self._cache_manager = None

            self._initialized = False
            logger.success("Camel-tools-api service factory cleanup completed")

        except Exception as e:
            logger.error(f"Error during service factory cleanup: {e}")
            # Continue with cleanup even if some operations fail
            self._initialized = False

    def is_initialized(self) -> bool:
        """
        Check if factory services are initialized

        Returns:
            True if initialized, False otherwise
        """
        return self._initialized
