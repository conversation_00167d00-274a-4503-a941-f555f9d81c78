"""
Dependency injection container implementation

Manages service lifecycle and provides dependency injection capabilities.
"""

from typing import Any, Optional, TypeVar, cast

from loguru import logger

from interfaces.processing import ArabicTextProcessingService
from interfaces.services import ServiceFactory
from services.text_processing import ArabicTextProcessingServiceImpl


T = TypeVar("T")


class DIContainer:
    """
    Dependency injection container

    Manages service instances and their lifecycle, providing a centralized
    way to resolve dependencies throughout the application.
    """

    def __init__(self) -> None:
        """Initialize the DI container"""
        self._services: dict[type, Any] = {}
        self._factory: Optional[ServiceFactory] = None
        self._initialized = False

    def register_factory(self, factory: ServiceFactory) -> None:
        """
        Register the service factory

        Args:
            factory: Service factory instance
        """
        self._factory = factory
        logger.debug("Service factory registered")

    def get_service(self, service_type: type[T]) -> T:
        """
        Get a service instance by type

        Args:
            service_type: Type of service to retrieve

        Returns:
            Service instance

        Raises:
            ValueError: If service factory is not registered
            RuntimeError: If service cannot be created
        """
        if self._factory is None:
            raise ValueError("Service factory not registered")

        # Check if service is already cached
        if service_type in self._services:
            return cast(T, self._services[service_type])

        # Create new service instance
        try:
            service = self._create_service(service_type)
            self._services[service_type] = service
            logger.debug(f"Created service: {service_type.__name__}")
            return service
        except Exception as e:
            logger.error(f"Failed to create service {service_type.__name__}: {e}")
            raise RuntimeError(f"Cannot create service {service_type.__name__}") from e

    def _create_service(self, service_type: type[T]) -> T:
        """
        Create a service instance using the factory

        Args:
            service_type: Type of service to create

        Returns:
            Created service instance

        Raises:
            ValueError: If service type is not supported
        """
        if self._factory is None:
            raise ValueError("Service factory not registered")

        if service_type == ArabicTextProcessingService:
            return cast(T, self._factory.create_text_processing_service())
        raise ValueError(f"Unsupported service type: {service_type}")

    async def initialize_services(self) -> None:
        """
        Initialize all services that require async initialization
        """
        if self._initialized:
            return

        if self._factory is None:
            raise ValueError("Service factory not registered")

        try:
            logger.info("Initializing DI container services...")

            # Initialize the factory's services
            if hasattr(self._factory, "initialize_services"):
                await self._factory.initialize_services()

            self._initialized = True
            logger.success("DI container services initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize DI container services: {e}")
            raise

    async def cleanup_services(self) -> None:
        """
        Cleanup all services and release resources
        """
        if not self._initialized:
            return

        try:
            logger.info("Cleaning up DI container services...")

            # Cleanup the factory's services
            if self._factory and hasattr(self._factory, "cleanup_services"):
                await self._factory.cleanup_services()

            # Clear service cache
            self._services.clear()
            self._initialized = False

            logger.success("DI container cleanup completed")

        except Exception as e:
            logger.error(f"Error during DI container cleanup: {e}")
            # Continue with cleanup even if some operations fail

    def is_initialized(self) -> bool:
        """
        Check if the container is initialized

        Returns:
            True if initialized, False otherwise
        """
        return self._initialized

    def get_registered_services(self) -> list[str]:
        """
        Get list of registered service types

        Returns:
            List of service type names
        """
        return [service_type.__name__ for service_type in self._services]
