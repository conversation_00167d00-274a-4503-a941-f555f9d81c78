"""
Service factory interface definitions

Defines the contract for creating and managing service instances.
Polymorphism: 同一个接口, 多个实现
Polymorphism has two types:
    - 协议: 结构化类型
    - 抽象基类: 名义话类型
"""

from typing import Protocol, runtime_checkable

from .caching import CacheManager
from .processing import (
    ArabicTextProcessingService,
    BatchProcessor,
    MorphologicalAnalyzer,
    TextNormalizer,
    TextTokenizer,
)


@runtime_checkable
class ServiceFactory(Protocol):
    """Protocol for service factory operations"""

    def create_text_processing_service(self) -> ArabicTextProcessingService:
        """
        Create the main Arabic text processing service

        Returns:
            Configured text processing service instance
        """
        ...

    def get_normalizer(self) -> TextNormalizer:
        """
        Get or create a text normalizer instance

        Returns:
            Text normalizer instance
        """
        ...

    def get_tokenizer(self) -> TextTokenizer:
        """
        Get or create a text tokenizer instance

        Returns:
            Text tokenizer instance
        """
        ...

    def get_analyzer(self) -> MorphologicalAnalyzer:
        """
        Get or create a morphological analyzer instance

        Returns:
            Morphological analyzer instance
        """
        ...

    def get_batch_processor(self) -> BatchProcessor:
        """
        Get or create a batch processor instance

        Returns:
            Batch processor instance
        """
        ...

    def get_cache_manager(self) -> CacheManager:
        """
        Get or create a cache manager instance

        Returns:
            Cache manager instance
        """
        ...

    async def initialize_services(self) -> None:
        """
        Initialize all services that require async initialization

        Raises:
            RuntimeError: If initialization fails
        """
        ...

    async def cleanup_services(self) -> None:
        """
        Cleanup all services and release resources
        """
        ...

    def is_initialized(self) -> bool:
        """
        Check if factory services are initialized

        Returns:
            True if initialized, False otherwise
        """
        ...
