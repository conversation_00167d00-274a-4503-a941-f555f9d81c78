"""
Core processing interfaces for Arabic text processing operations

These protocols define the contracts for different text processing operations,
enabling clean separation of concerns and dependency injection.
"""

from dataclasses import dataclass
from typing import Any, Callable, Optional, Protocol, runtime_checkable


@dataclass
class NormalizationOptions:
    """Configuration options for text normalization"""

    remove_diacritics: bool = True
    # The following normalization operations are now mandatory and always enabled:
    # - remove_punctuation: Always True
    # - normalize_digits: Always True
    # - normalize_whitespace: Always True
    # - normalize_english_case: Always True


@dataclass
class MorphologyOptions:
    """Configuration options for morphological analysis"""

    enable_stemming: bool = True
    enable_root_extraction: bool = True
    # Note: Diacritics removal from stems and roots is now mandatory and always enabled


@dataclass
class ProcessingOptions:
    """Combined processing options"""

    normalization: Optional[NormalizationOptions]
    morphology: MorphologyOptions
    enable_caching: bool = True


@dataclass
class MorphologyResult:
    """Result of morphological analysis for a single word"""

    word: str
    stem: Optional[str] = None
    root: Optional[str] = None


@dataclass
class ProcessingRequest:
    """Request for text processing operations"""

    text: str
    options: ProcessingOptions


@dataclass
class ProcessingResult:
    """Result of text processing operations"""

    success: bool
    message: str
    original_text: str
    normalized_text: Optional[str] = None
    tokens: Optional[list[str]] = None
    morphology_results: Optional[list[MorphologyResult]] = None
    processing_time: float = 0.0
    cache_hit: bool = False


@runtime_checkable
class TextNormalizer(Protocol):
    """Protocol for text normalization operations"""

    def normalize(self, text: str, options: NormalizationOptions) -> str:
        """
        Normalize Arabic text according to specified options

        Args:
            text: Input Arabic text
            options: Normalization configuration

        Returns:
            Normalized text
        """
        ...


@runtime_checkable
class TextTokenizer(Protocol):
    """Protocol for text tokenization operations"""

    def tokenize(self, text: str) -> list[str]:
        """
        Tokenize text into individual words

        Args:
            text: Input text to tokenize

        Returns:
            List of tokens
        """
        ...


@runtime_checkable
class MorphologicalAnalyzer(Protocol):
    """Protocol for morphological analysis operations"""

    async def analyze(
        self, tokens: list[str], options: MorphologyOptions
    ) -> list[MorphologyResult]:
        """
        Perform morphological analysis on tokens

        Args:
            tokens: List of tokens to analyze
            options: Analysis configuration

        Returns:
            List of morphological analysis results
        """
        ...


@runtime_checkable
class BatchProcessor(Protocol):
    """Protocol for batch processing operations"""

    async def process_batch(
        self, items: list[Any], processor_func: Callable[[Any], Any]
    ) -> list[Any]:
        """
        Process a batch of items concurrently

        Args:
            items: List of items to process
            processor_func: Function to apply to each item

        Returns:
            List of processing results
        """
        ...


@runtime_checkable
class ArabicTextProcessingService(Protocol):
    """Main service protocol for Arabic text processing"""

    async def process_text(self, request: ProcessingRequest) -> ProcessingResult:
        """
        Process a single text with specified options

        Args:
            request: Processing request with text and options

        Returns:
            Processing result
        """
        ...

    async def process_batch(
        self, requests: list[ProcessingRequest]
    ) -> list[ProcessingResult]:
        """
        Process multiple texts in batch

        Args:
            requests: List of processing requests

        Returns:
            List of processing results
        """
        ...

    def get_service_info(self) -> dict[str, Any]:
        """
        Get information about the service capabilities

        Returns:
            Service information dictionary
        """
        ...
