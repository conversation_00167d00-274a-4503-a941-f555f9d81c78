"""
Abstract interfaces for the Camel-Tools API service

This module defines the core abstractions that enable clean separation of concerns,
dependency injection, and improved testability throughout the service.
"""

from .processing import (
    TextNormalizer,
    TextTokenizer,
    MorphologicalAnalyzer,
    BatchProcessor,
    ArabicTextProcessingService,
)
from .caching import CacheManager
from .services import ServiceFactory

__all__ = [
    "TextNormalizer",
    "TextTokenizer",
    "MorphologicalAnalyzer",
    "BatchProcessor",
    "ArabicTextProcessingService",
    "CacheManager",
    "ServiceFactory",
]
