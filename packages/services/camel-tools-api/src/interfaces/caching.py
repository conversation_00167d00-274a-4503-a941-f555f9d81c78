"""
Caching interface definitions

Defines the contract for caching operations used throughout the service.
Follows clean architecture principles by separating generic cache operations
from business-specific Arabic text processing cache operations.
"""

from typing import Any, Optional, Protocol, runtime_checkable


@runtime_checkable
class CacheManager(Protocol):
    """
    Protocol for generic cache management operations

    Provides basic cache operations that can be implemented by any caching system.
    This interface follows the Interface Segregation Principle by only including
    fundamental cache operations.
    """

    def get(self, key: str) -> Optional[Any]:
        """
        Retrieve a value from cache

        Args:
            key: Cache key

        Returns:
            Cached value or None if not found
        """
        ...

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        Store a value in cache

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (optional)
        """
        ...

    def delete(self, key: str) -> None:
        """
        Delete a value from cache

        Args:
            key: Cache key to delete
        """
        ...

    def clear(self) -> None:
        """Clear all cached values"""
        ...


@runtime_checkable
class ArabicTextCacheManager(Protocol):
    """
    Protocol for Arabic text processing specific cache operations

    Extends the basic cache functionality with business-specific operations
    for Arabic morphological analysis and text processing results.
    """

    def get_word_analysis(
        self, word: str, processing_options: dict[str, Any]
    ) -> Optional[dict[str, str]]:
        """
        Get cached word analysis result

        Args:
            word: Arabic word to get analysis for
            processing_options: Processing options that affect the result

        Returns:
            Cached word analysis or None if not found
        """
        ...

    def set_word_analysis(
        self, word: str, processing_options: dict[str, Any], analysis: dict[str, str]
    ) -> bool:
        """
        Cache word analysis result

        Args:
            word: Arabic word
            processing_options: Processing options that affect the result
            analysis: Word analysis result to cache

        Returns:
            True if successfully cached, False otherwise
        """
        ...

    def get_tokens_analysis(
        self, tokens: list[str], processing_options: dict[str, Any]
    ) -> Optional[list[dict[str, str]]]:
        """
        Get cached morphological analysis results for tokens

        Args:
            tokens: List of tokens
            processing_options: Processing configuration

        Returns:
            Cached analysis results or None
        """
        ...

    def set_tokens_analysis(
        self,
        tokens: list[str],
        processing_options: dict[str, Any],
        results: list[dict[str, str]],
    ) -> None:
        """
        Cache morphological analysis results for tokens

        Args:
            tokens: List of tokens
            processing_options: Processing configuration
            results: Analysis results to cache
        """
        ...
