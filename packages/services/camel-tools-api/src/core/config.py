"""
Configuration management for Camel-Tools API
Following audio-processor service patterns for configuration management
"""

from functools import lru_cache
from pathlib import Path
from typing import Optional

from pydantic import field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings with environment variable support

    Uses Pydantic Settings for configuration management with validation,
    following the same patterns as audio-processor service.
    """

    class Config:
        # In Pydantic V2+, the env_file path is relative to the current working dir
        # We build an absolute path here to ensure it can always be found
        env_file = str(Path(__file__).resolve().parent.parent.parent / ".env")
        case_sensitive = True
        extra = "ignore"

    # Service configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8002
    DEBUG: bool = True

    # Camel-Tools configuration
    ENABLE_CACHING: bool = True
    CACHE_SIZE: int = 1000

    # Performance optimization configuration
    ENABLE_CONCURRENT_PROCESSING: bool = True
    MAX_CONCURRENT_WORKERS: int = 4  # Number of concurrent workers for batch processing
    CONCURRENT_CHUNK_SIZE: int = 10  # Size of chunks for concurrent processing

    # Redis caching configuration (shared cluster architecture)
    ENABLE_REDIS_CACHING: bool = True
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_NAMESPACE: str = "camel"  # Service-specific namespace prefix
    REDIS_TTL: int = 3600  # Cache TTL in seconds (1 hour)
    REDIS_CONNECTION_TIMEOUT: int = 5  # Connection timeout in seconds
    REDIS_MAX_CONNECTIONS: int = 10  # Maximum Redis connections in pool

    # Environment and cluster configuration
    ENVIRONMENT: str = "development"  # development, staging, production
    REDIS_CLUSTER_MODE: bool = False  # Enable for Redis cluster deployment
    REDIS_SENTINEL_HOSTS: Optional[str] = None  # Comma-separated sentinel hosts
    REDIS_MASTER_NAME: Optional[str] = None  # Redis master name for sentinel

    # Cross-service access configuration
    ENABLE_SHARED_CACHE: bool = True  # Enable shared namespace access
    ENABLE_CROSS_SERVICE_READ: bool = (
        True  # Enable reading other services' public cache
    )

    # Arabic text processing configuration
    DEFAULT_LANGUAGE: str = "ar"
    ENABLE_NORMALIZATION: bool = True
    ENABLE_STEMMING: bool = True
    ENABLE_ROOT_EXTRACTION: bool = True
    ENABLE_POS_TAGGING: bool = True
    # Note: Diacritics removal from stems and roots is now mandatory and always enabled

    # Text processing limits
    MAX_TEXT_LENGTH: int = 1000000  # 1MB of text
    MAX_BATCH_SIZE: int = 100  # Maximum number of texts to process in batch

    # Logging configuration
    LOG_LEVEL: str = "INFO"

    @field_validator("LOG_LEVEL")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Validate log level"""
        valid_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()


@lru_cache
def get_settings() -> Settings:
    """
    Get cached settings instance

    Returns:
        Settings: Cached settings instance
    """
    return Settings()
