"""
Main Arabic text processing service implementation

Orchestrates all text processing operations including normalization, tokenization,
and morphological analysis using dependency injection.
"""

import time
from typing import Any

from loguru import logger

from interfaces.caching import CacheManager
from interfaces.processing import (
    ArabicTextProcessingService,
    BatchProcessor,
    MorphologicalAnalyzer,
    MorphologyResult,
    ProcessingRequest,
    ProcessingResult,
    TextNormalizer,
    TextTokenizer,
)


class ArabicTextProcessingServiceImpl(ArabicTextProcessingService):
    """
    Main Arabic text processing service implementation

    Coordinates all text processing operations using injected dependencies
    for normalization, tokenization, morphological analysis, and caching.
    """

    def __init__(
        self,
        normalizer: TextNormalizer,
        tokenizer: TextTokenizer,
        analyzer: <PERSON><PERSON>hological<PERSON>nal<PERSON><PERSON>,
        batch_processor: BatchProcessor,
        cache_manager: CacheManager,
    ) -> None:
        """
        Initialize the text processing service

        Args:
            normalizer: Text normalization service
            tokenizer: Text tokenization service
            analyzer: Morphological analysis service
            batch_processor: Batch processing service
            cache_manager: Cache management service
        """
        self.normalizer = normalizer
        self.tokenizer = tokenizer
        self.analyzer = analyzer
        self.batch_processor = batch_processor
        self.cache_manager = cache_manager

    async def process_text(self, request: ProcessingRequest) -> ProcessingResult:
        """
        Process a single text with specified options

        Args:
            request: Processing request with text and options

        Returns:
            Processing result
        """
        start_time = time.time()

        try:
            if not request.text or not request.text.strip():
                return ProcessingResult(
                    success=False,
                    message="Empty text provided",
                    original_text=request.text,
                    processing_time=time.time() - start_time,
                )

            # Step 1: Normalize text if requested
            normalized_text = None
            if request.options.normalization:
                try:
                    normalized_text = self.normalizer.normalize(
                        request.text, request.options.normalization
                    )
                except Exception as e:
                    logger.error(f"Error in text normalization: {e}")
                    normalized_text = request.text
            else:
                normalized_text = request.text

            # Step 2: Tokenize text
            tokens = []
            try:
                tokens = self.tokenizer.tokenize(normalized_text)
            except Exception as e:
                logger.error(f"Error in tokenization: {e}")
                tokens = normalized_text.split()  # Fallback

            # Step 3: Perform morphological analysis if requested
            morphology_results = []
            if (
                request.options.morphology.enable_stemming
                or request.options.morphology.enable_root_extraction
            ):
                try:
                    morphology_results = await self.analyzer.analyze(
                        tokens, request.options.morphology
                    )
                except Exception as e:
                    logger.error(f"Error in morphological analysis: {e}")
                    # Create fallback results
                    morphology_results = [
                        MorphologyResult(word=token) for token in tokens
                    ]

            processing_time = time.time() - start_time

            return ProcessingResult(
                success=True,
                message="Text processed successfully",
                original_text=request.text,
                normalized_text=normalized_text,
                tokens=tokens,
                morphology_results=morphology_results,
                processing_time=processing_time,
                cache_hit=False,  # TODO: Implement cache checking
            )

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error processing text: {e}")

            return ProcessingResult(
                success=False,
                message=f"Processing failed: {e!s}",
                original_text=request.text,
                processing_time=processing_time,
            )

    async def process_batch(
        self, requests: list[ProcessingRequest]
    ) -> list[ProcessingResult]:
        """
        Process multiple texts in batch

        Args:
            requests: List of processing requests

        Returns:
            List of processing results
        """
        if not requests:
            return []

        try:
            # Use batch processor for concurrent processing
            results = await self.batch_processor.process_batch(
                requests, self.process_text
            )

            # Handle None results from failed processing
            processed_results = []
            for i, result in enumerate(results):
                if result is None:
                    # Create error result for failed processing
                    processed_results.append(
                        ProcessingResult(
                            success=False,
                            message="Processing failed due to system error",
                            original_text=requests[i].text if i < len(requests) else "",
                            processing_time=0.0,
                        )
                    )
                else:
                    processed_results.append(result)

            return processed_results

        except Exception as e:
            logger.error(f"Error in batch processing: {e}")

            # Return error results for all requests
            return [
                ProcessingResult(
                    success=False,
                    message=f"Batch processing failed: {e!s}",
                    original_text=req.text,
                    processing_time=0.0,
                )
                for req in requests
            ]

    def get_service_info(self) -> dict[str, Any]:
        """
        Get information about the service capabilities

        Returns:
            Service information dictionary
        """
        return {
            "service_name": "Camel-Tools API",
            "version": "2.0.0",
            "architecture": "Clean Architecture with Dependency Injection",
            "supported_languages": ["ar"],
            "supported_features": {
                "normalization": True,
                "tokenization": True,
                "stemming": True,
                "root_extraction": True,
                "batch_processing": True,
                "caching": True,
            },
            "components": {
                "normalizer": type(self.normalizer).__name__,
                "tokenizer": type(self.tokenizer).__name__,
                "analyzer": type(self.analyzer).__name__,
                "batch_processor": type(self.batch_processor).__name__,
                "cache_manager": type(self.cache_manager).__name__,
            },
        }
