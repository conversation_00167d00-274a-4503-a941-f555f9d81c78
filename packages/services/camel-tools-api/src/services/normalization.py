"""
Text normalization service implementation using Camel-Tools

Provides Arabic text normalization capabilities including diacritic removal,
character normalization, and enhanced text cleaning options.
"""

import re

from loguru import logger

from interfaces.processing import NormalizationOptions, TextNormalizer


# Import Camel-Tools components with fallback
try:
    from camel_tools.utils.dediac import dediac_ar
    from camel_tools.utils.normalize import (
        normalize_alef_ar,
        normalize_alef_maksura_ar,
        normalize_teh_marbuta_ar,
    )

    CAMEL_TOOLS_AVAILABLE = True
except ImportError:
    logger.warning("Camel-Tools not available, using fallback normalization")
    CAMEL_TOOLS_AVAILABLE = False


class CamelToolsNormalizer(TextNormalizer):
    """
    Text normalizer implementation using Camel-Tools

    Provides comprehensive Arabic text normalization with configurable options
    for different use cases and processing requirements.
    """

    def __init__(self) -> None:
        """
        Initialize the normalizer

        Note: This service currently doesn't require configuration.
        When configuration is needed in the future, specific parameters
        will be added to maintain clean dependency injection principles.
        """
        self.initialized = CAMEL_TOOLS_AVAILABLE
        if not self.initialized:
            logger.warning("CamelToolsNormalizer initialized without Camel-Tools")

    def normalize(self, text: str, options: NormalizationOptions) -> str:
        """
        Normalize Arabic text according to specified options

        Args:
            text: Input Arabic text
            options: Normalization configuration

        Returns:
            Normalized text
        """
        if not text or not text.strip():
            return text

        if self.initialized and CAMEL_TOOLS_AVAILABLE:
            normalized = self._normalize_with_camel_tools(text, options)
        else:
            normalized = self._normalize_fallback(text, options)

        # Apply additional normalization options (all mandatory operations)
        return self._apply_enhanced_normalization(normalized)

    def _normalize_with_camel_tools(
        self, text: str, options: NormalizationOptions
    ) -> str:
        """
        Normalize text using Camel-Tools

        Args:
            text: Input text
            options: Normalization options

        Returns:
            Normalized text using Camel-Tools
        """
        try:
            # Remove diacritics if requested
            normalized = str(dediac_ar(text)) if options.remove_diacritics else text

            # Apply character normalization
            normalized = str(normalize_alef_ar(normalized))
            normalized = str(normalize_alef_maksura_ar(normalized))

            return str(normalize_teh_marbuta_ar(normalized))

        except Exception as e:
            logger.error(f"Error in Camel-Tools normalization: {e}")
            return self._normalize_fallback(text, options)

    def _normalize_fallback(self, text: str, options: NormalizationOptions) -> str:
        """
        Fallback normalization without Camel-Tools

        Args:
            text: Input text
            options: Normalization options

        Returns:
            Normalized text using basic rules
        """
        normalized = text

        # Basic character normalization
        normalized = normalized.replace("أ", "ا").replace("إ", "ا").replace("آ", "ا")
        normalized = normalized.replace("ة", "ه")
        normalized = normalized.replace("ى", "ي")

        # Remove diacritics if requested
        if options.remove_diacritics:
            normalized = re.sub(r"[\u064B-\u0652]", "", normalized)

        return normalized

    def _apply_enhanced_normalization(self, text: str) -> str:
        """
        Apply enhanced normalization operations (all mandatory)

        Args:
            text: Input text

        Returns:
            Enhanced normalized text with mandatory operations applied
        """
        result = text

        # Remove punctuation (mandatory operation)
        punctuation_pattern = r'[،\.:\;!\?"\'\(\)\[\]\{\}\-_/\\|@#\$%\^&\*\+=<>~`؟؛]'
        result = re.sub(punctuation_pattern, "", result)

        # Normalize digits (Indian-Arabic to Arabic) (mandatory operation)
        digit_map = {
            "٠": "0",
            "١": "1",
            "٢": "2",
            "٣": "3",
            "٤": "4",
            "٥": "5",
            "٦": "6",
            "٧": "7",
            "٨": "8",
            "٩": "9",
        }
        for indian_digit, arabic_digit in digit_map.items():
            result = result.replace(indian_digit, arabic_digit)

        # Convert English letters to lowercase (mandatory operation)
        result = result.lower()

        # Normalize whitespace (mandatory operation)
        return re.sub(r"\s+", " ", result).strip()
