"""
Text tokenization service implementation using Camel-Tools

Provides Arabic text tokenization capabilities for breaking text into words.
"""

from loguru import logger

from interfaces.processing import TextTokenizer


# Import Camel-Tools components with fallback
try:
    from camel_tools.tokenizers.word import simple_word_tokenize

    CAMEL_TOOLS_AVAILABLE = True
except ImportError:
    logger.warning("Camel-Tools not available, using fallback tokenization")
    CAMEL_TOOLS_AVAILABLE = False


class CamelToolsTokenizer(TextTokenizer):
    """
    Text tokenizer implementation using Camel-Tools

    Provides word-level tokenization for Arabic text with fallback
    to simple whitespace tokenization when Camel-Tools is not available.
    """

    def __init__(self) -> None:
        """
        Initialize the tokenizer

        Note: This service currently doesn't require configuration.
        When configuration is needed in the future, specific parameters
        will be added to maintain clean dependency injection principles.
        """
        self.initialized = CAMEL_TOOLS_AVAILABLE
        if not self.initialized:
            logger.warning("CamelToolsTokenizer initialized without Camel-Tools")

    def tokenize(self, text: str) -> list[str]:
        """
        Tokenize text into individual words

        Args:
            text: Input text to tokenize

        Returns:
            List of tokens
        """
        if not text or not text.strip():
            return []

        if self.initialized and CAMEL_TOOLS_AVAILABLE:
            return self._tokenize_with_camel_tools(text)
        return self._tokenize_fallback(text)

    def _tokenize_with_camel_tools(self, text: str) -> list[str]:
        """
        Tokenize text using Camel-Tools

        Args:
            text: Input text

        Returns:
            List of tokens using Camel-Tools tokenizer
        """
        try:
            tokens = simple_word_tokenize(text)
            return list(tokens) if tokens else []
        except Exception as e:
            logger.error(f"Error in Camel-Tools tokenization: {e}")
            return self._tokenize_fallback(text)

    def _tokenize_fallback(self, text: str) -> list[str]:
        """
        Fallback tokenization using simple whitespace splitting

        Args:
            text: Input text

        Returns:
            List of tokens using whitespace splitting
        """
        return text.split()
