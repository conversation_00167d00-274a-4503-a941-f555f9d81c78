"""
Batch processing service implementation

Provides concurrent processing capabilities for handling multiple items efficiently.
"""

import asyncio
from typing import Any, Callable

from loguru import logger

from core.config import Settings
from interfaces.processing import BatchProcessor


class ConcurrentBatchProcessor(BatchProcessor):
    """
    Batch processor implementation with concurrent processing support

    Handles batch processing of items using asyncio for improved performance
    when processing multiple texts or operations.
    """

    def __init__(self, settings: Settings) -> None:
        """
        Initialize the batch processor with dependency injection

        Args:
            settings: Application configuration settings
        """
        self.settings = settings
        self.max_workers = self.settings.MAX_CONCURRENT_WORKERS
        self.chunk_size = self.settings.CONCURRENT_CHUNK_SIZE

    async def process_batch(
        self, items: list[Any], processor_func: Callable[[Any], Any]
    ) -> list[Any]:
        """
        Process a batch of items concurrently

        Args:
            items: List of items to process
            processor_func: Function to apply to each item

        Returns:
            List of processing results
        """
        if not items:
            return []

        if not self.settings.ENABLE_CONCURRENT_PROCESSING or len(items) == 1:
            # Process sequentially if concurrent processing is disabled or single item
            return await self._process_sequential(items, processor_func)

        try:
            return await self._process_concurrent(items, processor_func)
        except Exception as e:
            logger.error(f"Error in concurrent processing: {e}")
            logger.info("Falling back to sequential processing")
            return await self._process_sequential(items, processor_func)

    async def _process_concurrent(
        self, items: list[Any], processor_func: Callable[[Any], Any]
    ) -> list[Any]:
        """
        Process items concurrently using asyncio

        Args:
            items: List of items to process
            processor_func: Processing function

        Returns:
            List of processing results
        """
        # Create semaphore to limit concurrent operations
        semaphore = asyncio.Semaphore(self.max_workers)

        async def process_with_semaphore(item: Any) -> Any:
            """Process single item with semaphore control"""
            async with semaphore:
                try:
                    if asyncio.iscoroutinefunction(processor_func):
                        return await processor_func(item)
                    # Run sync function in thread pool
                    loop = asyncio.get_event_loop()
                    return await loop.run_in_executor(None, processor_func, item)
                except Exception as e:
                    logger.error(f"Error processing item: {e}")
                    return None

        # Process all items concurrently
        tasks = [process_with_semaphore(item) for item in items]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle exceptions in results
        processed_results: list[Any] = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Exception processing item {i}: {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)

        return processed_results

    async def _process_sequential(
        self, items: list[Any], processor_func: Callable[[Any], Any]
    ) -> list[Any]:
        """
        Process items sequentially

        Args:
            items: List of items to process
            processor_func: Processing function

        Returns:
            List of processing results
        """
        results: list[Any] = []

        for item in items:
            try:
                if asyncio.iscoroutinefunction(processor_func):
                    result = await processor_func(item)
                else:
                    result = processor_func(item)
                results.append(result)
            except Exception as e:
                logger.error(f"Error processing item: {e}")
                results.append(None)

        return results
