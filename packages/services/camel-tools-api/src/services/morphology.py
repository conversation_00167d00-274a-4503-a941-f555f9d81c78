"""
Morphological analysis service implementation using Camel-Tools

Provides Arabic morphological analysis including stemming and root extraction.
"""

from typing import Optional

from loguru import logger

from interfaces.processing import (
    MorphologicalAnalyzer,
    MorphologyOptions,
    MorphologyResult,
)


# Import Camel-Tools components with fallback
try:
    from camel_tools.disambig.mle import MLEDisambiguator
    from camel_tools.utils.dediac import dediac_ar

    CAMEL_TOOLS_AVAILABLE = True
except ImportError:
    logger.warning("Camel-Tools not available, morphological analysis disabled")
    CAMEL_TOOLS_AVAILABLE = False


class CamelToolsMorphologyAnalyzer(MorphologicalAnalyzer):
    """
    Morphological analyzer implementation using Camel-Tools

    Provides stemming and root extraction capabilities for Arabic text
    using the MLE disambiguator from Camel-Tools.
    """

    def __init__(self) -> None:
        """
        Initialize the morphological analyzer

        Note: This service currently doesn't require configuration.
        When configuration is needed in the future (e.g., custom model paths,
        analysis depth settings), specific parameters will be added to maintain
        clean dependency injection principles.
        """
        self.mle: Optional[MLEDisambiguator] = None
        self.initialized = False
        self.initialization_error: Optional[str] = None

        if CAMEL_TOOLS_AVAILABLE:
            self._initialize_camel_tools()

    def _initialize_camel_tools(self) -> None:
        """Initialize Camel-Tools MLE disambiguator"""
        try:
            logger.info("Initializing Camel-Tools MLE disambiguator...")
            self.mle = MLEDisambiguator.pretrained()
            self.initialized = True
            logger.success("Camel-Tools MLE disambiguator initialized successfully")
        except Exception as e:
            error_msg = f"Failed to initialize Camel-Tools MLE disambiguator: {e}"
            logger.error(error_msg)
            self.initialization_error = error_msg
            self.initialized = False

    async def analyze(
        self, tokens: list[str], options: MorphologyOptions
    ) -> list[MorphologyResult]:
        """
        Perform morphological analysis on tokens

        Args:
            tokens: List of tokens to analyze
            options: Analysis configuration

        Returns:
            List of morphological analysis results
        """
        if not tokens:
            return []

        if not self.initialized or not self.mle:
            logger.warning(
                "MLE disambiguator not initialized, returning fallback results"
            )
            return self._create_fallback_results(tokens, options)

        try:
            return await self._analyze_with_camel_tools(tokens, options)
        except Exception as e:
            logger.error(f"Error in morphological analysis: {e}")
            return self._create_fallback_results(tokens, options)

    async def _analyze_with_camel_tools(
        self, tokens: list[str], options: MorphologyOptions
    ) -> list[MorphologyResult]:
        """
        Perform analysis using Camel-Tools MLE disambiguator

        Args:
            tokens: List of tokens to analyze
            options: Analysis options

        Returns:
            List of morphological analysis results
        """
        results = []

        # Ensure MLE disambiguator is available (should be guaranteed by caller)
        if self.mle is None:
            raise RuntimeError("MLE disambiguator is not initialized")

        # Use MLE disambiguator for morphological analysis
        disambig = self.mle.disambiguate(tokens)

        for i, d in enumerate(disambig):
            word = tokens[i]
            stem = None
            root = None

            if d.analyses:
                analysis = d.analyses[0].analysis

                # Extract stem if requested
                if options.enable_stemming:
                    raw_stem = analysis.get("stem", word)
                    # Always remove diacritics from stems (mandatory behavior)
                    if CAMEL_TOOLS_AVAILABLE:
                        stem = str(dediac_ar(raw_stem))
                    else:
                        stem = str(raw_stem)

                # Extract root if requested
                if options.enable_root_extraction:
                    raw_root = analysis.get("root", "")
                    if raw_root:
                        # Always remove diacritics from roots (mandatory behavior)
                        if CAMEL_TOOLS_AVAILABLE:
                            root = str(dediac_ar(raw_root))
                        else:
                            root = str(raw_root)
            # Fallback for words without analysis
            elif options.enable_stemming:
                stem = word
                # Always remove diacritics from stems (mandatory behavior)
                if CAMEL_TOOLS_AVAILABLE:
                    stem = str(dediac_ar(word))

            results.append(MorphologyResult(word=word, stem=stem, root=root))

        return results

    def _create_fallback_results(
        self, tokens: list[str], options: MorphologyOptions
    ) -> list[MorphologyResult]:
        """
        Create fallback results when Camel-Tools is not available

        Args:
            tokens: List of tokens
            options: Analysis options

        Returns:
            List of fallback morphological results
        """
        results = []

        for token in tokens:
            stem = token if options.enable_stemming else None
            root = "" if options.enable_root_extraction else None

            results.append(MorphologyResult(word=token, stem=stem, root=root))

        return results
