{"name": "camel-tools-api", "version": "1.0.0", "description": "Camel-Tools Arabic Processing Service", "main": "src/main.py", "scripts": {"dev": "poetry run python src/main.py", "install-deps": "poetry install", "setup": "poetry install"}, "keywords": ["camel-tools", "arabic", "nlp", "text-processing", "morphological-analysis"], "author": "Arabic Learning Platform Team", "license": "MIT", "dependencies": {}, "devDependencies": {}}