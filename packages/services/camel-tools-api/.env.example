# Camel-Tools Arabic Processing Service Environment Configuration
# Copy this file to .env and modify as needed

# Service Configuration
# HOST=0.0.0.0
# PORT=8002
# DEBUG=true
# LOG_LEVEL=INFO

# Arabic Text Processing Configuration (optional overrides)
# DEFAULT_LANGUAGE=ar
# ENABLE_NORMALIZATION=true
# ENABLE_STEMMING=true
# ENABLE_ROOT_EXTRACTION=true
# ENABLE_POS_TAGGING=true
# Note: Diacritics removal from stems and roots is now mandatory and always enabled

# Processing Limits (optional overrides)
# MAX_TEXT_LENGTH=1000000
# MAX_BATCH_SIZE=100

# Performance Optimization Configuration (optional overrides)
# ENABLE_CONCURRENT_PROCESSING=true
# MAX_CONCURRENT_WORKERS=4
# CONCURRENT_CHUNK_SIZE=10

# Redis Caching Configuration - Shared Cluster Architecture (optional overrides)
# ENABLE_REDIS_CACHING=true
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_DB=0
# REDIS_PASSWORD=
# REDIS_NAMESPACE=camel
# REDIS_TTL=3600
# REDIS_CONNECTION_TIMEOUT=5
# REDIS_MAX_CONNECTIONS=10

# Environment and Cluster Configuration
# ENVIRONMENT=development
# REDIS_CLUSTER_MODE=false
# REDIS_SENTINEL_HOSTS=sentinel1:26379,sentinel2:26379,sentinel3:26379
# REDIS_MASTER_NAME=mymaster

# Cross-Service Access Configuration
# ENABLE_SHARED_CACHE=true
# ENABLE_CROSS_SERVICE_READ=true

# Legacy Caching Configuration (optional overrides)
# ENABLE_CACHING=true
# CACHE_SIZE=1000

# Note: Most configuration is handled in config.py with sensible defaults.
# Only add environment variables here for sensitive information or
# deployment-specific overrides.
