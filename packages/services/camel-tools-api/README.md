# Camel-Tools API Service

A comprehensive microservice for Arabic text processing using camel-tools, designed for the Arabic Learning Platform. This service implements clean architecture principles with dependency injection, providing robust Arabic NLP capabilities including morphological analysis, text normalization, and batch processing with Redis caching.

## 🚀 Features

### Core Arabic Processing Capabilities

- **Text Normalization**: Automatic punctuation removal, digit standardization, whitespace normalization, and English case normalization
- **Morphological Analysis**: Advanced Arabic morphological analysis with POS tagging
- **Stemming & Root Extraction**: Automatic diacritics removal from stems and roots
- **Tokenization**: Arabic-aware text tokenization
- **Batch Processing**: Concurrent processing of multiple texts with configurable workers

### Performance & Scalability

- **Concurrent Batch Processing**: Processes multiple texts concurrently using asyncio.gather()
- **Redis Word-Level Caching**: Caches MLE disambiguator results at token/word level for 50-80% performance improvement
- **Optimized Processing Pipeline**: Reduces redundant operations and improves efficiency
- **Graceful Degradation**: Continues working even if Redis cache is unavailable
- **Clean Architecture**: Dependency injection, interface segregation, and testable design

### Integration & Monitoring

- **RESTful API**: Clean FastAPI-based HTTP API with comprehensive documentation
- **Health Monitoring**: Built-in health checks and service information endpoints
- **Cache Management**: Redis cache statistics, health monitoring, and manual cache clearing
- **Cross-Service Integration**: Consumed by audio-processor and translation services

## 📦 Installation & Setup

### Prerequisites

- Python 3.9-3.12
- Poetry for dependency management
- Redis server (optional, for caching)

### Quick Start

```bash
# Navigate to service directory
cd packages/services/camel-tools-api

# Activate virtual environment
source .venv/bin/activate

# Install dependencies using Poetry
poetry install

# Start the service in development mode
poetry run python src/main.py

# Alternative: Use package script
pnpm dev
```

The service will start on `http://localhost:8002` by default.

### Redis Setup (Recommended)

For optimal performance, install and configure Redis:

```bash
# Install Redis (Ubuntu/Debian)
sudo apt-get install redis-server

# Start Redis service
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Test Redis connection
redis-cli ping
```

## ⚙️ Configuration

### Environment Variables

```bash
# Service Configuration
HOST=0.0.0.0
PORT=8002
DEBUG=true
LOG_LEVEL=INFO

# Performance Optimization
ENABLE_CONCURRENT_PROCESSING=true
MAX_CONCURRENT_WORKERS=4
CONCURRENT_CHUNK_SIZE=10

# Redis Caching (Shared Cluster Architecture)
ENABLE_REDIS_CACHING=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_NAMESPACE=camel
REDIS_TTL=3600
REDIS_CONNECTION_TIMEOUT=5
REDIS_MAX_CONNECTIONS=10

# Arabic Processing Defaults
DEFAULT_LANGUAGE=ar
ENABLE_NORMALIZATION=true
ENABLE_STEMMING=true
ENABLE_ROOT_EXTRACTION=true
ENABLE_POS_TAGGING=true

# Processing Limits
MAX_TEXT_LENGTH=1000000
MAX_BATCH_SIZE=100
```

### Configuration File

Create a `.env` file in the service root directory with your custom settings. The service uses Pydantic Settings for configuration management with validation.

## 🔌 API Reference

### Core Processing Endpoints

#### Process Single Text

```http
POST /process
Content-Type: application/json

{
    "text": "النص العربي للمعالجة",
    "enable_normalization": true,
    "enable_stemming": true,
    "enable_root_extraction": true
}
```

#### Process Multiple Texts (Batch)

```http
POST /process/batch
Content-Type: application/json

{
    "texts": ["النص الأول", "النص الثاني"],
    "enable_normalization": true,
    "enable_stemming": true,
    "enable_root_extraction": true
}
```

### Monitoring & Management Endpoints

- **GET /health**: Service health check with detailed status
- **GET /info**: Service information, capabilities, and Camel-Tools status
- **GET /cache/stats**: Redis cache statistics and performance metrics
- **GET /cache/health**: Cache connectivity and health status
- **POST /cache/clear**: Clear all cache entries (admin operation)

### Example Usage

```python
import requests

# Process single text with full analysis
response = requests.post("http://localhost:8002/process", json={
    "text": "الكتاب الجديد مفيد جداً للطلاب",
    "enable_normalization": True,
    "enable_stemming": True,
    "enable_root_extraction": True
})

result = response.json()
print(f"Processed text: {result['normalized_text']}")
print(f"Word count: {len(result['words'])}")

# Batch processing for better performance
batch_response = requests.post("http://localhost:8002/process/batch", json={
    "texts": [
        "النص الأول للمعالجة",
        "النص الثاني للتحليل",
        "النص الثالث للفهرسة"
    ],
    "enable_normalization": True,
    "enable_stemming": True,
    "enable_root_extraction": True
})

batch_results = batch_response.json()
print(f"Processed {len(batch_results['results'])} texts")
```

## 🛠️ Development Workflow

### Code Quality & Formatting

```bash
# Format code with Ruff (replaces Black + isort)
poetry run ruff format

# Lint code and check for issues
poetry run ruff check

# Fix auto-fixable issues
poetry run ruff check --fix

# Type checking with MyPy
poetry run mypy src

# Run all quality checks
poetry run ruff check && poetry run ruff format --check && poetry run mypy src
```

### Testing & Validation

```bash
# Test service startup and health
poetry run python src/main.py &
curl http://localhost:8002/health
curl http://localhost:8002/info

# Test API endpoints
curl -X POST http://localhost:8002/process \
  -H "Content-Type: application/json" \
  -d '{"text": "مرحبا بالعالم", "enable_normalization": true}'

# Test batch processing
curl -X POST http://localhost:8002/process/batch \
  -H "Content-Type: application/json" \
  -d '{"texts": ["النص الأول", "النص الثاني"], "enable_normalization": true}'
```

### Performance Monitoring

```bash
# Monitor cache performance
curl http://localhost:8002/cache/stats

# Check service capabilities
curl http://localhost:8002/info

# Monitor Redis directly
redis-cli info stats
redis-cli monitor
```

### Dependency Management

```bash
# Add runtime dependency
poetry add <package-name>

# Add development dependency
poetry add --group dev <package-name>

# Update dependencies
poetry update

# Show dependency tree
poetry show --tree

# Export requirements for deployment
poetry export -f requirements.txt --output requirements.txt
```

## 📊 Performance Metrics

### Expected Performance Benefits

With optimizations enabled, you can expect:

- **2-4x faster batch processing** through concurrent execution
- **50-80% faster repeated text processing** through Redis caching
- **Reduced memory usage** through optimized processing pipeline
- **Better scalability** for high-throughput scenarios

### Monitoring Endpoints

The service provides built-in monitoring for:

- **Single text processing functionality**: Response times and success rates
- **Batch processing with concurrency**: Throughput and worker utilization
- **Redis cache functionality**: Hit rates, miss rates, and connection health
- **Performance metrics**: Processing times, queue lengths, and error rates

## 🏗️ Architecture Overview

### Clean Architecture Implementation

This service implements clean architecture principles with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                        API Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Processing    │  │     Health      │  │     Cache       │ │
│  │    Routes       │  │    Routes       │  │    Routes       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     Adapter Layer                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              API Request/Response Adapters              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Business Logic Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Text Processing │  │   Morphology    │  │ Batch Processing│ │
│  │    Service      │  │   Analyzer      │  │    Service      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐                    │
│  │   Normalizer    │  │   Tokenizer     │                    │
│  └─────────────────┘  └─────────────────┘                    │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  Infrastructure Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Service Factory │  │  DI Container   │  │ Cache Manager   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Camel-Tools Integration                    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Architecture Principles

- **Dependency Inversion**: Core business logic depends on abstractions, not concrete implementations
- **Single Responsibility**: Each service class focuses on a single concern (normalization, tokenization, etc.)
- **Dependency Injection**: DI container manages service lifecycle and eliminates global state
- **Interface Segregation**: Clean interfaces define contracts between layers
- **Adapter Pattern**: API layer decoupled from business logic through adapters

### Core Components

#### 1. Interface Layer (`src/interfaces/`)

- **`processing.py`**: Core text processing abstractions
- **`caching.py`**: Cache management interfaces
- **`services.py`**: Service factory interfaces

#### 2. Service Layer (`src/services/`)

- **`normalization.py`**: Arabic text normalization implementation
- **`tokenization.py`**: Arabic-aware tokenization service
- **`morphology.py`**: Morphological analysis with Camel-Tools
- **`batch_processing.py`**: Concurrent batch processing service
- **`text_processing.py`**: Main orchestration service

#### 3. Infrastructure Layer (`src/infrastructure/`)

- **`factory.py`**: Service factory implementation with dependency injection
- **`container.py`**: Dependency injection container
- **`redis_cache_adapter.py`**: Redis cache adapter implementation

#### 4. Adapter Layer (`src/adapters/`)

- **`api_adapters.py`**: Request/response model conversion

#### 5. API Layer (`src/api/`)

- **`routes/processing.py`**: Text processing endpoints
- **`routes/health.py`**: Health check and service info
- **`routes/cache.py`**: Cache management endpoints
- **`dependencies.py`**: FastAPI dependency injection

#### 6. Core Layer (`src/core/`)

- **`config.py`**: Configuration management with Pydantic

#### 7. Models Layer (`src/models/`)

- **`schemas.py`**: Pydantic models for API requests/responses

## 📁 Project Structure

```
packages/services/camel-tools-api/
├── src/                                    # Source code directory
│   ├── __init__.py                        # Package initialization
│   ├── main.py                            # Application entry point
│   │
│   ├── interfaces/                        # Abstract interface definitions
│   │   ├── __init__.py                    # Interface exports
│   │   ├── processing.py                  # Text processing interfaces
│   │   ├── caching.py                     # Cache management interfaces
│   │   └── services.py                    # Service factory interfaces
│   │
│   ├── services/                          # Concrete service implementations
│   │   ├── __init__.py                    # Service exports
│   │   ├── normalization.py               # Text normalization service
│   │   ├── tokenization.py                # Arabic tokenization service
│   │   ├── morphology.py                  # Morphological analysis service
│   │   ├── batch_processing.py            # Concurrent batch processing
│   │   └── text_processing.py             # Main text processing orchestrator
│   │
│   ├── infrastructure/                    # Infrastructure layer
│   │   ├── __init__.py                    # Infrastructure exports
│   │   ├── factory.py                     # Service factory implementation
│   │   ├── container.py                   # Dependency injection container
│   │   └── redis_cache_adapter.py         # Redis cache adapter implementation
│   │
│   ├── adapters/                          # Adapter layer
│   │   ├── __init__.py                    # Adapter exports
│   │   └── api_adapters.py                # API request/response adapters
│   │
│   ├── api/                               # API layer
│   │   ├── __init__.py                    # API router registration
│   │   ├── dependencies.py                # FastAPI dependency injection
│   │   └── routes/                        # API route modules
│   │       ├── __init__.py                # Route exports
│   │       ├── processing.py              # Text processing endpoints
│   │       ├── health.py                  # Health check endpoints
│   │       └── cache.py                   # Cache management endpoints
│   │
│   ├── core/                              # Core configuration and utilities
│   │   ├── __init__.py                    # Core exports
│   │   └── config.py                      # Configuration management
│   │
│   └── models/                            # Data models
│       ├── __init__.py                    # Model exports
│       └── schemas.py                     # Pydantic request/response models
│
├── .vscode/                               # VSCode configuration
│   └── settings.json                      # Python interpreter and analysis settings
│
├── pyproject.toml                         # Poetry configuration and dependencies
├── poetry.lock                            # Locked dependency versions
├── package.json                           # NPM scripts for development
├── .gitignore                             # Git ignore patterns
└── README.md                              # This documentation
```

## 🔄 Code Execution Flow

### Application Startup Sequence

```mermaid
graph TD
    A[main.py] --> B[Load Settings]
    B --> C[Create FastAPI App]
    C --> D[Configure CORS Middleware]
    D --> E[Register API Routes]
    E --> F[Start Lifespan Manager]
    F --> G[Initialize Service Factory]
    G --> H[Create DI Container]
    H --> I[Register Services]
    I --> J[Initialize Camel-Tools]
    J --> K[Set Global DI Container]
    K --> L[Service Ready]
    L --> M[Handle API Requests]
```

### Request Processing Flow

```mermaid
graph TD
    A[HTTP Request] --> B[FastAPI Router]
    B --> C[Route Handler]
    C --> D[Get Service from DI]
    D --> E[API Adapter]
    E --> F[Business Logic Service]
    F --> G{Cache Available?}
    G -->|Yes| H[Check Redis Cache]
    G -->|No| I[Direct Processing]
    H --> J{Cache Hit?}
    J -->|Yes| K[Return Cached Result]
    J -->|No| I[Direct Processing]
    I --> L[Camel-Tools Processing]
    L --> M[Store in Cache]
    M --> N[Response Adapter]
    K --> N
    N --> O[HTTP Response]
```

### Service Dependency Graph

```mermaid
graph TD
    A[ArabicTextProcessingService] --> B[TextNormalizer]
    A --> C[TextTokenizer]
    A --> D[MorphologicalAnalyzer]
    A --> E[BatchProcessor]

    B --> F[CamelToolsNormalizer]
    C --> G[CamelToolsTokenizer]
    D --> H[CamelToolsMorphologyAnalyzer]
    E --> I[ConcurrentBatchProcessor]

    F --> J[Camel-Tools Library]
    G --> J
    H --> J
    H --> K[CacheManager]

    K --> L[Redis Cache]

    M[ServiceFactory] --> A
    M --> B
    M --> C
    M --> D
    M --> E

    N[DIContainer] --> M
```

## 🔗 Integration & Cross-Service Communication

### Service Integration Architecture

This microservice is consumed by other services in the monorepo via HTTP API calls, following a distributed microservices pattern:

```mermaid
graph TD
    A[Audio Processor Service] --> D[Camel-Tools API]
    B[Document Parser Service] --> D
    C[Translation Service] --> D

    D --> E[Redis Cache]
    D --> F[Camel-Tools Library]

    G[Frontend Application] --> A
    G --> B
    G --> C

    H[Backend API] --> A
    H --> B
    H --> C
    H --> D
```

### Integration Points

#### 1. Audio Processor Service

- **Purpose**: Chapter detection and Arabic text preprocessing
- **Endpoints Used**: `/process/batch`, `/process`
- **Use Case**: Processes transcribed Arabic text for chapter boundary detection
- **Data Flow**: Audio → Transcription → Text Normalization → Chapter Detection

#### 2. Document Parser Service

- **Purpose**: Document text preprocessing and analysis
- **Endpoints Used**: `/process`, `/process/batch`
- **Use Case**: Normalizes and analyzes Arabic text from parsed documents
- **Data Flow**: Document → Text Extraction → Arabic Processing → Structured Data

#### 3. Translation Service

- **Purpose**: Arabic text analysis for translation preparation
- **Endpoints Used**: `/process`, `/cache/stats`
- **Use Case**: Morphological analysis to improve translation quality
- **Data Flow**: Source Text → Morphological Analysis → Translation Engine

### API Contract Guarantees

- **Backward Compatibility**: API endpoints maintain stable contracts
- **Error Handling**: Consistent error response formats across all endpoints
- **Performance SLA**: Sub-second response times for single text processing
- **Availability**: Graceful degradation when Redis cache is unavailable

## 🚀 Deployment & Production

### Environment Setup

```bash
# Production environment variables
export ENVIRONMENT=production
export DEBUG=false
export LOG_LEVEL=WARNING

# Redis cluster configuration
export REDIS_CLUSTER_MODE=true
export REDIS_SENTINEL_HOSTS="sentinel1:26379,sentinel2:26379,sentinel3:26379"
export REDIS_MASTER_NAME="camel-tools-master"

# Performance tuning
export MAX_CONCURRENT_WORKERS=8
export CONCURRENT_CHUNK_SIZE=20
export REDIS_MAX_CONNECTIONS=20
```

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy dependency files
COPY pyproject.toml poetry.lock ./

# Install Poetry and dependencies
RUN pip install poetry && \
    poetry config virtualenvs.create false && \
    poetry install --only main

# Copy source code
COPY src/ ./src/

# Expose port
EXPOSE 8002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8002/health || exit 1

# Run application
CMD ["python", "src/main.py"]
```

### Monitoring & Observability

```bash
# Health monitoring
curl http://localhost:8002/health

# Performance metrics
curl http://localhost:8002/cache/stats
curl http://localhost:8002/info

# Log monitoring (structured JSON logs in production)
tail -f /var/log/camel-tools-api/app.log | jq '.'
```

**Service Status**: ✅ Production Ready
**Last Updated**: 2025-06-23
**Architecture Version**: Clean Architecture v2.0 with Dependency Injection
