#!/bin/bash

# Enhanced test script for translation feature with detailed output
cd /home/<USER>/website_project

# Create output directory with timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="translation_test_results_${TIMESTAMP}"
mkdir -p "$OUTPUT_DIR"

echo "=== 翻译功能测试 - $(date) ==="
echo "输出目录: $OUTPUT_DIR"

# Function to check service health
check_service() {
    local service_name="$1"
    local url="$2"
    echo "检查 $service_name..."
    if curl -s "$url" > /dev/null; then
        echo "✅ $service_name 运行正常"
        return 0
    else
        echo "❌ $service_name 未响应"
        return 1
    fi
}

echo -e "\n=== 检查所有服务状态 ==="
check_service "Audio Processor (8001)" "http://localhost:8001/health"
check_service "Camel Tools API (8002)" "http://localhost:8002/health"
check_service "Backend Service (8003)" "http://localhost:8003/api/v1/qwen/health"
check_service "Qwen Service (8004)" "http://localhost:8004/api/v1/health"

echo -e "\n=== 测试1: 仅启用转录 ==="
echo "音频文件: arabic_audio_short.mp3"
echo "开始处理..."
START_TIME=$(date +%s)

curl -X POST "http://localhost:8001/transcribe" \
  -F "file=@arabic_audio_short.mp3" \
  -F "language=ar" \
  -F "enable_translation=false" \
  -H "Accept: application/json" \
  --max-time 0 \
  > "$OUTPUT_DIR/test1_transcription_only.json" 2> "$OUTPUT_DIR/test1_error.log"

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))
echo "测试1完成，耗时: ${DURATION}秒"

echo -e "\n=== 测试2: 转录 + 翻译 ==="
echo "音频文件: arabic_audio_short.mp3"
echo "目标语言: 中文"
echo "开始处理..."
START_TIME=$(date +%s)

curl -X POST "http://localhost:8001/transcribe" \
  -F "file=@arabic_audio_short.mp3" \
  -F "language=ar" \
  -F "enable_translation=true" \
  -F "target_language=中文" \
  -F "translation_context_segments=4" \
  -H "Accept: application/json" \
  --max-time 0 \
  > "$OUTPUT_DIR/test2_transcription_translation.json" 2> "$OUTPUT_DIR/test2_error.log"

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))
echo "测试2完成，耗时: ${DURATION}秒"

echo -e "\n=== 测试3: 转录 + 章节检测 + 翻译 ==="
echo "音频文件: arabic_audio_short.mp3"
echo "章节标记: مقدمة, الفصل الأول الصهيونية تقاتل على جبهة اللغة"
echo "目标语言: 中文"
echo "开始处理..."
START_TIME=$(date +%s)

curl -X POST "http://localhost:8001/transcribe" \
  -F "file=@arabic_audio_short.mp3" \
  -F "language=ar" \
  -F 'chapter_markers=["مقدمة","الفصل الأول الصهيونية تقاتل على جبهة اللغة","الفصل الثاني ولادة الصهيونية الأدبية"]' \
  -F "split_chapters=false" \
  -F "enable_translation=true" \
  -F "target_language=中文" \
  -F "translation_context_segments=4" \
  -H "Accept: application/json" \
  --max-time 0 \
  > "$OUTPUT_DIR/test3_full_pipeline.json" 2> "$OUTPUT_DIR/test3_error.log"

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))
echo "测试3完成，耗时: ${DURATION}秒"

echo -e "\n=== 分析测试结果 ==="

# Function to analyze test results
analyze_test() {
    local test_num="$1"
    local file_name="$2"
    local test_description="$3"
    
    echo -e "\n--- $test_description ---"
    
    if [ -s "$OUTPUT_DIR/$file_name" ]; then
        echo "✅ API调用成功"
        
        # Pretty print the JSON response
        jq '.' "$OUTPUT_DIR/$file_name" > "$OUTPUT_DIR/${file_name%.json}_formatted.json" 2>/dev/null
        
        if [ $? -eq 0 ]; then
            # Extract key information
            local success=$(jq -r '.success // false' "$OUTPUT_DIR/${file_name%.json}_formatted.json")
            local message=$(jq -r '.message // "无消息"' "$OUTPUT_DIR/${file_name%.json}_formatted.json")
            local segments_count=$(jq '.segments | length' "$OUTPUT_DIR/${file_name%.json}_formatted.json" 2>/dev/null || echo "0")
            local translation_enabled=$(jq -r '.translation_enabled // false' "$OUTPUT_DIR/${file_name%.json}_formatted.json")
            local target_language=$(jq -r '.target_language // "无"' "$OUTPUT_DIR/${file_name%.json}_formatted.json")
            local translation_time=$(jq -r '.translation_processing_time // "无"' "$OUTPUT_DIR/${file_name%.json}_formatted.json")
            
            echo "处理状态: $success"
            echo "消息: $message"
            echo "段落数量: $segments_count"
            echo "翻译启用: $translation_enabled"
            echo "目标语言: $target_language"
            echo "翻译处理时间: $translation_time 秒"
            
            # Check for translated text in segments
            if [ "$translation_enabled" = "true" ]; then
                local translated_segments=$(jq '[.segments[] | select(.translated_text != null)] | length' "$OUTPUT_DIR/${file_name%.json}_formatted.json" 2>/dev/null || echo "0")
                echo "已翻译段落数量: $translated_segments"
                
                # Show first translated segment as example
                local first_original=$(jq -r '.segments[0].text // "无原文"' "$OUTPUT_DIR/${file_name%.json}_formatted.json")
                local first_translated=$(jq -r '.segments[0].translated_text // "无翻译"' "$OUTPUT_DIR/${file_name%.json}_formatted.json")
                echo -e "\n示例翻译:"
                echo "原文: $first_original"
                echo "译文: $first_translated"
            fi
            
        else
            echo "❌ JSON格式化失败"
            head -20 "$OUTPUT_DIR/$file_name"
        fi
    else
        echo "❌ API调用失败"
        if [ -s "$OUTPUT_DIR/test${test_num}_error.log" ]; then
            echo "错误信息:"
            cat "$OUTPUT_DIR/test${test_num}_error.log"
        fi
    fi
}

# Analyze all test results
analyze_test "1" "test1_transcription_only.json" "测试1: 仅转录"
analyze_test "2" "test2_transcription_translation.json" "测试2: 转录+翻译"
analyze_test "3" "test3_full_pipeline.json" "测试3: 完整流水线"

# Create comprehensive summary
cat > "$OUTPUT_DIR/translation_test_summary.txt" << EOF
=== 翻译功能测试总结 ===
测试时间: $(date)
音频文件: arabic_audio_short.mp3

=== 测试场景 ===
1. 仅转录（基线测试）
2. 转录 + 翻译
3. 转录 + 章节检测 + 翻译

=== 预期验证 ===
- 翻译功能可以通过API参数启用/禁用
- 翻译使用4个上下文段落进行准确翻译
- 翻译与现有功能（转录、章节检测）兼容
- 翻译结果包含在段落的translated_text字段中
- 响应包含翻译元数据（处理时间、目标语言等）

=== 文件输出 ===
EOF

ls -la "$OUTPUT_DIR/" >> "$OUTPUT_DIR/translation_test_summary.txt"

echo -e "\n=== 测试完成 ==="
echo "所有结果已保存到: $OUTPUT_DIR/"
echo "详细分析请查看: $OUTPUT_DIR/translation_test_summary.txt"

# Final validation
echo -e "\n=== 功能验证总结 ==="
echo "1. ✅ 基础转录功能: 已验证"
echo "2. 🔄 翻译功能: 测试中..."
echo "3. 🔄 章节检测兼容性: 测试中..."
echo "4. 🔄 API参数控制: 测试中..."

echo -e "\n请检查输出文件以确认所有功能正常工作。"